    <script>
    {{ with .Scratch.Get "repo_id" }}
      var base_url = '{{ (printf "%s" $.Site.BaseURL) | safeJS }}';
      var repo_id  = '{{ . | safeJS }}';
    {{ else }}
      var base_url = '';
      var repo_id  = '';
    {{ end }}
    </script>

    <script src="{{ "javascripts/application.js" | absURL }}"></script>
    {{ range .Site.Params.custom_js }}
    <script src="{{ . | absURL }}"></script>
    {{ end }}

    <script>
      {{ "/* Add headers to scrollspy */" | safeJS }}
      var headers   = document.getElementsByTagName("h2");
      var scrollspy = document.getElementById('scrollspy');

      if(scrollspy) {
        if(headers.length > 0) {
          for(var i = 0; i < headers.length; i++) {
            var li = document.createElement("li");
            li.setAttribute("class", "anchor");

            var a  = document.createElement("a");
            a.setAttribute("href", "#" + headers[i].id);
            a.setAttribute("title", headers[i].innerHTML);
            a.innerHTML = headers[i].innerHTML;

            li.appendChild(a)
            scrollspy.appendChild(li);
          }
        } else {
          scrollspy.parentElement.removeChild(scrollspy)
        }


        {{ "/* Add permanent link next to the headers */" | safeJS }}
        var headers = document.querySelectorAll("h1, h2, h3, h4, h5, h6");

        for(var i = 0; i < headers.length; i++) {
            var a = document.createElement("a");
            a.setAttribute("class", "headerlink");
            a.setAttribute("href", "#" + headers[i].id);
            a.setAttribute("title", "Permanent link")
            a.innerHTML = {{ or .Site.Params.permalink "¶" }};
            headers[i].appendChild(a);
        }
      }
    </script>

    {{ with .Site.GoogleAnalytics }}
      <script>
        (function(i,s,o,g,r,a,m){
          i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){(i[r].q=i[r].q||
          []).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
          m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;
          m.parentNode.insertBefore(a,m)
        })(window, document,
          'script', '//www.google-analytics.com/analytics.js', 'ga');
        /* General initialization */
        ga('create', '{{ . }}', 'auto');
        ga('set', 'anonymizeIp', true);
        ga('send', 'pageview');
        /* Track outbound links */
        var buttons = document.querySelectorAll('a');
        Array.prototype.map.call(buttons, function(item) {
          if (item.host != document.location.host) {
            item.addEventListener('click', function() {
              var action = item.getAttribute('data-action') || 'follow';
              ga('send', 'event', 'outbound', action, item.href);
            });
          }
        });
        /* Register handler to log search on blur */
        var query = document.querySelector('.query');
        query.addEventListener('blur', function() {
          if (this.value) {
            var path = document.location.pathname;
            ga('send', 'pageview', path + '?q=' + this.value);
          }
        });
      </script>
    {{ end }}

    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.8.0/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
  </body>
</html>
