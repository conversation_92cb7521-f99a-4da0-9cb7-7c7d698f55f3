/* 
 * overwrite the current primary color of the 
 * theme that is used as fallback in codeblocks 
 */
.article pre code {
	color: rgba(0, 0, 0, 0.78) !important;
}


/*	
	HIGHLIGHT.JS THEME 

	tweaked version of the Github theme
*/

.hljs {
display:block;
overflow-x:auto;
}

.hljs-comment,
.hljs-quote {
color:#998;
font-style:italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
color:#333;
font-weight:700;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
color:teal;
}

.hljs-string,
.hljs-doctag {
color:#d14;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
color:#900;
font-weight:700;
}

.hljs-subst {
font-weight:400;
}

.hljs-type,
.hljs-class .hljs-title {
color:#458;
font-weight:700;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
color:navy;
font-weight:400;
}

.hljs-regexp,
.hljs-link {
color:#009926;
}

.hljs-symbol,
.hljs-bullet {
color:#990073;
}

.hljs-built_in,
.hljs-builtin-name {
color:#0086b3;
}

.hljs-meta {
color:#999;
font-weight:700;
}

.hljs-deletion {
background:#fdd;
}

.hljs-addition {
background:#dfd;
}

.hljs-emphasis {
font-style:italic;
}

.hljs-strong {
font-weight:700;
}

/*
    OVERRIDING THE DEFAULT STYLES - By Mahmoud Zalt (<EMAIL>) for Laradock.io
*/


.project .logo img {
    max-width: 100%;
    height: auto;
    background: transparent;
    border-radius: 0%;
}

.project .banner {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
}