import * as Yup from 'yup';
import i18n from '../../i18n';
import './validators';
import { WALLET_TRANSFERS } from '../../constants';

const wizardWalletTransferSchema = Yup.object().shape({
  wallet: Yup.string().required(),
  walletId: Yup.string().when('wallet', value => {
    if (WALLET_TRANSFERS[value] === 'easypay') {
      return Yup.string()
        .easypayId()
        .required();
    }
    return Yup.string()
      .idramId()
      .required();
  }),
  isChecked: Yup.bool()
    .test('isChecked', i18n.t('validator.terms'), value => value === true)
    .required(i18n.t('validator.terms')),
});

export default wizardWalletTransferSchema;
