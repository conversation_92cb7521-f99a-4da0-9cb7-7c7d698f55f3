export const numberToDecimalString = (number, precision = 2) => {
  const formatter = new Intl.NumberFormat('en', {
    style: 'decimal',
    minimumFractionDigits: precision,
  });

  return isNaN(number) ? '' : formatter.format(number);
};

export const isNumeric = number => {
  const numberRegex = /^[0-9]*$/;

  return number.match(numberRegex);
};

export const roundUp = (value, multiplier) => {
  return Math.ceil(value / multiplier) * multiplier;
};

export const roundDown = (value, multiplier) => {
  return Math.floor(value / multiplier) * multiplier;
};

export const round = (value, multiplier) => {
  if (typeof value === 'string') {
    value = parseInt(value.replaceAll(',', ''));
  }

  return Math.round(value / multiplier) * multiplier;
};

export const percentage = (value, percentage) => {
  return (percentage * value) / 100;
};

export const mathRound = num => {
  return Math.round(num * 100) / 100;
};
