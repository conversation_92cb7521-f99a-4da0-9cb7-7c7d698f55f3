import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { Formik, Form } from 'formik';
import ErrorFocus from '../Validation/ErrorFocus';
import GCButton from '../GCButton';

import error_icon from '../../svgs/error_icon.svg';

import styles from './index.module.scss';

class IdentityVerificationFailure extends Component {
  handleSubmit = () => {
    this.props.history.push('/home');
  };

  render() {
    const { t } = this.props;

    return (
      <Formik onSubmit={this.handleSubmit}>
        {() => {
          return (
            <Form id={styles.verification_failure}>
              <div className={styles.verification_failure_form}>
                <div>
                  <img
                    src={error_icon}
                    className={styles.map_marker}
                    alt="error_icon"
                  />
                  <div className={styles.verification_failure_title}>
                    {t('identity_verification_failure.title')}
                  </div>
                </div>

                <div className="ui form">
                  <GCButton type="submit" className={styles.go_back_btn}>
                    {t('identity_verification_failure.go_back')}
                  </GCButton>
                </div>
              </div>
              <ErrorFocus />
            </Form>
          );
        }}
      </Formik>
    );
  }
}

export default compose(
  withRouter,
  withNamespaces('translations')
)(IdentityVerificationFailure);
