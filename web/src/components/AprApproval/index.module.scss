@import '../../styles/colors';
@import '../../styles/sizes';

#apr_approval {
  text-align: center;
  margin: 10px auto;
  width: 358px;

  .apr_approval_checkbox {
    margin-bottom: 20px;
    margin-top: 30px;
    text-align: left;

    label {
      padding-left: 35px;
    }

    label:before {
      border-radius: 10px;
      width: 20px;
      height: 20px;
    }

    label:after {
      border-radius: 10px;
      background-color: $main;
      color: $white;
      width: 20px;
      height: 20px;
      padding: 2px;
    }
  }

  .apr_approval_input {
    float: left;
    width: 120px;
    margin-right: 20px;
    font-size: 16px;
  }

  .apr_approval_input_text {
    width: 500px;
    max-width: 100%;
    text-align: left;
    color: $darky-gray;
  }
}

@media screen and (max-width: $lowres-tablet-width) {
  #apr_approval {
    .apr_approval_input {
      float: none;
    }

    .apr_approval_input_text {
      width: 100%;
      padding: 10px;
      text-align: center;
    }
  }
}

@media screen and (max-width: $mobile-width) {
  #apr_approval {
    width: 100%;
    padding: 0 30px;
  }
}
