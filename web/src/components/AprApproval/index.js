import React from 'react';
import { compose } from 'redux';
import { withNamespaces } from 'react-i18next';
import { connect } from 'formik';
import { Checkbox, Input } from 'semantic-ui-react';
import Validation from '../Validation';

import styles from './index.module.scss';

class AprApproval extends React.Component {
  aprApproval = (e, setFieldValue, actualApr) => {
    // Accept only integer or floating number with dot or comma
    const reg = new RegExp(/^(\d+|(\d+[.,]([\d]?)+))$/);

    if (e.target.value.length && !reg.test(e.target.value)) {
      return;
    }

    setFieldValue('apr', e.target.value);
    setFieldValue('actualApr', actualApr);
  };

  componentDidUpdate(prevProps) {
    const { formik, apr } = this.props;

    if (prevProps.apr && prevProps.apr !== apr) {
      formik.setFieldValue('apr', '');
    }
  }

  render() {
    const { formik, t, apr, name } = this.props;

    return (
      <div id={styles.apr_approval}>
        <Validation name={name} showMessage={false}>
          <Checkbox
            className={styles.apr_approval_checkbox}
            label={t('loan.steps.loan_amount.apr_approval_checkbox')}
            checked={formik.values[name]}
          />
        </Validation>
        {formik.values[name] && (
          <Validation name="apr" showMessage={true}>
            <Input
              type="text"
              value={formik.values.apr}
              className={styles.apr_approval_input}
              icon="percent"
              onChange={e => {
                this.aprApproval(e, formik.setFieldValue, apr);
              }}
            />
            <div className={styles.apr_approval_input_text}>
              {t('loan.steps.loan_amount.apr_approval_input', [apr])}
            </div>
          </Validation>
        )}
      </div>
    );
  }
}

AprApproval.defaultProps = {
  name: 'aprApprovalCheckbox',
};

export default compose(
  withNamespaces('translations'),
  connect
)(AprApproval);
