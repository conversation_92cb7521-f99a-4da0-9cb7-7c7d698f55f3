import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { with<PERSON>outer } from 'react-router-dom';
import { compose } from 'redux';
import { Segment } from 'semantic-ui-react';
import querystring from 'query-string';

import CreditWizard from '../CreditWizard';
import { isOASL, isOIQL } from '../../helpers/common';

import styles from './index.module.scss';

class LoanScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      activeStep: {},
    };
  }

  onChange = step => {
    const { history } = this.props;

    this.setState({
      activeStep: step,
    });

    if (this.isMissingQueryParams()) {
      history.push({
        pathname: '/',
      });
    }
  };

  isMissingQueryParams = () => {
    const { location } = this.props;
    const { pid, tid, rid, vid, address } = querystring.parse(location.search);

    // For IQOS loan pipe id query param is required
    if (isOIQL()) {
      return !pid;
    }

    // For Arpi Solar loan product, region, village, loanTerm and address query params is required
    if (isOASL()) {
      return !pid || !rid || !vid || !tid || !address;
    }
  };

  render() {
    const { t } = this.props;
    const { description } = this.state.activeStep;

    return (
      <Segment id={styles.loan_screen} vertical>
        <Segment className={styles.loan_header} vertical>
          <span className={styles.active_step_title}>{t(description)}</span>
        </Segment>
        <CreditWizard onChange={this.onChange} />
      </Segment>
    );
  }
}

export default compose(
  withRouter,
  withNamespaces('translations')
)(LoanScreen);
