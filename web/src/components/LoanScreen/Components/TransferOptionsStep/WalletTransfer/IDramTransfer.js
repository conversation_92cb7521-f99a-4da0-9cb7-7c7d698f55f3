import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { Input, Container, Checkbox } from 'semantic-ui-react';
import { connect } from 'formik';
import Validation from '../../../../Validation';
import { isNumeric } from '../../../../../helpers/numberHelpers';
import ErrorFocus from '../../../../Validation/ErrorFocus';

import IDram_logo from '../../../../../svgs/idram-logo.png';

import styles from './index.module.scss';

class IDramTransfer extends Component {
  handleChange = (e, setFieldValue) => {
    const value = e.target.value;

    if (value.length < 10 && isNumeric(value)) {
      setFieldValue('walletId', value);
    }
  };

  render() {
    const { t, formik } = this.props;

    return (
      <div className={styles.wallet_transfer}>
        <div className={styles.wallet_transfer_block}>
          <div className={styles.wallet_logo}>
            <img
              className={styles.idram_logo}
              src={IDram_logo}
              alt="idram-logo"
            />
          </div>

          <Container className={styles.wallet_container}>
            <div>
              <Validation name="walletId" showMessage={false}>
                <Input
                  type="tel"
                  placeholder={t('loan.steps.transfer_options.idram_wallet')}
                  onChange={e => {
                    this.handleChange(e, formik.setFieldValue);
                  }}
                  value={formik.values.walletId}
                />
              </Validation>
            </div>

            <div className={styles.confirmation_block}>
              <Validation name="isChecked" showMessage={false}>
                <Checkbox
                  className={styles.cardTransfer_confirmation_text}
                  label={t('loan.steps.transfer_options.confirmation_text')}
                  checked={formik.values.isChecked}
                />
              </Validation>
            </div>
          </Container>
        </div>

        <ErrorFocus />
      </div>
    );
  }
}

export default compose(
  withRouter,
  connect,
  withNamespaces('translations')
)(IDramTransfer);
