import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import _ from 'lodash';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { Tab, Menu, Dropdown, Icon } from 'semantic-ui-react';
import { Formik, Form } from 'formik';
import Navigation from '../../../../CreditWizard/Navigation';
import Validation from '../../../../Validation';
import TabItem from '../TabItem';
import EasypayTransfer from './EasypayTransfer';
import IDramTransfer from './IDramTransfer';
import ErrorFocus from '../../../../Validation/ErrorFocus';
import { storeTransferInfo } from '../../../../../redux/ducks/loan';

import wizardWalletTransferSchema from '../../../../../validation/schemas/wizardWalletTransferSchema';

import wallet_icon from '../../../../../svgs/wallet.svg';
import select_wallet from '../../../../../svgs/select_wallet.svg';

import { TRANSFER_METHODS } from '../../../../../constants';

import styles from './index.module.scss';

class WalletTransfer extends Component {
  handleSubmit = payload => {
    const {
      wizardBag: { goNext },
      storeTransferInfo,
    } = this.props;

    const method =
      payload.wallet === 1 ? TRANSFER_METHODS[4] : TRANSFER_METHODS[2];

    storeTransferInfo(
      {
        wallet_id: payload.walletId,
        route: method.route,
      },
      goNext
    );
  };

  composeTabs = () => {
    const { t, wizardBag, transferTypes } = this.props;

    const panes = [];
    const stateOptions = [];

    const types = _.chain(TRANSFER_METHODS)
      .filter(
        method =>
          method.type === 'wallet' &&
          transferTypes.find(type => type.name === method.method)
      )
      .value();

    const tabs = {
      idram_wallet: {
        title: 'loan.steps.transfer_options.idram',
        icon: wallet_icon,
        alt: 'wallet_icon',
        content: () => {
          return <IDramTransfer wizardBag={wizardBag} />;
        },
      },
      easypay_wallet: {
        title: 'loan.steps.transfer_options.easypay',
        icon: wallet_icon,
        alt: 'wallet_icon',
        content: () => {
          return <EasypayTransfer wizardBag={wizardBag} />;
        },
      },
    };

    _.forEach(types, (type, key) => {
      const tab = tabs[type.method];

      panes.push({
        menuItem: (
          <Menu.Item key={key}>
            <TabItem title={t(tab.title)} icon={tab.icon} />
          </Menu.Item>
        ),
        render: () => <Tab.Pane>{tab.content()}</Tab.Pane>,
      });

      stateOptions.push({
        text: (
          <div>
            <img
              src={tab.icon}
              alt={tab.alt}
              className={styles.wallet_container_img}
            />
            <div className={styles.transfer_text}>{t(tab.title)}</div>
          </div>
        ),
        value: key,
        key,
      });
    });

    return {
      panes,
      stateOptions,
    };
  };

  render() {
    const { t, wizardBag } = this.props;
    const { panes, stateOptions } = this.composeTabs();

    return (
      <Formik
        initialValues={{
          wallet: null,
          walletId: '',
          isChecked: false,
        }}
        onSubmit={this.handleSubmit}
        validationSchema={wizardWalletTransferSchema}
        validateOnBlur={true}
        validateOnChange={false}
      >
        {props => {
          const { values, setFieldValue, setFieldTouched } = props;

          return (
            <Form className={styles.wallet_transfer}>
              <div id={styles.wallet_container}>
                <div className={styles.wallet_header}>
                  {t('loan.steps.transfer_options.select_wallet')}
                </div>
                <Validation name="wallet" showMessage={false}>
                  <div className={styles.dropdown_container}>
                    <Dropdown
                      id="wallet"
                      icon={
                        <Icon
                          name="chevron down"
                          className={styles.transfer_methods_icon}
                        />
                      }
                      placeholder={t(
                        'loan.steps.transfer_options.dropdown_placeholder'
                      )}
                      {...values.wallet === null && {
                        text: t(
                          'loan.steps.transfer_options.dropdown_placeholder'
                        ),
                      }}
                      selectOnNavigation={false}
                      selectOnBlur={false}
                      selection
                      options={stateOptions}
                      className={styles.wallet_transfer_methods}
                      onChange={(event, data) => {
                        setFieldValue('wallet', data.value);
                        setFieldTouched('walletId', false, false);
                        setFieldTouched('isChecked', false, false);
                      }}
                      value={values.wallet}
                    />
                  </div>
                </Validation>
                {values.wallet === null && (
                  <div className={styles.select_wallet_logo}>
                    <img src={select_wallet} alt="select_wallet" />
                  </div>
                )}
                <Tab
                  menu={{ text: true }}
                  panes={panes}
                  className={styles.wallet_transfer_menu}
                  activeIndex={values.wallet}
                  onTabChange={this.handleTabChange}
                  value={values.wallet}
                />
              </div>

              <Navigation wizardBag={wizardBag} />

              <ErrorFocus />
            </Form>
          );
        }}
      </Formik>
    );
  }
}

WalletTransfer.propTypes = {
  wizardBag: PropTypes.object,
};

const mapDispatchToProps = dispatch => {
  return {
    storeTransferInfo: (data, cb) => dispatch(storeTransferInfo(data, cb)),
  };
};

export default compose(
  withNamespaces('translations'),
  connect(
    null,
    mapDispatchToProps
  )
)(WalletTransfer);
