@import '../../../../../styles/colors';
@import '../../../../../styles/sizes';

.vehicle_section {
  text-align: center;
  padding: 20px 20px 10px 20px;
  border-top: 1px solid $lighty-gray;

  .vehicle_pledge_text {
    max-width: 391px;
    margin: 0 auto;
    text-align: center;
    margin-bottom: 20px;
  }

  .available_amount {
    font-size: 30px;
    font-weight: bold;
    color: $light-green;
    margin: 20px;

    svg {
      height: 22px;
      width: 20px;
      fill: $light-green;
    }
  }

  .loan_only_trade {
    text-align: center;
    padding-bottom: 5px;
  }

  .vehicle_pledge_section {
    max-width: 466px;
    margin: 0 auto;
    padding: 0;
    text-align: left;

    .vehicle_container_disabled {
      opacity: 0.4;
    }

    .vehicle_container {
      max-width: 100%;
      margin: 10px auto;
      border: 1px solid $gray;
      border-radius: 6px;
      text-align: left;
      background-color: $white;

      .vehicle_info_error {
        text-align: center;
        padding-top: 12px;
      }

      .vehicle_info_section {
        display: flex;
        position: relative;
        padding: 12px 10px 10px;
        font-family: 'DejaVu Sans Book';

        .vehicle_radio {
          width: 24px;
          height: 24px;
          top: 4px;
          position: unset;

          label {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;

            &::before,
            &::after {
              top: 50%;
              transform: translateY(-50%);
            }

            &:before {
              left: 10px;
              width: 20px;
              height: 20px;
              border-color: $main;
            }

            &:after {
              left: 15px;
              width: 10px;
              height: 10px;
              background-color: $white !important;
            }
          }
        }

        :global(.checked) {
          label:before {
            background-color: $main !important;
          }
        }

        .vehicle_flag_label {
          font-size: 13px;
          margin-left: 24px;
          font-weight: bold;
          margin-top: 3px;

          .flag {
            .flag_red {
              width: 20px;
              height: 5.21px;
              background: $red;
              border-radius: 0;
            }

            .flag_blue {
              width: 20px;
              height: 5.21px;
              background: $blue;
            }

            .flag_orange {
              width: 20px;
              height: 5.21px;
              background: $web-orange;
            }
          }
        }

        .seperator {
          width: 2px;
          margin-top: 3px;
          height: 30px;
          margin-left: 16px;
          background: $light-gray;
        }

        .vehicle_info_container {
          width: -webkit-fill-available;
          display: flex;
          flex-direction: column;
          padding-left: 10px;

          .vehicle_number {
            font-size: 24px;
            font-weight: bold;
            float: left;
          }

          .vehicle_details {
            font-size: 16px;
          }
        }

        .tech_passport_container,
        .vehicle_number_container {
          margin: 0 auto;
        }

        .trade_amount_container {
          margin: 0 auto;

          .currency {
            display: flex;
            flex-direction: column;

            input {
              width: 220px;
              font-size: 25px;
              height: 36px;
              border: none !important;
              font-family: 'DejaVu Sans Book';
              padding-left: 30px;
              padding-right: 5px;
            }

            input:focus {
              outline: none;
              border: none;
            }

            .dram_icon {
              width: 20px;
              height: 23px;
              fill: $main;
              margin-top: 3px;
              margin-left: 5px;
            }

            .currency_input {
              width: 265px;

              div:first-child {
                width: 45px;
                padding: 0 12px 0 0;
                border: none;
                position: relative;
              }
            }

            .vehicle_number_input_label {
              display: inline-flex;
              margin-left: 12px;
              height: 36px;

              .label_container {
                padding-top: 2.5px;
                padding-bottom: 7.5px;
              }

              .vehicle_number_input_label_flag {
                margin: 5px 0 0 0;
              }

              .vehicle_number_input_label_text {
                margin-top: 3px;
              }

              .vehicle_input_label_seperator {
                margin-top: 3px;
                height: 30px;
                margin-left: 8px;
                border-left: 2px solid $light-gray;
              }
            }

            .currency_text {
              display: block;
              font-size: 12px;
              font-style: italic;
              margin-left: 10px;
            }
          }
        }
      }
    }

    .new_vehicle {
      margin: 0;

      .info_text {
        text-align: center;
        font-size: 16px;
        opacity: 0.4;
      }

      .active_text {
        opacity: 1;
      }

      .vehicle_buttons {
        display: flex;
        justify-content: space-between;

        .button {
          width: 100%;
          padding: 11px 0;
          background-color: $white;
          border: 1px solid $main;
          border-radius: 6px;
          margin: 0 auto;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: center;

          p {
            margin: 0;
            width: 100%;
          }

          .button_title {
            color: $main;
            font-size: 14px;
            font-weight: 100;
          }

          .button_description {
            color: $dark-gray;
            font-size: 12px;
            font-weight: 100;
          }
        }
      }
    }
  }
  @media screen and (max-width: $lowres-tablet-width) {
    .vehicle_pledge_section {
      .vehicle_container {
        .vehicle_amount_section {
          .vehicle_amount {
            font-size: 20px;

            svg {
              height: 15px;
              width: 15px;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: $mobile-width) {
    .vehicle_pledge_section {
      .vehicle_container {
        .vehicle_info_section {
          .vehicle_flag_label {
            margin-left: 20px;
          }

          .seperator {
            margin-left: 10px;
          }

          .vehicle_info_container {
            .vehicle_number {
              font-size: 20px;
            }

            .vehicle_details {
              font-size: 12px;
            }
          }

          .trade_amount_container {
            .currency {
              input {
                font-size: 16px;
              }
            }
          }
        }

        .vehicle_amount_section {
          padding: 10px 0 5px 0px;
          line-height: 1.5;
        }
      }
    }
  }
}
