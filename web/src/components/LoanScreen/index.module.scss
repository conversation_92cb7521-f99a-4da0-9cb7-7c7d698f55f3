@import '../../styles/colors';
@import '../../styles/sizes';

#loan_screen {
  padding: 0;

  .loan_header {
    background-image: url('../../svgs/loan-screen-header.svg');
    background-repeat: no-repeat;
    background-size: cover;
    height: 107px;
    padding: 0;
    display: flex;
    align-items: center;

    .active_step_title {
      line-height: 40px;
      font-size: 36px;
      color: $secondary-text-color;
      margin-left: 45px;
    }
  }

  @media screen and (max-width: $mobile-width) {
    .loan_header {
      .active_step_title {
        font-size: 24px;
        margin-left: 30px;
      }
    }
  }
}
