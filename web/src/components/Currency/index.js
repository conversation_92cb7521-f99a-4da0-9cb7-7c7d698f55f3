import React, { Component } from 'react';
import { numberToDecimalString } from '../../helpers/numberHelpers';
import { ReactComponent as DramIcon } from '../../svgs/amd.svg';
import classnames from 'classnames';
import styles from './index.module.scss';

export default class Currency extends Component {
  render() {
    const { hasSign, value, className, precision } = this.props;

    return (
      <div className={classnames(className, styles.currency)}>
        {hasSign && <DramIcon className={styles.dram_icon} />}
        <span className={styles.currency_price}>
          {isNaN(value) ? value : numberToDecimalString(value, precision)}
        </span>
      </div>
    );
  }
}
