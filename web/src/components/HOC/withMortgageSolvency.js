import React from 'react';

import { REML_SOLVENCY_BOUNDARY_VALUES } from '../../constants';

export const withMortgageSolvency = WrappedComponent => {
  return class extends React.Component {
    getSalaryFreePartPercentage = (loanAmount, realEstatePrice) => {
      if (
        realEstatePrice > REML_SOLVENCY_BOUNDARY_VALUES.REAL_ESTATE_PRICE ||
        loanAmount > REML_SOLVENCY_BOUNDARY_VALUES.MORTGAGE_AMOUNT
      ) {
        return REML_SOLVENCY_BOUNDARY_VALUES.NET_SALARY_PERCENTAGE_60;
      }

      return REML_SOLVENCY_BOUNDARY_VALUES.NET_SALARY_PERCENTAGE_45;
    };

    calculateSolvency = (
      realEstatePrice,
      loanAmount,
      netSalary,
      acraLoansMonthlyRepayments
    ) => {
      const salaryFreePartPercentage = this.getSalaryFreePartPercentage(
        loanAmount,
        realEstatePrice
      );

      let solvency =
        netSalary * salaryFreePartPercentage - acraLoansMonthlyRepayments;

      if (!solvency || solvency < 0) {
        solvency = 0;
      }

      return solvency;
    };

    render() {
      return (
        <WrappedComponent
          {...this.props}
          calculateSolvencyHOC={this.calculateSolvency}
          getSalaryFreePartPercentageHOC={this.getSalaryFreePartPercentage}
        />
      );
    }
  };
};
