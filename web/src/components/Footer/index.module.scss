@import '../../styles/colors';
@import '../../styles/sizes';

#footer {
  padding: 0;
  flex-shrink: 0;

  .footer__body {
    background-color: $main;
    padding: 37px 0 18px 0;
    width: 100%;
    margin-left: 0;
    margin-right: 0;

    .grid {
      margin-left: 0;
      margin-right: 0;

      .grid_row {
        padding-top: 0;
        width: fit-content;

        .grid_column {
          box-shadow: none;
          margin: 0 auto;
          width: auto;

          .text,
          .text_header {
            font-size: 10px;
          }

          .text_header {
            margin-top: 1em;
            color: $white;
          }

          .text {
            line-height: inherit;
            color: $lighty-gray;
          }

          a {
            color: $lighty-gray;
          }

          .social {
            margin-top: 10px;
            float: right;
            width: fit-content;

            .social_icon {
              font-size: 23px;
              color: $white;
            }
          }

          .grid_column__links {
            display: flex;
            flex-direction: column;
            margin-top: 60px;

            .grid_column__link {
              text-align: right;
              line-height: inherit;
              color: $white;
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  .rights {
    width: 100%;
    padding: 10px 18px;
    background-color: $secondary;

    .rights_text {
      color: $white;
      font-size: 10px;
      margin: auto;
    }

    .terms_and_conditions {
      font-size: 11px;
      color: $white;

      .terms_and_conditions_link {
        color: white;
      }
    }
  }

  @media screen and (max-width: $tablet-width) {
    .footer__body {
      padding-top: 0;

      .grid {
        display: block;

        .grid_row {
          flex-direction: column;
          text-align: center;
          margin: auto;

          .grid_column {
            margin-top: 20px;

            .social {
              margin-top: 0;
              float: none;
              width: initial;
            }

            .grid_column__links {
              margin-top: 20px;

              .grid_column__link {
                text-align: center;
              }
            }
          }
        }
      }
    }

    .rights {
      text-align: center;
    }
  }
}
