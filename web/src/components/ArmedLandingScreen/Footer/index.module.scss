@import '../../../styles/colors';
@import '../../../styles/sizes';

#footer_container {
  background-color: $armed_blue;
  height: 60px;
  display: flex;
  justify-content: space-between;
  padding: 16px 40px;
  color: $gray-blue;

  .left_section {
    svg {
      height: 28px;
      width: 101px;
      fill: $gray-blue;
    }
  }

  .right_section {
    display: inherit;
    align-items: center;

    .social {
      margin-top: 5px;
      display: flex;

      .social_icon {
        font-size: 22px;
        color: $gray-blue;
      }
    }

    .rights {
      margin-left: 8px;
      font-size: 10px;
      line-height: 15px;

      a {
        font-size: 10px;
        color: $gray-blue;
      }
    }
  }

  @media screen and (max-width: $lowres-tablet-width) {
    padding: 16px 8px;
  }

  @media screen and (orientation: portrait) and (max-width: $mobile-width) {
    height: 135px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: unset;

    .right_section {
      flex-direction: column;

      .rights {
        margin-left: unset;
        text-align: center;

        a {
          text-decoration: underline;
        }

        & > div:first-child {
          margin: 5px 0;
        }
      }
    }
  }
}
