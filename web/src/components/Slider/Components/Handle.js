import React from 'react';
import PropTypes from 'prop-types';

import styles from '../index.module.scss';

export function Handle({
  allowedMax,
  domain: [min, max],
  handle: { id, value, percent },
  getHandleProps,
  realMax,
}) {
  let railPercent = Math.floor(percent);
  const railMaxPercent = Math.floor(
    (allowedMax - min) / ((realMax - min) / 100)
  );

  if (allowedMax && value > allowedMax) {
    railPercent = railMaxPercent;
  }

  if (allowedMax === min) {
    railPercent = 0;
  }

  if (min === max) {
    railPercent = 100;
  }

  return (
    <div
      className={styles.slider__handle}
      aria-valuemin={min}
      aria-valuemax={realMax}
      aria-valuenow={value}
      style={{
        left: `${railPercent}%`,
      }}
      {...getHandleProps(id)}
    >
      <div className={styles.slider_ball} />
    </div>
  );
}

Handle.propTypes = {
  divOrButton: PropTypes.oneOf(['div', 'button']).isRequired,
  domain: PropTypes.array.isRequired,
  handle: PropTypes.shape({
    id: PropTypes.string.isRequired,
    value: PropTypes.number.isRequired,
    percent: PropTypes.number.isRequired,
  }).isRequired,
  getHandleProps: PropTypes.func.isRequired,
};

Handle.defaultProps = {
  divOrButton: 'div',
};
