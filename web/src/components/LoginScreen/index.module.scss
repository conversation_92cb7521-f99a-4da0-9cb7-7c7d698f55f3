@import '../../styles/colors';

#login {
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid $argent-gray;
  width: 500px;
  padding: 40px;
  background: $cloud-white 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 30px $black-with-opacity;
  border-radius: 39px;

  .logo_header {
    text-align: center;
    margin-bottom: 45px;

    .logo {
      height: 42px;
    }
  }

  :global(.ui.form .field input) {
    height: 42px;
    border-radius: 8px;
  }

  .login_btn {
    margin-top: 40px;
    text-align: center;

    button {
      background: $main;
    }
  }
}
