import React from 'react';
import { Link } from 'react-router-dom';
import { compose } from 'redux';
import { withNamespaces } from 'react-i18next';
import { Icon } from 'semantic-ui-react';

import Logo from '../../../Logo';

import styles from './index.module.scss';

const Footer = props => {
  const { t } = props;

  return (
    <div className={styles.luyser_footer}>
      <div className={styles.logo}>
        <Logo />
      </div>

      <div className={styles.info}>
        <div className={styles.socials}>
          <a
            target="_blank"
            rel="noopener noreferrer"
            href="https://www.facebook.com/GlobalCreditUCO/"
          >
            <Icon name="facebook square" className={styles.icon} />
          </a>
          <a
            target="_blank"
            rel="noopener noreferrer"
            href="https://www.instagram.com/__globalcredit__/"
          >
            <Icon name="instagram" className={styles.icon} />
          </a>
        </div>

        <div className={styles.terms}>
          <div>
            <Link
              to="/terms-and-conditions"
              target="_blank"
              rel="noopener noreferrer"
            >
              {t('footer.terms_and_conditions')}
            </Link>
          </div>
          <div>{t('footer.rights')}</div>
        </div>
      </div>
    </div>
  );
};

export default compose(withNamespaces('translations'))(Footer);
