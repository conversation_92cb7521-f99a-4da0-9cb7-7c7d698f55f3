@import '../../../../styles/colors';
@import '../../../../styles/sizes';

.luyser_calculator_form {
  width: 350px;
  max-width: 100%;
  margin-left: 10px;

  > .title {
    padding-bottom: 20px;

    h2 {
      font-size: 16px;
      letter-spacing: 0.5px;
      font-family: 'DejaVu Sans Book';
    }
  }

  .apartment_info {
    display: flex;
    justify-content: space-between;

    .field {
      width: calc(100% / 2 - 10px);
    }
  }

  .field {
    display: flex;
    flex-direction: column;
    padding-bottom: 15px;
    width: 100%;

    label {
      font-size: 14px;
      margin-bottom: 5px;
    }

    &.global_fild {
      position: relative;

      :global(.ui.input) input {
        padding-left: 40px;
      }

      .global_icon {
        position: absolute;
        bottom: 25px;
        left: 10px;
      }
    }

    &.prepayment {
      label {
        display: flex;
        justify-content: space-between;

        span:last-child {
          font-size: 10px;
          margin-left: 10px;
        }
      }
    }

    :global(.ui.input) {
      input {
        font-weight: bold;
        padding: 10px;
      }

      &:global(.disabled) input {
        background-color: $luyser-light-blue;
      }
    }
  }

  .monthly_payment,
  .expected_net_salary {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;

    .title {
      font: normal normal normal 18px/22px Arial;
    }

    .amount {
      font: normal normal normal 20px/24px Arial;
      color: $lighter-blue;
      font-weight: 600;
    }
  }

  .expected_net_salary {
    .title {
      font-size: 14px;
      max-width: 170px;
    }

    .amount {
      font-size: 14px;
      color: white;
      white-space: nowrap;

      span:last-child {
        font-size: 9px;
        vertical-align: top;
        margin-left: 3px;
      }
    }
  }

  .annual_rate {
    label {
      margin-bottom: 8px;
    }

    svg {
      width: 100%;
    }
  }

  .calculate_btn {
    display: flex;
    justify-content: center;
    padding-top: 35px;

    :global(.ui.button.gc_primary) {
      width: 100%;
      max-width: 100%;
      padding: 15px;
      height: 50px;
      background-color: $lighter-blue;
      font: normal normal normal 16px/18px Arial;
    }
  }

  @media screen and (max-width: $lowres-tablet-width) {
    max-width: 350px;
    min-width: 240px;
    width: 100%;

    .apartment_info {
      span {
        word-spacing: 60px;
      }

      .field {
        width: calc(60% - 10px);

        &:first-child {
          width: calc(40% - 10px);
        }
      }
    }
  }

  @media (orientation: landscape) {
    @media screen and (max-height: 600px) {
      margin-left: 20px;
    }
  }

  @media (orientation: portrait) {
    @media screen and (max-width: $mobile-width) {
      margin: 15px auto;

      .apartment_info {
        span {
          word-spacing: unset;
        }

        .field {
          width: calc(54% - 6px);

          &:first-child {
            width: calc(46% - 6px);
          }
        }
      }
    }
  }

  @media screen and (max-width: 350px) {
    .apartment_info {
      span {
        word-spacing: 60px;
      }
    }
  }
}
