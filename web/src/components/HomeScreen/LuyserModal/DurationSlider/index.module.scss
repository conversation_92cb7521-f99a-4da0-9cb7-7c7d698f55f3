@import '../../../../styles/colors';
@import '../../../../styles/sizes';

#slider {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;

  .slider__wrapper_container {
    display: flex;
    flex-direction: row;
    color: $secondary;
    margin: 0 5px;

    .price {
      font-size: 16px;
      margin: 0 1em;
      min-width: 115px;

      & > div {
        color: $secondary;
        font-size: 16px;
        margin-left: 3px;
        font-family: Arial;

        svg {
          fill: $secondary;
          margin: 0 5px;
          height: 12px;
        }
      }
    }
  }

  .slider__container {
    position: relative;
    width: 100%;
    height: 30px;

    .slider__rail,
    .slider__white_rail {
      position: absolute;
      width: 100%;
      cursor: pointer;
      overflow: hidden;
      height: 12px;
      margin-top: 1px;
      z-index: 2;
    }

    .slider__handle {
      position: absolute;
      margin-left: -10px;
      margin-top: -7px;
      z-index: 9;
      cursor: pointer;
    }

    .slider_ball {
      width: 20px;
      height: 20px;
      border: 3px solid #153a57;
      background-color: $lighter-blue;
      border-radius: 50%;
    }

    .slider__handle:before {
      position: relative;
      top: 4px;
    }

    .slider__track {
      position: absolute;
      left: 0;
      z-index: 7;
      margin-left: -3px;
      background-color: $lighter-blue;
      border-radius: 5px;
      cursor: pointer;
      height: 5px;
    }

    .slider__track_available {
      pointer-events: none;
      position: absolute;
      left: 0;
      z-index: 6;
      background-color: $smoke-gray;
      cursor: pointer;
      height: 5px;
      border-radius: 30px;
    }
  }

  .label_value {
    display: flex;
    justify-content: space-between;
    margin-bottom: 13px;

    div {
      span:first-child {
        font-weight: bold;
        margin-right: 3px;
      }
    }
  }

  .slider_wrapper_value,
  .slider__value {
    margin-bottom: 13px;

    & > div {
      span {
        font-family: Arial;
        font-weight: bold;
        font-size: 25px;
        color: $main;
      }
    }
  }

  .slider__value {
    & > div {
      svg {
        height: 18px;
      }
    }
  }

  .slider_wrapper_value_max {
    width: 100%;
    padding-top: 30px;
    text-align: right;
    justify-content: space-between;

    .amount_value {
      font-size: 14px;

      .amount_year {
        font-size: 8px;
        line-height: 12px;
      }
    }
  }

  .slider_wrapper_values {
    width: 100%;
    padding-top: 30px;
    display: flex;
    justify-content: space-between;

    .amount_value {
      display: flex;
      align-items: flex-start;
      font-size: 14px;

      .amount_year {
        font-size: 8px;
        line-height: 12px;
      }
    }
  }
}

@media screen and (max-width: $lowres-tablet-width) {
  #slider {
    position: relative;

    .slider__wrapper_container {
      margin: 0 5px;

      .price {
        font-size: 10px;
        position: absolute;
        top: 0;
        margin: 0;
      }
    }

    .slider__container {
      width: 100%;
      height: 20px;

      .border_flag {
        display: none;
      }

      .slider__rail {
        height: 12px;
        margin-top: 1px;
      }

      .slider__handle:before {
        top: 7px;
      }

      .slider__track {
        height: 5px;
      }

      .toggle_amount {
        display: none;
      }
    }

    .slider_wrapper_value {
      margin-bottom: 25px;

      & > div {
        color: $secondary;
        font-size: 25px;
        margin-bottom: 20px;
      }
    }

    .slider__value,
    .slider_wrapper_value {
      margin-bottom: 13px;
    }
  }
}
