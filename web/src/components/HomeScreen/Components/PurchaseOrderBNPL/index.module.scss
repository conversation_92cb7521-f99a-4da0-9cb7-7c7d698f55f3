@import '../../../../styles/colors';
@import '../../../../styles/sizes';

#purchase_orders_list_container {
  padding: 30px;

  .header {
    font-size: 15px;
  }

  .currency {
    justify-content: flex-start;
  }

  .pagination {
    display: flex;
    justify-content: flex-end;
  }

  .pending {
    background: $status-pending;
  }

  .rejected {
    background: $status-rejected;
  }

  .approved {
    background: $status-approved;
  }

  .expired {
    background: $status-expired;
  }

  .highlight_record {
    animation: blink_text 1s infinite;
  }

  @keyframes blink_text {
    0%,
    100% {
      font-weight: bold;
    }
    50% {
      font-weight: normal;
    }
  }
}
