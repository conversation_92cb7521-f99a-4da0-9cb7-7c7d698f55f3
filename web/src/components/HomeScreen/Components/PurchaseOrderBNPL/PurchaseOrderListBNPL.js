import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Pagination, Table } from 'semantic-ui-react';
import { withNamespaces } from 'react-i18next';
import classnames from 'classnames';
import moment from 'moment';

import CashMeLoader from '../../../CashMeLoader';
import ServerError from '../../../Validation/ServerError';

import { fetchPurchaseOrders } from '../../../../redux/ducks/purchaseOrder';

import Currency from '../../../Currency';
import {
  FRACTION_NO,
  ORDER_STATUSES,
  STANDARD_DATETIME_FORMAT,
} from '../../../../constants';
import { setArmTimezone } from '../../../../helpers/date';

import styles from './index.module.scss';

class PurchaseOrderListBNPL extends Component {
  highlightRecordsDateBoundary = 1; // 1 min
  tableRowsHighlightTimeout = 15000; // 15 sec

  constructor(props) {
    super(props);

    this.state = {
      values: {},
      activePage: 1,
      highlightRecords: [],
    };
  }

  componentDidMount() {
    this.props.fetchPurchaseOrders(this.state.activePage);

    this.removeTableRowsHighlightTimeout();
  }

  componentDidUpdate(prevProps) {
    const { purchaseOrder } = this.props;

    if (
      !purchaseOrder.error &&
      prevProps.purchaseOrder.data !== purchaseOrder.data
    ) {
      const result = purchaseOrder.data.reduce((result, item) => {
        const timeDifference = moment().diff(
          setArmTimezone(item.createdAt),
          'minutes'
        );
        // Apply a some highlighting to records created less than 1 minute ago
        const isNewRecord = timeDifference < this.highlightRecordsDateBoundary;
        if (isNewRecord) {
          result.push(item.orderId);
        }

        return result;
      }, []);

      this.setState({
        highlightRecords: [...result],
      });
    }
  }

  componentWillUnmount() {
    if (this.timeout) {
      clearTimeout(this.timeout);
    }
  }

  hasLoading = () => {
    const { purchaseOrder } = this.props;

    return purchaseOrder.loading;
  };

  setActivePage = (event, { activePage }) => {
    this.setState({ activePage }, () => {
      this.props.fetchPurchaseOrders(this.state.activePage);
    });
  };

  getStatusClassName = (status, exp) => {
    const statusMappings = {
      [ORDER_STATUSES.PENDING]: styles.pending,
      [ORDER_STATUSES.REJECTED]: styles.rejected,
      [ORDER_STATUSES.APPROVED]: styles.approved,
      [ORDER_STATUSES.EXPIRED]: styles.expired,
    };

    // In the cases when purchase order expiration is in the past, we need to reflect it in the UI as expired
    if (this.isExpired(exp) && status === ORDER_STATUSES.PENDING) {
      status = ORDER_STATUSES.EXPIRED;
    }

    return statusMappings[status] || '';
  };

  getStatusTranslation = (status, exp) => {
    // In the cases when purchase order expiration is in the past, we need to reflect it in the UI as expired
    if (this.isExpired(exp) && status === ORDER_STATUSES.PENDING) {
      status = ORDER_STATUSES.EXPIRED;
    }

    return this.props.t(`bnpl.purchase_order.${status.toLowerCase()}`);
  };

  isExpired = exp => {
    const now = moment().format(STANDARD_DATETIME_FORMAT);
    const expiration = setArmTimezone(exp);

    return expiration < now;
  };

  composeAgentDetails = agent => {
    return `${agent.firstName} ${agent.lastName} (${agent.email})`;
  };

  removeTableRowsHighlightTimeout = () => {
    this.timeout = setTimeout(() => {
      this.setState({
        highlightRecords: [],
      });
    }, this.tableRowsHighlightTimeout);
  };

  render() {
    const {
      t,
      purchaseOrder: { error: serverErrors, data: purchaseOrders, pagination },
    } = this.props;
    const { activePage, highlightRecords } = this.state;

    return (
      <div id={styles.purchase_orders_list_container}>
        <CashMeLoader loading={this.hasLoading()} />
        <Table celled>
          <Table.Header className={styles.header}>
            <Table.Row>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.document_number')}
              </Table.HeaderCell>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.ssn')}
              </Table.HeaderCell>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.phone_number')}
              </Table.HeaderCell>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.amount')}
              </Table.HeaderCell>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.status')}
              </Table.HeaderCell>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.created_at')}
              </Table.HeaderCell>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.expiration')}
              </Table.HeaderCell>
              <Table.HeaderCell>
                {t('bnpl.purchase_order.agent')}
              </Table.HeaderCell>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {purchaseOrders &&
              purchaseOrders.length > 0 &&
              purchaseOrders.map(item => (
                <Table.Row
                  key={item.orderId}
                  className={classnames(
                    this.getStatusClassName(item.status, item.expiration),
                    highlightRecords.includes(item.orderId)
                      ? styles.highlight_record
                      : ''
                  )}
                >
                  <Table.Cell>{item.documentNumber}</Table.Cell>
                  <Table.Cell>{item.ssn}</Table.Cell>
                  <Table.Cell>{item.phoneNumber}</Table.Cell>
                  <Table.Cell>
                    <Currency
                      hasSign={false}
                      value={item.amount}
                      precision={FRACTION_NO}
                      className={styles.currency}
                    />
                  </Table.Cell>
                  <Table.Cell>
                    {this.getStatusTranslation(item.status, item.expiration)}
                  </Table.Cell>
                  <Table.Cell>{setArmTimezone(item.createdAt)}</Table.Cell>
                  <Table.Cell>{setArmTimezone(item.expiration)}</Table.Cell>
                  <Table.Cell>
                    {item.agent && this.composeAgentDetails(item.agent)}
                  </Table.Cell>
                </Table.Row>
              ))}
          </Table.Body>
        </Table>
        <div className={styles.pagination}>
          {pagination && (
            <Pagination
              activePage={activePage}
              totalPages={pagination.totalPages}
              onPageChange={this.setActivePage}
            />
          )}
        </div>
        <ServerError error={serverErrors} />
      </div>
    );
  }
}

function mapStateToProps(state) {
  const { purchaseOrder } = state;

  return {
    purchaseOrder,
  };
}

const mapDispatchToProps = dispatch => {
  return {
    fetchPurchaseOrders: data => dispatch(fetchPurchaseOrders(data)),
  };
};

export default compose(
  withNamespaces('translations'),
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(PurchaseOrderListBNPL);
