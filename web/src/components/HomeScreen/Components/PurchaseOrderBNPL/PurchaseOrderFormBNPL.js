import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { Checkbox, Input } from 'semantic-ui-react';
import { withNamespaces, Trans } from 'react-i18next';
import { Formik, Form } from 'formik';
import NumberFormat from 'react-number-format';
import { v4 as uuidv4 } from 'uuid';

import DiscreteInput from '../../../DiscreteInput';
import GCButton from '../../../GCButton';
import PhoneNumberInput from '../../../PhoneNumberInput';
import CashMeLoader from '../../../CashMeLoader';
import Validation from '../../../Validation';
import ServerError from '../../../Validation/ServerError';
import PurchaseOrderBNPLSchema from '../../../../validation/schemas/PurchaseOrderBNPLSchema';
import ErrorFocus from '../../../Validation/ErrorFocus';
import { getLoanType } from '../../../../helpers/common';
import { HOME_SCREEN_PDFS } from '../../../../constants';
import WarningModal from './WarningModal';

import {
  checkPurchaseOrderExistence,
  resetPurchaseOrderData,
  submitPurchaseOrder,
} from '../../../../redux/ducks/purchaseOrder';

import styles from '../../index.module.scss';

class PurchaseOrderFormBNPL extends Component {
  constructor(props) {
    super(props);

    this.state = {
      values: {},
      showWarningModal: false,
    };

    this.formikRef = React.createRef();
  }

  componentDidUpdate(prevProps) {
    const { purchaseOrder, history } = this.props;
    const {
      submitPurchaseOrderSuccess,
      hasPendingPurchaseOrder,
      error,
    } = purchaseOrder;

    const shouldRedirect =
      !error &&
      prevProps.purchaseOrder.submitPurchaseOrderSuccess !==
        submitPurchaseOrderSuccess &&
      submitPurchaseOrderSuccess;
    const shouldShowModal =
      !error &&
      prevProps.purchaseOrder.hasPendingPurchaseOrder !==
        hasPendingPurchaseOrder &&
      hasPendingPurchaseOrder;

    if (shouldRedirect) {
      history.push('bnpl/purchase-orders');
    }

    if (shouldShowModal) {
      this.setState({ showWarningModal: true });
    }
  }

  componentWillUnmount() {
    // We need to reset some data to avoid componentDidUpdate history push issues
    this.props.resetPurchaseOrderData();
  }

  handleSubmit = ({ documentNumber, phoneNumber, amount }) => {
    const { submitPurchaseOrder, checkPurchaseOrderExistence } = this.props;

    const loanTypeId = getLoanType();
    const orderId = uuidv4();

    const submitOrder = () => {
      submitPurchaseOrder({
        documentNumber,
        loanTypeId,
        phoneNumber,
        amount: amount.toString().replace(/,/g, ''),
        orderId,
      });
    };

    checkPurchaseOrderExistence({ documentNumber }, submitOrder);
  };

  handleChange = (e, setFieldValue) => {
    const value = e.target.value.replace(/\s/g, '');
    setFieldValue('phoneNumber', value.replace(/[()]/g, ''));
  };

  handleCancelModalAction = () => {
    this.setState(
      {
        showWarningModal: !this.state.showWarningModal,
      },
      // We need to reset some data to allow reopen the modal if necessary
      () => this.props.resetPurchaseOrderData()
    );
  };

  handleConfirmModalAction = () => {
    const {
      documentNumber,
      phoneNumber,
      amount,
    } = this.formikRef.current.state.values;
    const { submitPurchaseOrder } = this.props;

    const loanTypeId = getLoanType();
    const orderId = uuidv4();

    submitPurchaseOrder({
      documentNumber,
      loanTypeId,
      phoneNumber,
      amount: amount.toString().replace(/,/g, ''),
      orderId,
    });
  };

  hasLoading = () => {
    const { purchaseOrder, appConfigs } = this.props;

    return purchaseOrder.loading || appConfigs.loading;
  };

  render() {
    const {
      t,
      purchaseOrder: { error: serverErrors },
    } = this.props;

    return (
      <>
        <Formik
          initialValues={{
            firstIsChecked: false,
            secondIsChecked: false,
            documentNumber: '',
            amount: '',
            phoneNumber: '',
          }}
          onSubmit={this.handleSubmit}
          validationSchema={PurchaseOrderBNPLSchema}
          validateOnBlur={true}
          validateOnChange={false}
          ref={this.formikRef}
        >
          {props => {
            const { values, setFieldValue } = props;

            return (
              <div className={styles.homescreen_container}>
                <Form className={styles.bnpl_purchase_order_form}>
                  <CashMeLoader loading={this.hasLoading()} />

                  <div className={styles.bnpl_purchase_order_container}>
                    <p className={styles.document_number_title}>
                      {t('document_number.phone_number')}
                    </p>
                    <Validation name="phoneNumber" showMessage={false}>
                      <div name="phoneNumber">
                        <PhoneNumberInput
                          value={values.phoneNumber}
                          handleChange={e => {
                            this.handleChange(e, setFieldValue);
                          }}
                        />
                      </div>
                    </Validation>

                    <p className={styles.document_number_title}>
                      {t('document_number.pdn_header')}
                    </p>
                    <Validation name="documentNumber" showMessage={false}>
                      <DiscreteInput
                        onChange={value => {
                          setFieldValue('documentNumber', value);
                        }}
                      />
                    </Validation>

                    <p className={styles.document_number_title}>
                      {t('bnpl.purchase_order.amount')}
                    </p>
                    <Validation name="amount" showMessage={false}>
                      <div className={styles.purchase_order_amount}>
                        <NumberFormat
                          customInput={Input}
                          onValueChange={e => {
                            setFieldValue('amount', e.floatValue);
                          }}
                          value={values.prepayment}
                          placeholder="֏ 250,000"
                          autoComplete="off"
                          type="tel"
                          name="amount"
                          allowNegative={false}
                          thousandSeparator={true}
                          maxLength={8}
                        />
                      </div>
                    </Validation>
                  </div>

                  {!serverErrors && <br />}
                  <ServerError error={serverErrors} />

                  <Validation name="firstIsChecked" showMessage={false}>
                    <Checkbox
                      className={styles.confirmation_text}
                      label={
                        <label>
                          <Trans
                            defaults={`document_number.confirmation_text_1_purchase_order`}
                            components={[
                              <a
                                target="_blank"
                                rel="noopener noreferrer"
                                href={HOME_SCREEN_PDFS.AGREEMENT}
                                className={styles.confirmation_text_link}
                              >
                                text
                              </a>,
                            ]}
                          />
                        </label>
                      }
                    />
                  </Validation>
                  <Validation name="secondIsChecked" showMessage={false}>
                    <Checkbox
                      className={styles.confirmation_text}
                      label={t(
                        'document_number.confirmation_text_2_purchase_order'
                      )}
                      checked={values.secondIsChecked}
                    />
                  </Validation>

                  <br />

                  <GCButton
                    className={styles.confirmation_button}
                    type="submit"
                    primary={true}
                  >
                    {t('document_number.submit')}
                  </GCButton>

                  <ErrorFocus />
                </Form>
              </div>
            );
          }}
        </Formik>
        <WarningModal
          isOpen={this.state.showWarningModal}
          handleCancel={this.handleCancelModalAction}
          handleConfirm={this.handleConfirmModalAction}
          serverErrors={serverErrors}
        />
      </>
    );
  }
}

function mapStateToProps(state) {
  const { appConfigs, purchaseOrder } = state;

  return {
    appConfigs,
    purchaseOrder,
  };
}

const mapDispatchToProps = dispatch => {
  return {
    submitPurchaseOrder: data => dispatch(submitPurchaseOrder(data)),
    checkPurchaseOrderExistence: (data, cb) =>
      dispatch(checkPurchaseOrderExistence(data, cb)),
    resetPurchaseOrderData: () => dispatch(resetPurchaseOrderData()),
  };
};

export default compose(
  withRouter,
  withNamespaces('translations'),
  connect(
    mapStateToProps,
    mapDispatchToProps
  )
)(PurchaseOrderFormBNPL);
