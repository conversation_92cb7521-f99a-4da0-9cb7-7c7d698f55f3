import React, { Component } from 'react';
import OutsideClickHandler from 'react-outside-click-handler';

import styles from './index.module.scss';
import classnames from 'classnames';

class APRInformation extends Component {
  constructor(props) {
    super(props);
    this.state = { hide: false };
  }

  handleClick = e => {
    this.setState({ hide: true });
  };

  render() {
    return (
      <OutsideClickHandler onOutsideClick={this.handleClick}>
        <div
          onClick={this.handleClick}
          className={classnames(
            this.state.hide ? styles.hide : '',
            styles.apr_information
          )}
        >
          <p className={styles.info_text}>
            Հարգելի Հաճախորդ, տեղեկացնում ենք Ձեզ, որ սույն վարկատեսակն ունի
            բարձր փաստացի տոկոսադրույք, ուստի նախապես դիտարկեք շուկայում առկա
            նմանատիպ այլ ծառայությունները և գնահատեք վարկը մարելու Ձեր
            հնարավորությունները:
          </p>
          <button className={styles.btn_outline}>ՀԱՍՏԱՏՈՒՄ ԵՄ</button>
        </div>
      </OutsideClickHandler>
    );
  }
}

export default APRInformation;
