@import '../../styles/colors';
@import '../../styles/sizes';

.generic_error_container {
  position: absolute;
  width: 100%;
  background-color: $pink;
  z-index: 1;
  min-height: 70px;
  height: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;

  & > .message {
    color: $white;
    font-size: 14px;
    text-align: center;
  }

  .icon {
    position: absolute;
    right: 15px;
    top: 33%;
    color: $white;
    cursor: pointer;
  }
}

@media screen and (max-width: $lowres-tablet-width) {
  .generic_error_container {
    & > .message {
      padding: 10px 30px 10px 5px;
    }

    .icon {
      right: 0;
    }
  }
}
