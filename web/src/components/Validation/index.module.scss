@import '../../styles/colors';

.error {
  color: $pink;
  font-size: 10px;
  margin: 10px;
}

:global(.server_error) {
  margin-top: 5px;
  margin-bottom: 12px;
  font-size: 10px;
  color: $pink;
}

:global(.vehicle_input_container) {
  &.invalid {
    border: 1px solid $pink !important;
    background-color: $darker-skin;
  }
}

:global(.small_phone_input_container) {
  &.invalid {
    :global(.phone_number_input) {
      border: 1px solid $pink !important;
    }
  }
}

.invalid {
  input {
    border: 1px solid $pink !important;
    background-color: $darker-skin !important;
  }

  input:first-child,
  input:last-child {
    border: 1px solid $pink !important;
  }

  :global(.ui.selection.dropdown) {
    border: 1px solid $pink !important;
    background-color: $darker-skin !important;
  }

  :global(.ui.fluid.selection.dropdown.search) {
    background-color: $darker-skin !important;
  }

  :global(.ui.basic.label.label) {
    background-color: $darker-skin !important;
  }

  label::before {
    background-color: $darker-skin !important;
    border: 1px solid $pink !important;
  }

  iframe {
    border: 1px solid $pink !important;
  }

  :global(.small_phone_input) {
    display: none !important;
  }
}

textarea.invalid {
  background-color: $darker-skin !important;
  border: 1px solid $pink !important;
}

:global(.ui.form .field.field) {
  .invalid {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus {
      border-color: $pink !important;
      box-shadow: 0 0 0px 1000px $darker-skin inset !important;
      -webkit-box-shadow: 0 0 0px 1000px $darker-skin inset !important;
    }
  }
}
