@import '../../styles/colors';
@import '../../styles/sizes';

:global(#root) {
  height: -webkit-fill-available;
}

#renovation {
  position: relative;
  width: 100%;
  height: -webkit-fill-available;
  background-color: $renovation-gray;
  overflow: hidden;
  box-sizing: border-box;

  .bg_bottom_left {
    position: absolute;
    bottom: 0;
    left: 0;
    max-width: 100%;
    z-index: 1;
  }

  .bg_top_right {
    position: absolute;
    top: 0;
    right: 0;
    max-width: 100%;
    z-index: 1;
  }

  .phone {
    position: absolute;
    z-index: 2;
  }

  .logo {
    position: absolute;
    z-index: 2;
    width: 270px;
    left: calc(44% + 15px);
    top: 25%;
  }

  .section {
    max-width: 700px;
    position: absolute;
    z-index: 2;
    left: 44%;
    top: calc(25% + 100px);
    padding: 15px;
    font-weight: bold;

    .description {
      font-family: 'DejaVu Sans Book';

      .title {
        font-size: 24px;
        color: $light-red;
      }
      .text {
        font-size: 18px;
      }
    }

    .apps {
      margin-top: 25px;
      display: flex;
      width: 100%;

      .store_logo {
        width: 250px;
        max-width: 48%;
        padding-right: 20px;

        img {
          width: 100%;
        }
      }
    }
  }

  @media (orientation: landscape) {
    .phone {
      max-height: 80%;
      max-width: 40%;
      left: 5%;
      top: 50%;
      transform: translateY(-50%);
    }

    @media screen and (max-height: 600px) {
      .logo {
        width: 180px;
        top: 7%;
        left: calc(38% + 15px);
      }

      .section {
        top: calc(7% + 50px);
        left: 38%;

        .description {
          .title {
            font-size: 15px;
            margin-bottom: 10px;
          }

          .text {
            font-size: 13px;
            margin-bottom: 5px;
          }
        }

        .apps {
          margin-top: 15px;

          .store_logo {
            width: 160px;
          }
        }
      }
    }
  }

  @media (orientation: portrait) {
    .phone {
      max-height: 60%;
      max-width: 80%;
      left: -10%;
      top: 5%;
    }

    .logo {
      left: auto;
      left: 50%;
      top: 10%;
      max-width: 48%;
    }

    .section {
      position: unset;
      text-align: left;
      bottom: 15px;
      top: auto;
      width: 100%;
      left: 0;

      .description {
        position: absolute;
        z-index: 2;
        bottom: 40px;
      }

      .apps {
        margin-top: 10px;
        position: absolute;
        z-index: 2;
        flex-direction: column;
        left: 50%;
        top: calc(10% + 90px);

        .store_logo {
          margin-bottom: 10px;
        }
      }
    }

    @media screen and (max-width: $mobile-width) {
      .logo {
        top: 5%;
        width: 170px;
      }

      .section {
        bottom: 15px;
        width: 100%;

        .description {
          bottom: 20px;

          .title {
            font-size: 16px;
            margin-bottom: 10px;
          }

          .text {
            font-size: 14px;
            margin-bottom: 5px;
          }
        }

        .apps {
          top: calc(5% + 60px);

          .store_logo {
            width: 150px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 350px) {
    .section {
      .description {
        .title {
          font-size: 14px;
        }

        .text {
          font-size: 12px;
        }
      }
    }
  }
}

#renovation_screen {
  padding-top: 40vh;
  text-align: center;
  line-height: 35px;

  .renovation_screen_title {
    color: $main-text-color;
    font-size: 32px;
  }
}

@media screen and (max-width: $mobile-width) {
  #renovation_screen {
    padding-top: 30vh;
  }
}
