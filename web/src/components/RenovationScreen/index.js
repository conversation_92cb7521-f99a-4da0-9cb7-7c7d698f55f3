import React, { Component } from 'react';
import { withNamespaces } from 'react-i18next';
import { compose } from 'redux';

import { CASH_ME_IOS_URL, CASH_ME_ANDROID_URL } from '../../constants';

import BgBottomLeft from '../../svgs/renovation_bg_corner_left_bottom.png';
import BgTopRight from '../../svgs/renovation_bg_corner_top_right.png';
import Phone from '../../svgs/renovation_phone.png';
import AppStoreLogo from '../../svgs/renovation_app_store.png';
import GooglePlayLogo from '../../svgs/renovation_google_play.png';
import Logo from '../../svgs/renovation_cashme_logo.svg';

import styles from './index.module.scss';

class RenovationScreen extends Component {
  render() {
    const { t } = this.props;

    return process.env.REACT_APP_FULL_RENOVATION === 'true' ? (
      <div id={styles.renovation_screen}>
        <div>
          <div className={styles.renovation_screen_title}>
            {t('renovation_screen.title_full')}
          </div>
        </div>
      </div>
    ) : (
      <div id={styles.renovation}>
        <img
          className={styles.bg_bottom_left}
          src={BgBottomLeft}
          alt="Background"
        />

        <img
          className={styles.bg_top_right}
          src={BgTopRight}
          alt="AppStore logo"
        />

        <img className={styles.phone} src={Phone} alt="AppStore logo" />

        <img className={styles.logo} src={Logo} alt="Cashme logo" />

        <div className={styles.section}>
          <div className={styles.description}>
            <p className={styles.title}>{t('renovation_screen.title')}</p>
            <p className={styles.text}>{t('renovation_screen.text1')}</p>
            <p className={styles.text}>{t('renovation_screen.text2')}</p>
          </div>

          <div className={styles.apps}>
            <a
              href={CASH_ME_IOS_URL}
              className={styles.store_logo}
              target="_blank"
              rel="noreferrer noopener"
            >
              <img src={AppStoreLogo} alt="AppStore logo" />
            </a>
            <a
              href={CASH_ME_ANDROID_URL}
              className={styles.store_logo}
              target="_blank"
              rel="noreferrer noopener"
            >
              <img src={GooglePlayLogo} alt="GooglePlay logo" />
            </a>
          </div>
        </div>
      </div>
    );
  }
}

export default compose(withNamespaces('translations'))(RenovationScreen);
