import { JL } from 'jsnlog';
import { getSuuid } from '../helpers/auth';
import { API_URL } from '../config/config';

const setSuuidHeader = function(xhr) {
  xhr.setRequestHeader('suuid', getSuuid());
};

const appender = JL.createAjaxAppender('appender');

appender.setOptions({
  beforeSend: setSuuidHeader,
  url: `${API_URL}/logs`,
});

JL().setOptions({
  appenders: [appender],
});

const Log = JL();

export default Log;
