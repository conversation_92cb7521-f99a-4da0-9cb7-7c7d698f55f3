import React from 'react';
import { Route, Redirect, withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { isMobile } from 'react-device-detect';
import { isProd, isBrowserAllowedLoanType } from '../helpers/common';

class MobileRoute extends Route {
  render() {
    return !isProd() || isMobile || isBrowserAllowedLoanType() ? (
      super.render()
    ) : (
      <Redirect to={this.props.redirectOnFailure} />
    );
  }
}

export default compose(withRouter)(MobileRoute);
