import React, { Component } from 'react';
import { connect } from 'react-redux';
import FaceRecognition from '../components/FaceRecognition';
import { allowRecognition, recognise } from '../redux/ducks/faceRecognition';

class FaceRecognitionContainer extends Component {
  render() {
    const { allowRecognition, faceRecognition, recognise } = this.props;

    return (
      <FaceRecognition
        faceRecognition={faceRecognition}
        recognise={recognise}
        allowRecognition={allowRecognition}
      />
    );
  }
}

const mapStateToProps = state => {
  const { faceRecognition } = state;

  return { faceRecognition };
};

const mapDispatchToProps = dispatch => {
  return {
    recognise: data => dispatch(recognise(data)),
    allowRecognition: fingerprint => dispatch(allowRecognition(fingerprint)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(FaceRecognitionContainer);
