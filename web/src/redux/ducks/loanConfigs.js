import { ofType } from 'redux-observable';
import { mergeMap, catchError } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { API_URL } from '../../config/config';
import { getSuuidHeader } from '../../helpers/auth';
import { wizardError, wizardErrorClear } from './wizard';
import axios from 'axios';
import _ from 'lodash';

export const LOAN_CONFIGS_FOR_CITIZEN_FETCH =
  'globalcredit/loan_configs/LOAN_CONFIGS_FOR_CITIZEN_FETCH';
export const LOAN_CONFIGS_FETCHED =
  'globalcredit/loan_configs/LOAN_CONFIGS_FETCHED';
export const LOAN_CONFIGS_ERROR =
  'globalcredit/loan_configs/LOAN_CONFIGS_ERROR';

export const fetchLoanConfigsForCitizen = loanTypeId => ({
  type: LOAN_CONFIGS_FOR_CITIZEN_FETCH,
  loanTypeId,
});

export const fetchLoanConfigsFulfilled = payload => ({
  type: LOAN_CONFIGS_FETCHED,
  payload,
});

export const fetchLoanConfigsError = payload => ({
  type: LOAN_CONFIGS_ERROR,
  payload,
});

const initialState = {
  data: null,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case LOAN_CONFIGS_FOR_CITIZEN_FETCH:
      return {
        loading: true,
      };
    case LOAN_CONFIGS_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case LOAN_CONFIGS_ERROR:
      return {
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const loanConfigsForCitizenEpic = action$ =>
  action$.pipe(
    ofType(LOAN_CONFIGS_FOR_CITIZEN_FETCH),
    mergeMap(action =>
      from(
        axios.get(`${API_URL}/citizen/current/config`, {
          params: {
            loan_type_id: action.loanTypeId,
          },
          headers: getSuuidHeader(),
          transformResponse: axios.defaults.transformResponse.concat(
            response => {
              response.data = _.reduce(
                response.data,
                (result, value, key) => {
                  if (+value) {
                    result[key] = +value;
                  } else {
                    result[key] = value;
                  }

                  return result;
                },
                {}
              );

              return response;
            }
          ),
        })
      ).pipe(
        mergeMap(response => {
          return of(
            fetchLoanConfigsFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            wizardError(error.response.data.error),
            fetchLoanConfigsError(error.response && error.response.data.error)
          )
        )
      )
    )
  );
