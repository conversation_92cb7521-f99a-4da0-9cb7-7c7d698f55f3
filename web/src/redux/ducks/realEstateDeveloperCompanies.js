import { ofType } from 'redux-observable';
import { catchError, mergeMap } from 'rxjs/operators';
import { API_URL } from '../../config/config';
import { wizardError, wizardErrorClear } from './wizard';
import axios from 'axios';
import { from, of } from 'rxjs';

export const FETCH_REAL_ESTATE_DEVELOPER_COMPANY =
  'globalcredit/real_estate_developer_companies/FETCH_REAL_ESTATE_DEVELOPER_COMPANY';
export const FETCH_REAL_ESTATE_DEVELOPER_COMPANY_SUCCESS =
  'globalcredit/real_estate_developer_companies/FETCH_REAL_ESTATE_DEVELOPER_COMPANY_SUCCESS';
export const FETCH_REAL_ESTATE_DEVELOPER_COMPANY_ERROR =
  'globalcredit/real_estate_developer_companies/FETCH_REAL_ESTATE_DEVELOPER_COMPANY_ERROR';

export const FETCH_PREDEFINED_REAL_ESTATE_INFO =
  'globalcredit/real_estate_developer_companies/FETCH_PREDEFINED_REAL_ESTATE_INFO';
export const FETCH_PREDEFINED_REAL_ESTATE_INFO_SUCCESS =
  'globalcredit/real_estate_developer_companies/FETCH_PREDEFINED_REAL_ESTATE_INFO_SUCCESS';
export const FETCH_PREDEFINED_REAL_ESTATE_INFO_ERROR =
  'globalcredit/real_estate_developer_companies/FETCH_PREDEFINED_REAL_ESTATE_INFO_ERROR';

export const fetchRealEstateDeveloperCompany = payload => ({
  type: FETCH_REAL_ESTATE_DEVELOPER_COMPANY,
  payload,
});

export const fetchRealEstateDeveloperCompanyFulfilled = payload => ({
  type: FETCH_REAL_ESTATE_DEVELOPER_COMPANY_SUCCESS,
  payload,
});

export const fetchRealEstateDeveloperCompanyError = payload => ({
  type: FETCH_REAL_ESTATE_DEVELOPER_COMPANY_ERROR,
  payload,
});

export const fetchPredefinedRealEstateInfo = payload => ({
  type: FETCH_PREDEFINED_REAL_ESTATE_INFO,
  payload,
});

export const fetchPredefinedRealEstateInfoFulfilled = payload => ({
  type: FETCH_PREDEFINED_REAL_ESTATE_INFO_SUCCESS,
  payload,
});

export const fetchPredefinedRealEstateInfoError = payload => ({
  type: FETCH_PREDEFINED_REAL_ESTATE_INFO_ERROR,
  payload,
});

const initialState = {
  data: undefined,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case FETCH_REAL_ESTATE_DEVELOPER_COMPANY:
      return {
        ...state,
        loading: true,
      };
    case FETCH_REAL_ESTATE_DEVELOPER_COMPANY_SUCCESS:
      return {
        ...state,
        data: action.payload.data,
        loading: false,
      };
    case FETCH_REAL_ESTATE_DEVELOPER_COMPANY_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case FETCH_PREDEFINED_REAL_ESTATE_INFO:
      return {
        ...state,
        loading: true,
      };
    case FETCH_PREDEFINED_REAL_ESTATE_INFO_SUCCESS:
      return {
        ...state,
        data: {
          ...state.data,
          realEstateInfo: action.payload.data,
        },
        loading: false,
      };
    case FETCH_PREDEFINED_REAL_ESTATE_INFO_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const fetchRealEstateDeveloperCompanyEpic = action$ =>
  action$.pipe(
    ofType(FETCH_REAL_ESTATE_DEVELOPER_COMPANY),
    mergeMap(action =>
      from(
        axios.get(
          `${API_URL}/real-estate-developer-company/${action.payload.token}`
        )
      ).pipe(
        mergeMap(response => {
          return of(
            fetchRealEstateDeveloperCompanyFulfilled(response.data),
            wizardErrorClear()
          );
        }),
        catchError(error =>
          of(
            fetchRealEstateDeveloperCompanyError(
              error.response && error.response.data.error
            ),
            wizardError(error.response.data.error)
          )
        )
      )
    )
  );

export const fetchPredefinedRealEstateInfoEpic = action$ =>
  action$.pipe(
    ofType(FETCH_PREDEFINED_REAL_ESTATE_INFO),
    mergeMap(action =>
      from(
        axios.get(`${API_URL}/predefined-real-estate-info/${action.payload}`)
      ).pipe(
        mergeMap(response => {
          return of(fetchPredefinedRealEstateInfoFulfilled(response.data));
        }),
        catchError(error =>
          of(
            fetchPredefinedRealEstateInfoError(
              error.response && error.response.data.error
            )
          )
        )
      )
    )
  );
