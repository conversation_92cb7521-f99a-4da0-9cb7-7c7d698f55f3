import { ofType } from 'redux-observable';
import { mergeMap, switchMap, takeUntil, exhaustMap } from 'rxjs/operators';
import { timer } from 'rxjs';
import { API_URL } from '../../config/config';
import axios from 'axios';

export const START_POLL = 'globalcredit/moderator/START_POLL';
export const STOP_POLL = 'globalcredit/moderator/STOP_POLL';
export const GET_ALL_RESPONSE = 'globalcredit/moderator/GET_ALL_RESPONSE';
export const GET_ALL_ERROR = 'globalcredit/moderator/GET_ALL_ERROR';

export const ASSIGN_REQUEST = 'globalcredit/moderator/ASSIGN_REQUEST';
export const ASSIGN_RESPONSE = 'globalcredit/moderator/ASSIGN_RESPONSE';
export const ASSIGN_ERROR = 'globalcredit/moderator/ASSIGN_ERROR';

export const GET_TAGS_REQUEST = 'globalcredit/moderator/GET_TAGS_REQUEST';
export const GET_TAGS_RESPONSE = 'globalcredit/moderator/GET_TAGS_RESPONSE';
export const GET_TAGS_ERROR = 'globalcredit/moderator/GET_TAGS_ERROR';

export const GET_ONE_ASSIGNED_REQUEST =
  'globalcredit/moderator/GET_ONE_ASSIGNED_REQUEST';
export const GET_ONE_ASSIGNED_RESPONSE =
  'globalcredit/moderator/GET_ONE_ASSIGNED_RESPONSE';
export const GET_ONE_ASSIGNED_ERROR =
  'globalcredit/moderator/GET_ONE_ASSIGNED_ERROR';

export const CONFIRM_REQUEST = 'globalcredit/moderator/CONFIRM_REQUEST';
export const CONFIRM_RESPONSE = 'globalcredit/moderator/CONFIRM_RESPONSE';
export const CONFIRM_ERROR = 'globalcredit/moderator/CONFIRM_ERROR';

export const REJECT_REQUEST = 'globalcredit/moderator/REJECT_REQUEST';
export const REJECT_RESPONSE = 'globalcredit/moderator/REJECT_RESPONSE';
export const REJECT_ERROR = 'globalcredit/moderator/REJECT_ERROR';

export const startPoll = timeout => ({
  type: START_POLL,
  payload: { timeout },
});
export const stopPoll = payload => ({
  type: STOP_POLL,
  payload,
});

export const getAllFulfilled = payload => ({
  type: GET_ALL_RESPONSE,
  payload,
});
export const getAllFailed = payload => ({
  type: GET_ALL_ERROR,
  payload: payload,
  error: true,
});

export const assign = (id, cb) => ({
  type: ASSIGN_REQUEST,
  payload: { id },
  cb,
});
export const assignFulfilled = payload => ({
  type: ASSIGN_RESPONSE,
  payload,
});
export const assignFailed = payload => ({
  type: ASSIGN_ERROR,
  payload: payload,
  error: true,
});

export const getOneAssigned = id => ({
  type: GET_ONE_ASSIGNED_REQUEST,
  payload: { id },
});
export const getOneAssignedFulfilled = payload => ({
  type: GET_ONE_ASSIGNED_RESPONSE,
  payload,
});
export const getOneAssignedFailed = payload => ({
  type: GET_ONE_ASSIGNED_ERROR,
  payload: payload,
  error: true,
});

export const getTags = () => ({
  type: GET_TAGS_REQUEST,
});
export const getTagsFulfilled = payload => ({
  type: GET_TAGS_RESPONSE,
  payload,
});
export const getTagsFailed = payload => ({
  type: GET_TAGS_ERROR,
  payload: payload,
  error: true,
});

export const confirm = (id, activeTags) => ({
  type: CONFIRM_REQUEST,
  payload: { id, activeTags },
});
export const confirmFulfilled = payload => ({
  type: CONFIRM_RESPONSE,
  payload,
});
export const confirmFailed = payload => ({
  type: CONFIRM_ERROR,
  payload: payload,
  error: true,
});

export const reject = (id, notes, activeTags) => ({
  type: REJECT_REQUEST,
  payload: { id, notes, activeTags },
});
export const rejectFulfilled = payload => ({
  type: REJECT_RESPONSE,
  payload,
});
export const rejectFailed = payload => ({
  type: REJECT_ERROR,
  payload: payload,
  error: true,
});

const initialState = {
  queued: {
    data: [],
    loading: false,
    error: undefined,
  },
  tags: {
    data: [],
    loading: false,
    error: undefined,
  },
  assigned: {
    data: [],
    loading: false,
    error: undefined,
  },
  active: {
    data: undefined,
    loading: false,
    error: undefined,
  },
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case GET_ALL_RESPONSE:
      return {
        ...state,
        queued: {
          ...state.queued,
          data: action.payload.queued,
          loading: false,
        },
        assigned: {
          ...state.queued,
          data: action.payload.assigned,
          loading: false,
        },
      };

    case ASSIGN_REQUEST:
      return {
        ...state,
        loading: true,
      };
    case ASSIGN_RESPONSE:
      const id = parseInt(action.payload.id, 10);
      const assigned = [state.queued.data.find(l => l.id === id)];
      const queued = state.queued.data.filter(l => l.id !== id);

      return {
        ...state,
        assigned: {
          ...state.assigned,
          data: assigned,
        },
        queued: {
          ...state.queued,
          data: queued,
        },
        loading: false,
      };
    case ASSIGN_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };

    case GET_ONE_ASSIGNED_REQUEST:
      return {
        ...state,
        active: {
          data: undefined,
          loading: true,
        },
      };
    case GET_ONE_ASSIGNED_RESPONSE:
      return {
        ...state,
        active: {
          data: action.payload,
          loading: false,
        },
      };
    case GET_ONE_ASSIGNED_ERROR:
      return {
        ...state,
        active: {
          data: undefined,
          loading: false,
          error: action.payload,
        },
      };

    case GET_TAGS_REQUEST:
      return {
        ...state,
        tags: {
          ...state.tags,
          loading: true,
        },
      };
    case GET_TAGS_RESPONSE:
      return {
        ...state,
        tags: {
          data: action.payload,
          loading: false,
          error: undefined,
        },
      };
    case GET_TAGS_ERROR:
      return {
        ...state,
        tags: {
          data: undefined,
          loading: false,
          error: action.payload,
        },
      };

    case CONFIRM_REQUEST:
      return {
        ...state,
        active: {
          ...state.active,
          loading: true,
        },
      };
    case CONFIRM_RESPONSE:
      return {
        ...state,
        active: {
          data: undefined,
          loading: false,
        },
        assigned: {
          ...state.assigned,
          data: state.assigned.data.filter(
            l => l.id !== parseInt(action.payload.id, 10)
          ),
        },
      };
    case CONFIRM_ERROR:
      return {
        ...state,
        active: {
          ...state.active,
          loading: false,
          error: action.payload,
        },
      };

    case REJECT_REQUEST:
      return {
        ...state,
        active: {
          ...state.active,
          loading: true,
        },
      };
    case REJECT_RESPONSE:
      return {
        ...state,
        active: {
          data: undefined,
          loading: false,
        },
        assigned: {
          ...state.assigned,
          data: state.assigned.data.filter(
            l => l.id !== parseInt(action.payload.id, 10)
          ),
        },
      };
    case REJECT_ERROR:
      return {
        ...state,
        active: {
          ...state.active,
          loading: false,
          error: action.payload,
        },
      };
    default:
      return state;
  }
}

export const assignEpic = action$ =>
  action$.pipe(
    ofType(ASSIGN_REQUEST),
    mergeMap(action =>
      axios
        .put(`${API_URL}/moderator/queued/${action.payload.id}`)
        .then(resp => {
          const {
            data: { data },
          } = resp;

          action.cb();
          return assignFulfilled(data);
        })
        .catch(error => {
          return assignFailed(error.response && error.response.data.error);
        })
    )
  );

export const getTagsEpic = action$ =>
  action$.pipe(
    ofType(GET_TAGS_REQUEST),
    mergeMap(action =>
      axios
        .get(`${API_URL}/moderator/tags`)
        .then(resp => {
          const {
            data: { data },
          } = resp;

          return getTagsFulfilled(data);
        })
        .catch(error => {
          return getTagsFailed(error.response.data.error);
        })
    )
  );

export const getOneAssignedEpic = action$ =>
  action$.pipe(
    ofType(GET_ONE_ASSIGNED_REQUEST),
    mergeMap(action =>
      axios
        .get(`${API_URL}/moderator/assigned/${action.payload.id}`)
        .then(resp => {
          const {
            data: { data },
          } = resp;

          return getOneAssignedFulfilled(data);
        })
        .catch(error => {
          return getOneAssignedFailed(error.response.data.error);
        })
    )
  );

export const confirmEpic = action$ =>
  action$.pipe(
    ofType(CONFIRM_REQUEST),
    mergeMap(action =>
      axios
        .post(`${API_URL}/moderator/confirm`, action.payload)
        .then(resp => {
          const {
            data: { data },
          } = resp;

          return confirmFulfilled(data);
        })
        .catch(error => {
          return confirmFailed(error.response.data.error);
        })
    )
  );

export const rejectEpic = action$ =>
  action$.pipe(
    ofType(REJECT_REQUEST),
    mergeMap(action =>
      axios
        .post(`${API_URL}/moderator/reject`, action.payload)
        .then(resp => {
          const {
            data: { data },
          } = resp;

          return rejectFulfilled(data);
        })
        .catch(error => {
          return rejectFailed(error.response.data.error);
        })
    )
  );

export const pollEpic = action$ =>
  action$.pipe(
    ofType(START_POLL),
    switchMap(action =>
      timer(0, action.payload.timeout).pipe(
        takeUntil(action$.ofType(STOP_POLL)),
        exhaustMap(() =>
          axios
            .get(`${API_URL}/moderator/all`)
            .then(resp => {
              const {
                data: { data },
              } = resp;

              return getAllFulfilled(data);
            })
            .catch(error => {
              return getAllFailed(error.response.data.error);
            })
        )
      )
    )
  );
