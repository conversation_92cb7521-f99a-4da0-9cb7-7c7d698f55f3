import { ofType } from 'redux-observable';
import { mergeMap } from 'rxjs/operators';
import { API_URL } from '../../config/config';
import axios from 'axios';

export const SUBMIT_PURCHASE_ORDER =
  'globalcredit/purchase_order/SUBMIT_PURCHASE_ORDER';
export const SUBMIT_PURCHASE_ORDER_SUCCESS =
  'globalcredit/purchase_order/SUBMIT_PURCHASE_ORDER_SUCCESS';
export const SUBMIT_PURCHASE_ORDER_ERROR =
  'globalcredit/purchase_order/SUBMIT_PURCHASE_ORDER_ERROR';

export const CHECK_PURCHASE_ORDER_EXISTENCE =
  'globalcredit/purchase_order/CHECK_PURCHASE_ORDER_EXISTENCE';
export const CHECK_PURCHASE_ORDER_EXISTENCE_SUCCESS =
  'globalcredit/purchase_order/CHECK_PURCHASE_ORDER_EXISTENCE_SUCCESS';
export const CHECK_PURCHASE_ORDER_EXISTENCE_ERROR =
  'globalcredit/purchase_order/CHECK_PURCHASE_ORDER_EXISTENCE_ERROR';

export const FETCH_PURCHASE_ORDERS =
  'globalcredit/purchase_order/FETCH_PURCHASE_ORDERS';
export const FETCH_PURCHASE_ORDERS_SUCCESS =
  'globalcredit/purchase_order/FETCH_PURCHASE_ORDERS_SUCCESS';
export const FETCH_PURCHASE_ORDERS_ERROR =
  'globalcredit/purchase_order/FETCH_PURCHASE_ORDERS_ERROR';

export const FETCH_MERCHANT_DETAILS =
  'globalcredit/purchase_order/FETCH_MERCHANT_DETAILS';
export const FETCH_MERCHANT_DETAILS_SUCCESS =
  'globalcredit/purchase_order/FETCH_MERCHANT_DETAILS_SUCCESS';
export const FETCH_MERCHANT_DETAILS_ERROR =
  'globalcredit/purchase_order/FETCH_MERCHANT_DETAILS_ERROR';

export const RESET_PURCHASE_ORDER_DATA =
  'globalcredit/purchase_order/RESET_PURCHASE_ORDER_DATA';

export const submitPurchaseOrder = payload => ({
  type: SUBMIT_PURCHASE_ORDER,
  payload,
});

export const submitPurchaseOrderFulfilled = payload => ({
  type: SUBMIT_PURCHASE_ORDER_SUCCESS,
  payload,
});

export const submitPurchaseOrderError = payload => ({
  type: SUBMIT_PURCHASE_ORDER_ERROR,
  payload,
});

export const checkPurchaseOrderExistence = (payload, cb) => ({
  type: CHECK_PURCHASE_ORDER_EXISTENCE,
  payload,
  cb,
});

export const checkPurchaseOrderExistenceFulfilled = payload => ({
  type: CHECK_PURCHASE_ORDER_EXISTENCE_SUCCESS,
  payload,
});

export const checkPurchaseOrderExistenceError = payload => ({
  type: CHECK_PURCHASE_ORDER_EXISTENCE_ERROR,
  payload,
});

export const fetchPurchaseOrders = payload => ({
  type: FETCH_PURCHASE_ORDERS,
  payload,
});

export const fetchPurchaseOrdersFulfilled = payload => ({
  type: FETCH_PURCHASE_ORDERS_SUCCESS,
  payload,
});

export const fetchPurchaseOrdersError = payload => ({
  type: FETCH_PURCHASE_ORDERS_ERROR,
  payload,
});

export const fetchMerchantDetails = payload => ({
  type: FETCH_MERCHANT_DETAILS,
  payload,
});

export const fetchMerchantDetailsFulfilled = payload => ({
  type: FETCH_MERCHANT_DETAILS_SUCCESS,
  payload,
});

export const fetchMerchantDetailsError = payload => ({
  type: FETCH_MERCHANT_DETAILS_ERROR,
  payload,
});

export const resetPurchaseOrderData = () => ({
  type: RESET_PURCHASE_ORDER_DATA,
});

const initialState = {
  data: undefined,
  error: undefined,
  loading: false,
  submitPurchaseOrderSuccess: false,
  hasPendingPurchaseOrder: false,
  pagination: undefined,
  merchantDetails: undefined,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case SUBMIT_PURCHASE_ORDER:
      return {
        ...state,
        loading: true,
      };
    case SUBMIT_PURCHASE_ORDER_SUCCESS:
      return {
        ...state,
        submitPurchaseOrderSuccess: action.payload.data.success,
        loading: false,
        error: undefined,
      };
    case SUBMIT_PURCHASE_ORDER_ERROR:
      return {
        ...state,
        error: action.payload,
        hasPendingPurchaseOrder: false,
        loading: false,
      };
    case CHECK_PURCHASE_ORDER_EXISTENCE:
      return {
        ...state,
        loading: true,
      };
    case CHECK_PURCHASE_ORDER_EXISTENCE_SUCCESS:
      return {
        ...state,
        hasPendingPurchaseOrder: action.payload.data.exists,
        loading: false,
        error: undefined,
      };
    case CHECK_PURCHASE_ORDER_EXISTENCE_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case FETCH_PURCHASE_ORDERS:
      return {
        ...state,
        loading: true,
      };
    case FETCH_PURCHASE_ORDERS_SUCCESS:
      const { meta, ...rest } = action.payload.data;

      return {
        ...state,
        data: Object.values(rest),
        pagination: meta.pagination,
        loading: false,
      };
    case FETCH_PURCHASE_ORDERS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case FETCH_MERCHANT_DETAILS:
      return {
        ...state,
        loading: true,
      };
    case FETCH_MERCHANT_DETAILS_SUCCESS:
      return {
        ...state,
        merchantDetails: action.payload.data,
        loading: false,
      };
    case FETCH_MERCHANT_DETAILS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case RESET_PURCHASE_ORDER_DATA:
      return {
        ...state,
        submitPurchaseOrderSuccess: false,
        hasPendingPurchaseOrder: false,
      };
    default:
      return state;
  }
}

export const submitPurchaseOrderEpic = action$ =>
  action$.pipe(
    ofType(SUBMIT_PURCHASE_ORDER),
    mergeMap(action =>
      axios
        .post(`${API_URL}/merchant/bnpl/purchase-order`, action.payload)
        .then(response => {
          return submitPurchaseOrderFulfilled(response.data);
        })
        .catch(error => {
          return submitPurchaseOrderError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const checkPurchaseOrderExistenceEpic = action$ =>
  action$.pipe(
    ofType(CHECK_PURCHASE_ORDER_EXISTENCE),
    mergeMap(action =>
      axios
        .post(
          `${API_URL}/merchant/bnpl/has-pending-purchase-order`,
          action.payload
        )
        .then(response => {
          if (!response.data.data.exists) {
            action.cb();
          }
          return checkPurchaseOrderExistenceFulfilled(response.data);
        })
        .catch(error => {
          return checkPurchaseOrderExistenceError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const fetchPurchaseOrdersEpic = action$ =>
  action$.pipe(
    ofType(FETCH_PURCHASE_ORDERS),
    mergeMap(action =>
      axios
        .get(`${API_URL}/merchant/bnpl/purchase-orders?page=${action.payload}`)
        .then(response => {
          return fetchPurchaseOrdersFulfilled(response.data);
        })
        .catch(error => {
          return fetchPurchaseOrdersError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const fetchMerchantDetailsEpic = action$ =>
  action$.pipe(
    ofType(FETCH_MERCHANT_DETAILS),
    mergeMap(action =>
      axios
        .get(`${API_URL}/merchant/bnpl/merchant-details`)
        .then(response => {
          return fetchMerchantDetailsFulfilled(response.data);
        })
        .catch(error => {
          return fetchMerchantDetailsError(
            error.response && error.response.data.error
          );
        })
    )
  );
