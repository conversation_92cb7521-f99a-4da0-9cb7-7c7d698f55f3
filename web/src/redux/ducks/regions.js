import { ofType } from 'redux-observable';
import { mergeMap } from 'rxjs/operators';
import { API_URL } from '../../config/config';
import { getSuuidHeader } from '../../helpers/auth';
import axios from 'axios';

export const REGIONS_FETCH = 'globalcredit/regions/REGIONS_FETCH';
export const REGIONS_FETCHED = 'globalcredit/regions/REGIONS_FETCHED';
export const REGIONS_ERROR = 'globalcredit/regions/REGIONS_ERROR';

export const fetchRegions = () => ({
  type: REGIONS_FETCH,
});

export const fetchRegionsFulfilled = payload => ({
  type: REGIONS_FETCHED,
  payload,
});

export const fetchRegionsError = payload => ({
  type: REGIONS_ERROR,
  payload,
});

const initialState = {
  data: undefined,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case REGIONS_FETCH:
      return {
        ...state,
        loading: true,
      };
    case REGIONS_FETCHED:
      return {
        ...state,
        data: action.payload.data,
        loading: false,
      };
    case REGIONS_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const fetchRegionsEpic = action$ =>
  action$.pipe(
    ofType(REGIONS_FETCH),
    mergeMap(() =>
      axios
        .get(`${API_URL}/regions`, {
          // Todo this loan type can be changed and used for OASL too or use getLoanType method
          headers: getSuuidHeader(),
        })
        .then(response => {
          return fetchRegionsFulfilled(response.data);
        })
        .catch(error => {
          return fetchRegionsError(error.response && error.response.data.error);
        })
    )
  );
