import { ofType } from 'redux-observable';
import { mergeMap } from 'rxjs/operators';
import { API_URL } from '../../config/config';
import { getSuuidHeader } from '../../helpers/auth';
import axios from 'axios';

export const PIPE_TYPES_FETCH = 'globalcredit/pipes/PIPE_TYPES_FETCH';
export const PIPE_TYPES_FETCHED = 'globalcredit/pipes/PIPE_TYPES_FETCHED';
export const PIPE_TYPES_ERROR = 'globalcredit/pipes/PIPE_TYPES_ERROR';

export const fetchPipeTypes = () => ({
  type: PIPE_TYPES_FETCH,
});

export const fetchPipeTypesFulfilled = payload => ({
  type: PIPE_TYPES_FETCHED,
  payload,
});

export const fetchPipeTypesError = payload => ({
  type: PIPE_TYPES_ERROR,
  payload,
});

const initialState = {
  data: undefined,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case PIPE_TYPES_FETCH:
      return {
        loading: true,
      };
    case PIPE_TYPES_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case PIPE_TYPES_ERROR:
      return {
        error: action.payload,
        loading: false,
      };
    default:
      return state;
  }
}

export const fetchPipeTypesEpic = action$ =>
  action$.pipe(
    ofType(PIPE_TYPES_FETCH),
    mergeMap(() =>
      axios
        .get(`${API_URL}/pipe-types/4`, {
          headers: getSuuidHeader(),
        })
        .then(response => {
          return fetchPipeTypesFulfilled(response.data);
        })
        .catch(error => {
          return fetchPipeTypesError(
            error.response && error.response.data.error
          );
        })
    )
  );
