import { ofType } from 'redux-observable';
import { mergeMap } from 'rxjs/operators';
import { API_URL } from '../../config/config';
import { getSuuidHeader } from '../../helpers/auth';
import axios from 'axios';

export const FETCH_CAR_VERIFICATION_DATA =
  'globalcredit/car_verification/FETCH_CAR_VERIFICATION_DATA';
export const CAR_VERIFICATION_DATA_FETCHED =
  'globalcredit/car_verification/CAR_VERIFICATION_DATA_FETCHED';
export const FETCH_CAR_VERIFICATION_DATA_ERROR =
  'globalcredit/car_verification/FETCH_CAR_VERIFICATION_DATA_ERROR';

export const STORE_CAR_VERIFICATION_DATA =
  'globalcredit/car_verification/STORE_CAR_VERIFICATION_DATA';
export const CAR_VERIFICATION_DATA_STORED =
  'globalcredit/car_verification/CAR_VERIFICATION_DATA_STORED';
export const STORE_CAR_VERIFICATION_DATA_ERROR =
  'globalcredit/car_verification/STORE_CAR_VERIFICATION_DATA_ERROR';
export const RESET_CAR_VERIFICATION_DATA =
  'globalcredit/car_verification/RESET_CAR_VERIFICATION_DATA';

export const fetchCarVerificationData = payload => ({
  type: FETCH_CAR_VERIFICATION_DATA,
  payload,
});

export const fetchCarVerificationDataFulfilled = payload => ({
  type: CAR_VERIFICATION_DATA_FETCHED,
  payload,
});

export const fetchCarVerificationDataError = payload => ({
  type: FETCH_CAR_VERIFICATION_DATA_ERROR,
  payload,
});

export const storeCarVerificationData = (payload, cb) => ({
  type: STORE_CAR_VERIFICATION_DATA,
  payload,
  cb,
});

export const storeCarVerificationDataFulfilled = payload => ({
  type: CAR_VERIFICATION_DATA_STORED,
  payload,
});

export const storeCarVerificationDataError = payload => ({
  type: STORE_CAR_VERIFICATION_DATA_ERROR,
  payload,
});

export const resetCarVerificationData = () => ({
  type: RESET_CAR_VERIFICATION_DATA,
});

const initialState = {
  data: null,
  loading: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case FETCH_CAR_VERIFICATION_DATA:
      return {
        loading: true,
      };
    case CAR_VERIFICATION_DATA_FETCHED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case FETCH_CAR_VERIFICATION_DATA_ERROR:
      return {
        error: action.payload,
        loading: false,
      };
    case STORE_CAR_VERIFICATION_DATA:
      return {
        loading: true,
      };
    case CAR_VERIFICATION_DATA_STORED:
      return {
        data: action.payload.data,
        loading: false,
      };
    case STORE_CAR_VERIFICATION_DATA_ERROR:
      return {
        error: action.payload,
        loading: false,
      };
    case RESET_CAR_VERIFICATION_DATA:
      return {
        data: null,
        loading: false,
      };
    default:
      return state;
  }
}

export const fetchCarVerificationEpic = action$ =>
  action$.pipe(
    ofType(FETCH_CAR_VERIFICATION_DATA),
    mergeMap(action =>
      axios
        .get(`${API_URL}/car-verification`, {
          headers: getSuuidHeader(),
        })
        .then(response => {
          return fetchCarVerificationDataFulfilled(response.data);
        })
        .catch(error => {
          return fetchCarVerificationDataError(
            error.response && error.response.data.error
          );
        })
    )
  );

export const storeCarVerificationEpic = action$ =>
  action$.pipe(
    ofType(STORE_CAR_VERIFICATION_DATA),
    mergeMap(action =>
      axios
        .post(`${API_URL}/car-verification`, action.payload, {
          headers: getSuuidHeader(),
        })
        .then(response => {
          action.cb();
          return storeCarVerificationDataFulfilled(response.data);
        })
        .catch(error => {
          return storeCarVerificationDataError(
            error.response && error.response.data.error
          );
        })
    )
  );
