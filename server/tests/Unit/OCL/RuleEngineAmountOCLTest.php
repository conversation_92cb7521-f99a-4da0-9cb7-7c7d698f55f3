<?php

namespace Tests\Unit\OCL;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineAmountOCLTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'OCL';

    public function dataProvider(): array
    {
        return [
            // new customer
            // with norq
            [[
                'rule' => 'dr_score_0_0.05_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 100000,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '330000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 100000,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '330000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 100000,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '460000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 100000,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '460000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 100000,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '410000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 100000,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '410000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 100000,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '380000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 100000,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '380000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 100000,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '380000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 100000,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '380000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 100000,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '280000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 100000,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '280000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 100000,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '280000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 100000,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '280000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 100000,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 100000,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 100000,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '380000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 100000,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '380000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 100000,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '280000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 100000,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '280000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 100000,
                        'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '230000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 100000,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '230000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 100000,
                        'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '280000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 100000,
                        'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 100000,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 100000,
                        'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '330000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 100000,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '330000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 100000,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 100000,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 100000,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 100000,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 100000,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 100000,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 100000,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '270000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 100000,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '270000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 100000,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 100000,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 100000,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 100000,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 100000,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 100000,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 100000,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 100000,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 100000,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '270000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 100000,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '270000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 100000,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 100000,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 100000,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 100000,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 100000,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_21_25_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 100000,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 100000,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_26_30_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 100000,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 100000,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_31_40_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 100000,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '220000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 100000,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_41_50_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 100000,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 100000,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_51_65_newOclCustomer_salary',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 100000,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],

            // without norq
            [[
                'rule' => 'dr_score_0_0.05_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 0,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 0,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 0,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '440000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 0,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '440000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 0,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '390000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 0,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '390000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 0,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '360000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 0,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '360000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 0,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '360000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0_0.05_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 0,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '360000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 0,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 0,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 0,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 0,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 0,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '290000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 0,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '290000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 0,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '360000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 0,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '360000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 0,
                        'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.05_0.1_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 0,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 0,
                        'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 0,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '210000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 0,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 0,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '260000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 0,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '290000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 0,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '290000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 0,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 0,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '310000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 0,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.1_0.15_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 0,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 0,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 0,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 0,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 0,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 0,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '250000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 0,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '250000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 0,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 0,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 0,
                        'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.15_0.2_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 0,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 0,
                        'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 0,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 0,
                        'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '170000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 0,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '170000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 0,
                        'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '250000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 0,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '250000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 0,
                        'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '170000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 0,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '170000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 0,
                        'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.2_0.3_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 0,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '190000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'salary' => 0,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_21_25_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'salary' => 0,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'salary' => 0,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_26_30_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'salary' => 0,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'salary' => 0,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_31_40_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'salary' => 0,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'salary' => 0,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_41_50_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'salary' => 0,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'salary' => 0,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'dr_score_0.3_0.4_age_51_65_newOclCustomer_salary_0',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'salary' => 0,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'drScore' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],

            // old customer
            [[
                'rule' => 'fico_501_549_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                 ],
             ]],
            [[
                'rule' => 'fico_501_549_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '400000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                 ],
             ]],
            [[
                'rule' => 'fico_501_549_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '180000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '250000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_501_549_age_51_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '300000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '550000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '250000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '350000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_550_599_age_51_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '420000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '550000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '360000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '550000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_600_649_age_51_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '450000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '420000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '550000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_650_699_age_51_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '160000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '550000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '550000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_700_749_age_51_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '200000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_21_25_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_26_30_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_31_40_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_41_50_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '600000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'fico_750_age_51_65_oldOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'fico' => [
                        'amount' => '250000',
                        'duration' => 18,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
