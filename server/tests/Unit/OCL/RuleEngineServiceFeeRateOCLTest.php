<?php

namespace Tests\Unit\OCL;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineServiceFeeRateOCLTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'OCL';

    public function dataProvider(): array
    {
        return [
            // new customer starting values
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'dr_score' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'drScore' => 0,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                         'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                         'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                         'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                         'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                         'drScore' => 0.051,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                         'drScore' => 0.11,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                         'drScore' => 0.16,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_51_65_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                         'drScore' => 0.21,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_51_65_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_greater_or_less_0_0.4_age_21_65_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'drScore' => 0.5,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 0,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_greater_or_less_0_0.4_age_21_65_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 18,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 0,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_greater_or_less_0_0.4_age_21_65_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 70,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 0,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],

            // old customers starting values
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 501,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 550,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 600,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 650,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 700,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 21,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 26,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 31,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 41,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 51,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],

            // new customer end value tests
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0_0.05_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'drScore' => 0.05,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.05_0.1_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'drScore' => 0.1,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.1_0.15_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'drScore' => 0.15,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.15_0.2_age_51_65_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'drScore' => 0.2,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                         'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.2_0.3_age_51_65_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'drScore' => 0.3,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_21_25_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                         'drScore' => 0.31,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_26_30_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_31_40_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_41_50_newCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 64,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_dr_score_0.3_0.4_age_51_65_newOclCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'drScore' => 0.4,
                        'isNewOclCustomer' => true,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],

            // old customer end values
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_501_549_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 549,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 48,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_550_599_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 599,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_600_649_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 649,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 70,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_650_699_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 699,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 36,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_700_749_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 749,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_21_25_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 25,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_26_30_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 30,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_31_40_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 40,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_41_50_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 50,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'serviceFeeRate_fico_750_and_more_age_51_65_oldCustomer',
                'context' => [
                    'input' => [
                        'age' => 65,
                        'fico' => 750,
                        'isNewOclCustomer' => false,
                    ],
                ],
                'output' => [
                    'serviceFeeRate' => [
                        'amount' => null,
                        'duration' => null,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => 24,
                        'label' => null,
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
