<?php

namespace Tests\Unit\OIWL;

use Tests\Unit\RuleEngineExecutorTest;

class RuleEngineAmountOIWLTest extends RuleEngineExecutorTest
{
    protected $loan_type = 'OIWL';

    public function dataProvider(): array
    {
        return [
            [[
                'rule' => 'walletAmount_10000',
                'context' => [
                    'input' => [
                        'walletAmount' => 10000,
                    ],
                ],
                'output' => [
                    'walletAmount' => [
                        'amount' => '10000',
                        'duration' => 12,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'walletAmount_10000',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'walletAmount_20000',
                'context' => [
                    'input' => [
                        'walletAmount' => 20000,
                    ],
                ],
                'output' => [
                    'walletAmount' => [
                        'amount' => '20000',
                        'duration' => 12,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'walletAmount_20000',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'walletAmount_80000',
                'context' => [
                    'input' => [
                        'walletAmount' => 80000,
                    ],
                ],
                'output' => [
                    'walletAmount' => [
                        'amount' => '80000',
                        'duration' => 12,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'walletAmount_80000',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'walletAmount_100000',
                'context' => [
                    'input' => [
                        'walletAmount' => 100000,
                    ],
                ],
                'output' => [
                    'walletAmount' => [
                        'amount' => '100000',
                        'duration' => 12,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'walletAmount_100000',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'walletAmount_10000',
                'context' => [
                    'input' => [
                        'walletAmount' => 10000,
                    ],
                ],
                'output' => [
                    'walletAmount' => [
                        'amount' => '10000',
                        'duration' => 12,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'walletAmount_10000',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'walletAmount_150000',
                'context' => [
                    'input' => [
                        'walletAmount' => 150000,
                    ],
                ],
                'output' => [
                    'walletAmount' => [
                        'amount' => '150000',
                        'duration' => 12,
                        'rate' => null,
                        'resolvable' => true,
                        'service_fee_rate' => null,
                        'label' => 'walletAmount_150000',
                        'id' => null,
                    ],
                ],
            ]],
            [[
                'rule' => 'walletActiveLoansBalance',
                'context' => [
                    'input' => [
                        'walletActiveLoansBalance' => 25000,
                    ],
                ],
                'output' => [
                    'walletActiveLoansBalance' => [
                        'amount' => '25000',
                        'duration' => 12,
                        'rate' => null,
                        'resolvable' => false,
                        'service_fee_rate' => null,
                        'label' => 'walletActiveLoansBalance',
                        'id' => null,
                    ],
                ],
            ]],
        ];
    }
}
