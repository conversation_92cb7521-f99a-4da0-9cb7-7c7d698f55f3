<?php

namespace Tests\Unit\Calculator;

use App\Calculator\LoanCalculator;
use App\Helpers\ArrayHelper;
use App\PHPUnitUtil;
use App\TestCase;
use Carbon\Carbon;
use function Functional\map;

class LoanCalculatorTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setup();

        $fake_now = Carbon::create(2020, 11, 1, 0);
        Carbon::setTestNow($fake_now);
    }

    public function testCalculateFor200000Amount80Percent1Month()
    {
        $calculator = new LoanCalculator(200000, 80, 12, ['max_duration' => 18]);

        $calculate = PHPUnitUtil::getPrivateMethod($calculator, 'calculate');
        $actual = $calculate->invoke($calculator, 1);
        $expected = [1, 213333.3333333334, 213333.3333333334];

        $this->assertEquals($actual, $expected);
    }

    public function testCalculateFor200000Amount80Percent2Months()
    {
        $calculator = new LoanCalculator(200000, 80, 12, ['max_duration' => 18]);

        $calculate = PHPUnitUtil::getPrivateMethod($calculator, 'calculate');
        $actual = $calculate->invoke($calculator, 2);
        $expected = [2, 110107.52688172043, 220215.05376344087];

        $this->assertEquals($actual, $expected);
    }

    public function testCalculateFor200000Amount80Percent18Months()
    {
        $calculator = new LoanCalculator(200000, 80, 12, ['max_duration' => 18]);

        $calculate = PHPUnitUtil::getPrivateMethod($calculator, 'calculate');
        $actual = $calculate->invoke($calculator, 18);
        $expected = [18, 19406.************, 349322.***********];

        $this->assertEquals($actual, $expected);
    }

    public function testCalculateAllowedMonthlyMinMax()
    {
        $calculator = new LoanCalculator(200000, 80, 12, ['max_duration' => 18]);

        $actual = $calculator->monthlyMinMax();
        $expected = [20000, 111000];

        $this->assertEquals($actual, $expected);
    }

    public function testCalculateAllowedMonthlyMinMaxTakingIntoAccountLowerBorderOf117MonthsPayment()
    {
        $calculator = new LoanCalculator(2700000, 48, 12, ['max_duration' => 120]);

        $actual = $calculator->monthlyMinMax(6);
        $expected = [110000, 516000];

        $this->assertEquals($actual, $expected);
    }

    public function testCalculateMonthsByMonthlyPayment()
    {
        $calculator = new LoanCalculator(200000, 80, 12, ['max_duration' => 18]);

        $monthsByMonthlyPayment = PHPUnitUtil::getPrivateMethod($calculator, 'monthsByMonthlyPayment');
        [$diff, ['months' => $months]] = $monthsByMonthlyPayment->invoke($calculator, 50000);

        $actual = [$diff, $months];
        $expected = [1656.*************, 5];

        $this->assertEquals($actual, $expected);
    }

    public function testGenerateScheduleFor80RateAnd20000BaseAmountMonthly50000()
    {
        $calculator = new LoanCalculator(200000, 80, 12, ['max_duration' => 18]);
        $actual = $calculator->generateSchedule(50000);

        // Schedule must be for 5 months
        $this->assertEquals(count($actual), 5);

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee', 'service_fee_interest', 'service_fee_plain', 'base', 'balance', 'payment']);

        $this->assertEquals($first, [
            'service_fee' => 13150.68,
            'service_fee_interest' => 1972.6,
            'service_fee_plain' => 11178.08,
            'base' => 36849.32,
            'balance' => 163150.68,
            'payment' => 50000.0,
        ]);

        // Check last item in the schedule
        $last = ArrayHelper::pick($actual[count($actual) - 1], ['service_fee', 'service_fee_interest', 'service_fee_plain', 'base', 'balance', 'payment']);

        $this->assertEquals($last, [
            'service_fee' => 3060.81,
            'service_fee_interest' => 391.59,
            'service_fee_plain' => 2669.22,
            'base' => 38422.55,
            'balance' => 0,
            'payment' => 41483.36,
        ]);
    }

    public function testGenerateScheduleFor100RateAnd80000BaseAmountMonthly32000()
    {
        $calculator = new LoanCalculator(80000, 100, 12, ['max_duration' => 18]);
        $actual = $calculator->generateSchedule(32000);

        // Schedule must be for 3 months
        $this->assertEquals(count($actual), 3);

        // Check first item in the schedule
        $first = ArrayHelper::pick($actual[0], ['service_fee', 'service_fee_interest', 'service_fee_plain', 'base', 'balance', 'payment']);

        $this->assertEquals($first, [
            'service_fee' => 6575.34,
            'service_fee_interest' => 789.04,
            'service_fee_plain' => 5786.3,
            'base' => 25424.66,
            'balance' => 54575.34,
            'payment' => 32000.0,
        ]);

        // Check last item in the schedule
        $last = ArrayHelper::pick($actual[count($actual) - 1], ['service_fee', 'service_fee_interest', 'service_fee_plain', 'base', 'balance', 'payment']);

        $this->assertEquals($last, [
            'service_fee' => 2447.45,
            'service_fee_interest' => 277.99,
            'service_fee_plain' => 2169.46,
            'base' => 27275.94,
            'balance' => 0,
            'payment' => 29723.39,
        ]);
    }

    public function testPaymentDaysFor100RateAnd1000000BaseAmountMonthly110000()
    {
        $calculator = new LoanCalculator(1000000, 100, 12, ['max_duration' => 18]);
        $actual = $calculator->generateSchedule(110000);

        $actual_days = map($actual, function ($s) {
            return $s['days'];
        });

        $this->assertEquals($actual_days, [
            30,
            31,
            31,
            28,
            31,
            30,
            31,
            30,
            31,
            31,
            30,
            31,
            30,
            31,
            31,
            28,
            31,
            30,
        ]);
    }

    public function testScheduleSummary()
    {
        $calculator = new LoanCalculator(200000, 80, 12, ['max_duration' => 18]);

        $actual = $calculator->scheduleSummary(50000);
        unset($actual['next_payment_date']);

        $expected = [
            'total' => 241483.36,
            'monthly_payment' => 50000.0,
            'second_month_payment' => 50000.0,
            'last_month_payment' => 41483.36,
            'months' => 5,
            'real_interest_rate' => 81.75,
        ];

        $this->assertEquals($actual, $expected);
    }

    public function testAPRFor5000000()
    {
        // For car loan there's withdrawal fee rate
        $withdrawal_fee_rate = 0.03;
        $loan_amount = 5000000;

        $calculator = new LoanCalculator($loan_amount, 30, 12, ['max_duration' => 120]);
        $withdrawal_fee_amount = $loan_amount * $withdrawal_fee_rate;
        $apr = $calculator->calculateAPR(280000, Carbon::create(2022, 04, 06, 0), $withdrawal_fee_amount);

        $this->assertEquals($apr, 38.98);
    }

    public function testEndOfMonthSchedule()
    {
        $fake_now = Carbon::create(2020, 3, 31, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculator(200000, 80, 24, ['max_duration' => 18]);
        $actual = $calculator->generateSchedule(15000);

        // Asserting last item should be enough
        $last = ArrayHelper::pick($actual[count($actual) - 1], ['service_fee', 'service_fee_interest', 'service_fee_plain', 'base', 'balance', 'payment', 'days']);

        $this->assertEquals($last, [
            'service_fee' => 13150.68,
            'service_fee_interest' => 3945.21,
            'service_fee_plain' => 9205.47,
            'base' => 200000.0,
            'balance' => 0,
            'payment' => 213150.68,
            'days' => 30,
        ]);
    }

    public function testScheduleWithAdjustedLastMonthNegativeBalance()
    {
        $calculator = new LoanCalculator(691200, 48, 12, ['max_duration' => 112]);
        $actual = $calculator->generateSchedule(28000);

        // Asserting last item should be enough
        $last = ArrayHelper::pick($actual[count($actual) - 1], [
            'service_fee',
            'service_fee_interest',
            'service_fee_plain',
            'base',
            'balance',
            'payment',
            'days',
        ]);

        $this->assertEquals($last, [
            'service_fee' => 130.69,
            'service_fee_interest' => 26.18,
            'service_fee_plain' => 104.51,
            'base' => 2568.33,
            'balance' => 0,
            'payment' => 2699.02,
            'days' => 31,
        ]);
    }

    public function testMonthSwtichCase()
    {
        $fake_now = Carbon::create(2020, 4, 30, 22, 0, 0);
        Carbon::setTestNow($fake_now);

        $calculator = new LoanCalculator(200000, 80, 24, ['max_duration' => 18]);
        $actual = $calculator->generateSchedule(15000);

        // Asserting last item should be enough
        $last = ArrayHelper::pick($actual[count($actual) - 1], ['service_fee', 'service_fee_interest', 'service_fee_plain', 'base', 'balance', 'payment', 'days']);

        $this->assertEquals($last, [
            'service_fee' => 13589.04,
            'service_fee_interest' => 4076.71,
            'service_fee_plain' => 9512.33,
            'base' => 200000.0,
            'balance' => 0,
            'payment' => 213589.04,
            'days' => 31,
        ]);
    }
}
