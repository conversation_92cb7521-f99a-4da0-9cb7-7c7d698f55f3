<?php

namespace App\Rules;

class OBLRules
{
    public static function rules(): array
    {
        return [
            'isDead' => [
                'name' => 'isDead',
                'label' => 'isDead',
                'formula' => json_encode(['==' => [['var' => 'isDead'], 'true']]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'isQKH' => [
                'name' => 'isQKH',
                'label' => 'isQKH',
                'formula' => json_encode(['==' => [['var' => 'isQKH'], 'true']]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'hasOverdueLoans' => [
                'name' => 'hasOverdueLoans',
                'label' => 'hasOverdueLoans',
                'formula' => json_encode(['==' => [['var' => 'hasOverdueLoans'], true]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'hasAddress' => [
                'name' => 'hasAddress',
                'label' => 'hasAddress',
                'formula' => json_encode(['==' => [['var' => 'hasAddress'], false]]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'loanClass' => [
                'name' => 'loanClass',
                'label' => 'loanClass',
                'formula' => json_encode(['and' => [['!=' => [['var' => 'loanClass'], 'Ստանդարտ']], ['!=' => [['var' => 'loanClass'], null]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'delayQuantity' => [
                'name' => 'delayQuantity',
                'label' => 'delayQuantity',
                'formula' => json_encode(['>' => [['var' => 'delayQuantity'], 0]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'acraStatus' => [
                'name' => 'acraStatus',
                'label' => 'acraStatus',
                'formula' => json_encode(['==' => [['var' => 'acraStatus'], '2']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'hasCredit' => [
                'name' => 'hasCredit',
                'label' => 'hasCredit',
                'formula' => json_encode(['==' => [['var' => 'hasCredit'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'isNewOrganisation' => [
                'name' => 'isNewOrganisation',
                'label' => 'isNewOrganisation',
                'formula' => json_encode(['==' => [['var' => 'isNewOrganisation'], true]]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'delayQuantityInYear' => [
                'name' => 'delayQuantityInYear',
                'label' => 'delayQuantityInYear',
                'formula' => json_encode(['>' => [['var' => 'delayQuantityInYear'], 31]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'amount_100000' => [
                'name' => 'amount',
                'label' => 'amount_100000',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'delayQuantityInYear'], 15]],
                    ['<=' => [['var' => 'delayQuantityInYear'], 31]],
                ]]),
                'amount' => '100000',
                'duration' => 60,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'amount_500000' => [
                'name' => 'amount',
                'label' => 'amount_500000',
                'formula' => json_encode(['<=' => [['var' => 'delayQuantityInYear'], 15]]),
                'amount' => '500000',
                'duration' => 60,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dstiAmount' => [
                'name' => 'dstiAmount',
                'label' => 'dstiAmount',
                'formula' => true,
                'amount' => json_encode(['var' => 'dstiAmount']),
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
        ];
    }
}
