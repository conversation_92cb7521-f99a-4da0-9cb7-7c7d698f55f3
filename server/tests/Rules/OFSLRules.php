<?php

namespace App\Rules;

class OFSLRules
{
    public static function rules(): array
    {
        return [
            'dr_score_0_0.02_walletLoansCount_0' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>=' => [['var' => 'drScore'], 0]],
                            ['<=' => [['var' => 'drScore'], 0.02]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '40000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0_0.02_walletLoansCount_1_4' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>=' => [['var' => 'drScore'], 0]],
                            ['<=' => [['var' => 'drScore'], 0.02]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '150000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0_0.02_walletLoansCount_5' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>=' => [['var' => 'drScore'], 0]],
                            ['<=' => [['var' => 'drScore'], 0.02]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '150000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.02_0.05_walletLoansCount_0' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.02]],
                            ['<=' => [['var' => 'drScore'], 0.05]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '40000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.02_0.05_walletLoansCount_1_4' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.02]],
                            ['<=' => [['var' => 'drScore'], 0.05]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '100000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.02_0.05_walletLoansCount_5' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.02]],
                            ['<=' => [['var' => 'drScore'], 0.05]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '150000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.05_0.1_walletLoansCount_0' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.05]],
                            ['<=' => [['var' => 'drScore'], 0.1]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '20000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.05_0.1_walletLoansCount_1_4' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.05]],
                            ['<=' => [['var' => 'drScore'], 0.1]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '80000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.05_0.1_walletLoansCount_5' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.05]],
                            ['<=' => [['var' => 'drScore'], 0.1]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '100000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.1_0.165_walletLoansCount_0' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.1]],
                            ['<=' => [['var' => 'drScore'], 0.165]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '20000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.1_0.165_walletLoansCount_1_4' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.1]],
                            ['<=' => [['var' => 'drScore'], 0.165]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '80000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.1_0.165_walletLoansCount_5' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.1]],
                            ['<=' => [['var' => 'drScore'], 0.165]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '100000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_0.165_0.4_walletLoansCount_0' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.165]],
                            ['<=' => [['var' => 'drScore'], 0.4]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '20000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_lower_0_greater_0.4_newCustomer' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'walletLoansCount'], 0]]]],
                        ['or' => [
                            ['>' => [['var' => 'drScore'], 0.4]],
                            ['<' => [['var' => 'drScore'], 0]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'dr_score_lower_0_greater_0.165_oldCustomer' => [
                'name' => 'drScore',
                'label' => 'label',
                'formula' => json_encode(
                    ['if' => [['and' => [['>' => [['var' => 'walletLoansCount'], 0]]]],
                        ['or' => [
                            ['>' => [['var' => 'drScore'], 0.165]],
                            ['<' => [['var' => 'drScore'], 0]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'citizenship' => [
                'name' => 'citizenship',
                'label' => 'label',
                'formula' => json_encode(['!=' => [['var' => 'citizenship'], 'ARM']]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'hasAddress' => [
                'name' => 'hasAddress',
                'label' => 'label',
                'formula' => json_encode(['==' => [['var' => 'hasAddress'], false]]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
            ],
            'isDead' => [
                'name' => 'isDead',
                'label' => 'label',
                'formula' => json_encode(['==' => [['var' => 'isDead'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'age' => [
                'name' => 'age',
                'label' => 'label',
                'formula' => json_encode(['if' => [['==' => [['var' => 'walletLoansCount'], 0]],
                    ['or' => [
                        ['<' => [['var' => 'age'], 21]],
                        ['>' => [['var' => 'age'], 65]],
                    ]],
                    ['or' => [
                        ['<' => [['var' => 'age'], 18]],
                        ['>' => [['var' => 'age'], 65]],
                    ]],
                ],
                ]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'loanClass' => [
                'name' => 'loanClass',
                'label' => 'label',
                'formula' => json_encode(['and' => [['!=' => [['var' => 'loanClass'], 'Ստանդարտ']], ['!=' => [['var' => 'loanClass'], null]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'hasOverdueLoans' => [
                'name' => 'hasOverdueLoans',
                'label' => 'label',
                'formula' => json_encode(['==' => [['var' => 'hasOverdueLoans'], true]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'hasCredit' => [
                'name' => 'hasCredit',
                'label' => 'label',
                'formula' => json_encode(['==' => [['var' => 'hasCredit'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'delayQuantityInYear' => [
                'name' => 'delayQuantityInYear',
                'label' => 'label',
                'formula' => json_encode(['>' => [['var' => 'delayQuantityInYear'], 31]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'acraStatus' => [
                'name' => 'acraStatus',
                'label' => 'label',
                'formula' => json_encode(['==' => [['var' => 'acraStatus'], '2']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'isQKH' => [
                'name' => 'isQKH',
                'label' => 'label',
                'formula' => json_encode(['==' => [['var' => 'isQKH'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'walletAmount_20000' => [
                'name' => 'walletAmount',
                'label' => 'walletAmount_20000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 20000]]),
                'amount' => '20000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'walletAmount_40000' => [
                'name' => 'walletAmount',
                'label' => 'walletAmount_40000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 40000]]),
                'amount' => '40000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'walletAmount_80000' => [
                'name' => 'walletAmount',
                'label' => 'walletAmount_80000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 80000]]),
                'amount' => '80000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'walletAmount_100000' => [
                'name' => 'walletAmount',
                'label' => 'walletAmount_100000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 100000]]),
                'amount' => '100000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'walletAmount_150000' => [
                'name' => 'walletAmount',
                'label' => 'walletAmount_150000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 150000]]),
                'amount' => '150000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'walletActiveLoansBalance' => [
                'name' => 'walletActiveLoansBalance',
                'label' => 'walletActiveLoansBalance',
                'formula' => true,
                'amount' => json_encode(['var' => 'walletActiveLoansBalance']),
                'duration' => 12,
                'resolvable' => false,
                'disabled' => false,
                'phase' => 1,
            ],
            'walletAvailableAmount' => [
                'name' => 'walletAvailableAmount',
                'label' => 'walletAvailableAmount',
                'formula' => true,
                'amount' => 'min($walletAmount, $walletMaxAmount)',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
        ];
    }
}
