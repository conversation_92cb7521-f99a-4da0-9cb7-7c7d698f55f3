<?php

namespace App\Rules;

class BNPLRules
{
    public static function rules(): array
    {
        return [
            'isDead' => [
                'name' => 'isDead',
                'formula' => json_encode(['==' => [['var' => 'isDead'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 1,
                'disabled' => false,
            ],
            'hasAddress' => [
                'name' => 'hasAddress',
                'formula' => json_encode(['==' => [['var' => 'hasAddress'], false]]),
                'amount' => '0',
                'resolvable' => true,
                'label' => 'label',
                'phase' => 1,
                'disabled' => false,
            ],
            'isQKH' => [
                'name' => 'isQKH',
                'label' => 'isQKH',
                'formula' => json_encode(['==' => [['var' => 'isQKH'], 'true']]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            'citizenship' => [
                'name' => 'citizenship',
                'formula' => json_encode(['!=' => [['var' => 'citizenship'], 'ARM']]),
                'amount' => '0',
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'age' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['or' => [['<' => [['var' => 'age'], 21]], ['>' => [['var' => 'age'], 65]]]],
                    ['!=' => [['var' => 'salary'], 0]], ],
                ]),
                'amount' => '0',
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'loanClass' => [
                'name' => 'loanClass',
                'formula' => json_encode(['and' => [['!=' => [['var' => 'loanClass'], 'Ստանդարտ']], ['!=' => [['var' => 'loanClass'], null]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'isValidLoanClass12' => [
                'name' => 'isValidLoanClass12',
                'formula' => json_encode(['and' => [['==' => [['var' => 'isValidLoanClass12'], false]], ['==' => [['var' => 'salary'], 0]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'hasOverdueLoans' => [
                'name' => 'hasOverdueLoans',
                'formula' => json_encode(['==' => [['var' => 'hasOverdueLoans'], true]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'acraStatus' => [
                'name' => 'acraStatus',
                'formula' => json_encode(['==' => [['var' => 'acraStatus'], '2']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'fico_missing_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['if' => [['missing' => ['fico']], true, false]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'fico_0_500' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>' => [['var' => 'fico'], 0]],
                    ['<' => [['var' => 'fico'], 500]],
                ]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'hasCredit' => [
                'name' => 'hasCredit',
                'formula' => json_encode(['==' => [['var' => 'hasCredit'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'fico_500_549_age_21_25_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '120000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_missing_age_21_25_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['if' => [['missing' => ['fico']], true, false]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '120000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_26_30_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_missing_age_26_30_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['if' => [['missing' => ['fico']], true, false]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_31_40_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '96000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_missing_age_31_40_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['if' => [['missing' => ['fico']], true, false]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '96000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_41_50_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '84000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_missing_age_41_50_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['if' => [['missing' => ['fico']], true, false]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '84000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_51_65_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '72000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_missing_age_51_65_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['if' => [['missing' => ['fico']], true, false]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '72000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_21_25_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_26_30_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '360000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_31_40_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '120000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_41_50_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '108000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_51_65_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '84000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_21_25_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '300000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_26_30_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '420000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_31_40_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '180000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_41_50_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '156000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_51_65_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '120000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_21_25_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '360000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_26_30_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '480000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_31_40_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_41_50_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '216000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_51_65_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '156000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_21_25_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '420000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_26_30_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '540000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_31_40_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '300000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_41_50_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_51_65_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '216000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_21_25_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '480000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_26_30_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '600000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_31_40_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '360000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_41_50_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '300000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_51_65_is_first_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['<=' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_21_25_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '180000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_26_30_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '360000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_31_40_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '120000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_41_50_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '108000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_500_549_age_51_65_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 500]],
                    ['<=' => [['var' => 'fico'], 549]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '84000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_21_25_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '300000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_26_30_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '600000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_31_40_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '180000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_41_50_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '144000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_550_599_age_51_65_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 550]],
                    ['<=' => [['var' => 'fico'], 599]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '108000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_21_25_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '420000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_26_30_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '780000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_31_40_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_41_50_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '180000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_600_649_age_51_65_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 600]],
                    ['<=' => [['var' => 'fico'], 649]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '144000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_21_25_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '540000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_26_30_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '960000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_31_40_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '300000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_41_50_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_650_699_age_51_65_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 650]],
                    ['<=' => [['var' => 'fico'], 699]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '180000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_21_25_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '660000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_26_30_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '1080000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_31_40_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '360000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_41_50_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '300000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_700_749_age_51_65_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 700]],
                    ['<=' => [['var' => 'fico'], 749]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '240000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_21_25_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 21]],
                    ['<=' => [['var' => 'age'], 25]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '780000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_26_30_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 26]],
                    ['<=' => [['var' => 'age'], 30]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '1200000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_31_40_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 31]],
                    ['<=' => [['var' => 'age'], 40]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '480000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_41_50_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 41]],
                    ['<=' => [['var' => 'age'], 50]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '420000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'fico_750_age_51_65_is_second_limit' => [
                'name' => 'fico',
                'formula' => json_encode(['and' => [
                    ['>=' => [['var' => 'fico'], 750]],
                    ['!=' => [['var' => 'fico'], 99999]],
                    ['>=' => [['var' => 'age'], 51]],
                    ['<=' => [['var' => 'age'], 65]],
                    ['>' => [['var' => 'firstTransactionDuration'], 91]],
                ]]),
                'amount' => '300000',
                'duration' => 3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            'N_A_fico' => [
                'name' => 'fico',
                'formula' => json_encode(['==' => [['var' => 'fico'], 99999]]),
                'amount' => '0',
                'duration' => 0,
                'rate' => 0,
                'service_fee_rate' => 0,
                'resolvable' => true,
                'label' => 'label',
                'phase' => 2,
                'disabled' => false,
            ],
            'osmAmount' => [
                'name' => 'osmAmount',
                'formula' => true,
                'amount' => json_encode(['var' => 'osmAmount']),
                'resolvable' => true,
                'label' => 'label',
                'phase' => 4,
                'disabled' => false,
            ],
            'dstiAmount' => [
                'name' => 'dstiAmount',
                'formula' => true,
                'amount' => json_encode(['var' => 'dstiAmount']),
                'resolvable' => true,
                'label' => 'label',
                'phase' => 3,
                'disabled' => false,
            ],
        ];
    }
}
