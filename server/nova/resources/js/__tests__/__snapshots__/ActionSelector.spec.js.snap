// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ActionSelector it renders correctly with actions and pivot action 1`] = `
<div>
  <div class="mr-3">
    <select data-testid="action-select" class="form-control form-select mr-2">
      <option value="" disabled="disabled" selected="selected">Select Action</option>
      <optgroup label="Resource">
        <option value="action-1">
          Action 1
        </option>
        <option value="action-2">
          Action 2
        </option>
      </optgroup>
      <!---->
    </select>
    <button data-testid="action-confirm" disabled="disabled" class="btn btn-default btn-primary btn-disabled">
      Run
    </button>
  </div>
  <div class="v-portal" style="display: none;"></div>
</div>
`;
