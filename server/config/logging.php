<?php

use App\Logging\ElasticsearchNestedFormatter;
use App\Logging\ElasticsearchNestedHandler;
use App\Logging\GcDebugProcessor;
use App\Logging\GcIntrospectionProcessor;
use App\Logging\GcUidProcessor;
use Elasticsearch\ClientBuilder;

return [
    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily', 'slack', 'elasticsearch'],
        ],

        'elasticsearch' => [
            'driver' => 'monolog',
            'level' => 'debug',
            'formatter' => ElasticsearchNestedFormatter::class,
            'formatter_with' => [
                'index' => env('ELASTICSEARCH_LOGS_INDEX', 'cashme_log'),
                'type' => env('ELASTICSEARCH_LOGS_DOC_TYPE', '_doc'),
            ],
            'handler' => ElasticsearchNestedHandler::class,
            'handler_with' => [
                'client' => ClientBuilder::create()
                    ->setHosts([env('ELASTICSEARCH_HOST', 'http://elasticsearch:9200/')])
                    ->setBasicAuthentication(env('ELASTICSEARCH_USERNAME', ''), env('ELASTICSEARCH_PASSWORD', ''))
                    ->build(),
                'options' => [
                    'index' => env('ELASTICSEARCH_LOGS_INDEX', 'cashme_log'),
                    'type' => env('ELASTICSEARCH_LOGS_DOC_TYPE', '_doc'),
                    'ignore_error' => false,
                ],
            ],
            'tap' => [GcUidProcessor::class, GcIntrospectionProcessor::class, GcDebugProcessor::class],
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 0,
            'tap' => [GcUidProcessor::class, GcIntrospectionProcessor::class, GcDebugProcessor::class],
            'permission' => 0774,
        ],

        'client' => [
            'driver' => 'daily',
            'path' => storage_path('logs/client/clients.log'),
            'level' => 'debug',
            'days' => 0,
            'tap' => [GcIntrospectionProcessor::class, GcDebugProcessor::class],
            'permission' => 0774,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],

        'slack_liveness' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_LIVENESS_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],
    ],

    'CLIENT_LOG_CHANNELS' => [
        'CLIENT' => 'client',
    ],
];
