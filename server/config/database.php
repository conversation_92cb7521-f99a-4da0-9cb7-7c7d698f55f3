<?php

return [
    /*
    |--------------------------------------------------------------------------
    | PDO Fetch Style
    |--------------------------------------------------------------------------
    |
    | By default, database results will be returned as instances of the PHP
    | stdClass object; however, you may desire to retrieve records in an
    | array format for simplicity. Here you can tweak the fetch style.
    |
    */

    'fetch' => PDO::FETCH_OBJ,

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        // Tmp For-Support-Credit
        'pgsql_covid' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST_COVID19', '127.0.0.1'),
            'port' => env('DB_PORT_COVID19', '5432'),
            'database' => env('DB_DATABASE_COVID19', 'forge'),
            'username' => env('DB_USERNAME_COVID19', 'forge'),
            'password' => env('DB_PASSWORD_COVID19', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsql_stabilization_loan' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST_SUPPORT_LOAN', '127.0.0.1'),
            'port' => env('DB_PORT_SUPPORT_LOAN', '5432'),
            'database' => env('DB_DATABASE_SUPPORT_LOAN', 'forge'),
            'username' => env('DB_USERNAME_SUPPORT_LOAN', 'forge'),
            'password' => env('DB_PASSWORD_SUPPORT_LOAN', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'hc_sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('HC_MS_SQL_HOST'),
            'port' => env('HC_MS_SQL_PORT'),
            'database' => env('HC_MS_SQL_DATABASE'),
            'username' => env('HC_MS_SQL_USERNAME'),
            'password' => env('HC_MS_SQL_PASSWORD'),
            'prefix' => '',
        ],

        'warehouse_sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('WAREHOUSE_MS_SQL_HOST'),
            'port' => env('WAREHOUSE_MS_SQL_PORT'),
            'database' => env('WAREHOUSE_MS_SQL_DATABASE'),
            'username' => env('WAREHOUSE_MS_SQL_USERNAME'),
            'password' => env('WAREHOUSE_MS_SQL_PASSWORD'),
            'prefix' => '',
        ],

        'dw_sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DW_MS_SQL_HOST'),
            'port' => env('DW_MS_SQL_PORT'),
            'database' => env('DW_MS_SQL_DATABASE'),
            'username' => env('DW_MS_SQL_USERNAME'),
            'password' => env('DW_MS_SQL_PASSWORD'),
            'prefix' => '',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [
        'cluster' => false,

        'default' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => 0,
        ],
    ],
];
