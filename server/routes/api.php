<?php

use Dingo\Api\Routing\Router;
use Illuminate\Http\Request;

if (config('app.env') == 'local') {
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Headers: suuid, client-identifier, client-secret, content-type, authorization, jsnlog-requestid');
    header('Access-Control-Expose-Headers: suuid, client-identifier, client-secret, authorization, jsnlog-requestid');
}

/** @var Router $api */
$api = app(Router::class);
$api->version('v1', ['middleware' => 'response_formatter'], function (Router $api) {
    $api->post('/unblock/{origin}', 'App\\Api\\V1\\Controllers\\SecurityController@unblockCitizenBySsn')->middleware('internal_auth');

    $api->group(['prefix' => 'settings'], function (Router $api) {
        $api->get('server-time', 'App\\Api\\V1\\Controllers\\SettingsController@getServerTime');
        $api->get('app-status', 'App\\Api\\V1\\Controllers\\SettingsController@getAppStatus');
    });

    $api->group(['prefix' => 'auth'], function (Router $api) {
        $api->post('signup', 'App\\Api\\V1\\Controllers\\SignUpController@signUp');
        $api->post('login', 'App\\Api\\V1\\Controllers\\LoginController@login');

        $api->post('recovery', 'App\\Api\\V1\\Controllers\\ForgotPasswordController@sendResetEmail');
        $api->post('reset', 'App\\Api\\V1\\Controllers\\ResetPasswordController@resetPasswordEmail');

        $api->post('logout', 'App\\Api\\V1\\Controllers\\LogoutController@logout');
        $api->post('refresh', 'App\\Api\\V1\\Controllers\\RefreshController@refresh');
        $api->get('me', 'App\\Api\\V1\\Controllers\\UserController@me');
    });

    $api->group(['middleware' => ['jwt.auth', 'can:access,App\Models\Loan']], function (Router $api) {
        $api->get('moderator/queued', 'App\\Api\\V1\\Controllers\\ModeratorController@getQueued');
        $api->put('moderator/queued/{id}', 'App\\Api\\V1\\Controllers\\ModeratorController@assign');
        $api->get('moderator/assigned', 'App\\Api\\V1\\Controllers\\ModeratorController@getAssigned');
        $api->get('moderator/assigned/{id}', 'App\\Api\\V1\\Controllers\\ModeratorController@getSingleAssigned');
        $api->get('moderator/all', 'App\\Api\\V1\\Controllers\\ModeratorController@getAll');
        $api->post('moderator/confirm', 'App\\Api\\V1\\Controllers\\ModeratorController@confirm');
        $api->post('moderator/reject', 'App\\Api\\V1\\Controllers\\ModeratorController@reject');
        $api->get('moderator/tags', 'App\\Api\\V1\\Controllers\\ModeratorController@getTags');
    });

    $api->post('submit-document-oasl', 'App\\Api\\V1\\Controllers\\CitizenController@submitDocumentOasl')
        ->middleware('security_entry')
        ->middleware('security_meta:'.constants('META_STEPS.SUBMIT_DOCUMENT'));

    $api->post('submit-document-oiql', 'App\\Api\\V1\\Controllers\\CitizenController@submitDocumentOiql')
        ->middleware('security_entry')
        ->middleware('security_meta:'.constants('META_STEPS.SUBMIT_DOCUMENT'));

    $steps = [constants('META_STEPS.SEND_IDENTITY_VERIFICATION_SMS')];
    $api->post('send-verification-sms', 'App\\Api\\V1\\Controllers\\SecurityController@sendVerificationSms')
        ->middleware('security_entry')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.RESEND_IDENTITY_VERIFICATION_SMS'), constants('META_STEPS.SEND_IDENTITY_VERIFICATION_SMS')];
    $api->post('resend-verification-sms', 'App\\Api\\V1\\Controllers\\SecurityController@sendVerificationSms')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.VERIFY_IDENTITY'), constants('META_STEPS.SEND_IDENTITY_VERIFICATION_SMS')];
    $api->post('verify-sms', 'App\\Api\\V1\\Controllers\\SecurityController@verifySms')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.ALLOW_OPEN_VIDEO'), constants('META_STEPS.CREDENTIALS_VERIFIED')];
    $api->post('allow-open-video', 'App\\Api\\V1\\Controllers\\SecurityController@allowOpenVideo')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.CREATE_STREAM_SESSION'), constants('META_STEPS.ALLOW_OPEN_VIDEO')];
    $api->get('archive/session', 'App\\Api\\V1\\Controllers\\SecurityController@startSession')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.START_STREAM'), constants('META_STEPS.CREATE_STREAM_SESSION')];
    $api->post('archive/start', 'App\\Api\\V1\\Controllers\\SecurityController@startRecording')
        ->middleware('security_meta:'.join(',', $steps));

    $api->post('archive/{archive_id}/stop', 'App\\Api\\V1\\Controllers\\SecurityController@stopRecording');

    $steps = [constants('META_STEPS.SUBMIT_DOCUMENT'), constants('META_STEPS.ALLOW_OPEN_VIDEO')];
    $api->post('check-liveness', 'App\\Api\\V1\\Controllers\\SecurityController@detectFace')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.STORE_EKENG_PHOTO')];
    $api->post('store-ekeng-photo', 'App\\Api\\V1\\Controllers\\SecurityController@storeEkengPhoto')
        ->middleware('security_meta:'.join(',', $steps));

    $api->post('logs', 'App\\Api\\V1\\Controllers\\ClientLogsController@createLog');

    $api->get('qr-get-discount', 'App\\Api\\V1\\Controllers\\CitizenController@getDiscount');

    $api->get('generate-referral-agreement', 'App\\Api\\V1\\Controllers\\ReferralCodeController@generateAgreement');

    $api->get('referral-code', 'App\\Api\\V1\\Controllers\\ReferralCodeController@getReferralCode');

    $api->post('verify-referral-code', 'App\\Api\\V1\\Controllers\\ReferralCodeController@verifyReferralCode');

    $api->post('set-referral-code', 'App\\Api\\V1\\Controllers\\ReferralCodeController@setReferralCode');

    $api->group(['middleware' => 'conditional.jwt.auth'], function (Router $api) {
        $api->get('pipe-types/{loan_type_id}', 'App\\Api\\V1\\Controllers\\LoanController@getPipeTypes');

        $api->get('regions', 'App\\Api\\V1\\Controllers\\RegionController@getRegions');

        $api->post('villages/{loan_type_id}', 'App\\Api\\V1\\Controllers\\LoanController@getVillages');

        $api->get('arpi-solar-terms/{loan_type_id}', 'App\\Api\\V1\\Controllers\\LoanController@getArpiSolarTerms');

        $api->get('solar-panel-types/{loan_type_id}', 'App\\Api\\V1\\Controllers\\LoanController@getSolarPanelTypes');

        $steps = [constants('META_STEPS.GET_ESTATE_DETAILS'), constants('META_STEPS.VERIFY_IDENTITY')];
        $api->get('real-estate-details', 'App\\Api\\V1\\Controllers\\RealEstateDetailsController@index')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.STORE_ESTATE_DETAILS'), constants('META_STEPS.VERIFY_IDENTITY')];
        $api->post('real-estate-details', 'App\\Api\\V1\\Controllers\\RealEstateDetailsController@store')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.STORE_ESTATE_MEDIA_DETAILS'), constants('META_STEPS.VERIFY_IDENTITY')];
        $api->post('real-estate-details/media', 'App\\Api\\V1\\Controllers\\RealEstateDetailsController@storeMedia')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_ESTATE_MEDIA_DETAILS'), constants('META_STEPS.VERIFY_IDENTITY')];
        $api->get('real-estate-details/media', 'App\\Api\\V1\\Controllers\\RealEstateDetailsController@getMedia')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.DELETE_ESTATE_MEDIA_DETAILS'), constants('META_STEPS.STORE_ESTATE_MEDIA_DETAILS')];
        $api->delete('real-estate-details/media/{id}', 'App\\Api\\V1\\Controllers\\RealEstateDetailsController@removeMedia')
            ->middleware('security_meta:'.join(',', $steps));

        $api->get('real-estate-developer-company/{token}', 'App\\Api\\V1\\Controllers\\PredefinedRealEstateController@show');

        $api->get('predefined-real-estate-info/{token}', 'App\\Api\\V1\\Controllers\\PredefinedRealEstateController@getRealEstateInfo');

        $steps = [constants('META_STEPS.FETCH_CITIZEN'), constants('META_STEPS.SUBMIT_DOCUMENT')];
        $api->get('citizens', 'App\\Api\\V1\\Controllers\\CitizenController@show')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TRADE_VEHICLE'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('trade-vehicle', 'App\\Api\\V1\\Controllers\\CitizenController@getTradeVehicleCredit')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->post('loans/approve', 'App\\Api\\V1\\Controllers\\LoanController@approve')
            ->middleware('store_type')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->post('loans/approve-oasl', 'App\\Api\\V1\\Controllers\\LoanController@approveOasl')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('loans', 'App\\Api\\V1\\Controllers\\LoanController@show')
            ->middleware('security_meta:'.join(',', $steps));

        // Personal info submission requires loan approval step
        $steps = [constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('loans/personal-info', 'App\\Api\\V1\\Controllers\\LoanController@getPersonalInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_EVALUATION_COMPANIES'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('evaluation-companies', 'App\\Api\\V1\\Controllers\\RealEstateEvaluationCompaniesController@getEvaluationCompanies')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->put('loans/personal-info', 'App\\Api\\V1\\Controllers\\LoanController@updatePersonalInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.CURRENT_CONFIG'), constants('META_STEPS.SUBMIT_DOCUMENT')];
        $api->get('citizen/current/config', 'App\\Api\\V1\\Controllers\\LoanController@getConfigsForCitizen')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CASH_OFFICES'), constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('cash-offices', 'App\\Api\\V1\\Controllers\\LoanController@getCashOffices')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.FETCH_AGENT_SCHEDULES'), constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('fetch-agent-schedules', 'App\\Api\\V1\\Controllers\\LoanController@fetchAgentSchedules')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_TRANSFER_TYPES'), constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('transfer-types', 'App\\Api\\V1\\Controllers\\LoanController@getTransferTypes')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('loans/wire-transfer', 'App\\Api\\V1\\Controllers\\LoanController@storeWireTransfer')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('loans/cash-transfer', 'App\\Api\\V1\\Controllers\\LoanController@storeCashTransfer')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('loans/easypay-wallet-transfer', 'App\\Api\\V1\\Controllers\\LoanController@storeEasypayWalletTransfer')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('loans/idram-wallet-transfer', 'App\\Api\\V1\\Controllers\\LoanController@storeIdramWalletTransfer')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('loans/card-to-card-transfer', 'App\\Api\\V1\\Controllers\\LoanController@storeCardToCardTransfer')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('loans/product-provision', 'App\\Api\\V1\\Controllers\\LoanController@storeProductTransfer')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.CAR_VERIFICATION'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('car-verification', 'App\\Api\\V1\\Controllers\\LoanController@storeCarVerificationInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CAR_VERIFICATION_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('car-verification', 'App\\Api\\V1\\Controllers\\LoanController@getCarVerificationInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('get-code', 'App\\Api\\V1\\Controllers\\SmsValidationController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.VALIDATE_CODE'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('validate-code', 'App\\Api\\V1\\Controllers\\SmsValidationController@validateCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_LOAN_DOCUMENTS'), constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('loans/get-documents', 'App\\Api\\V1\\Controllers\\LoanController@getDocuments')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.EXPIRE_CODE'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->get('expire-validation-code', 'App\\Api\\V1\\Controllers\\SmsValidationController@expireCode')
            ->middleware('security_meta:'.join(',', $steps));

        $api->get('loans/fetch', 'App\\Api\\V1\\Controllers\\LoanController@fetchLoan');

        $api->get('fetch-cash-offices', 'App\\Api\\V1\\Controllers\\LoanController@fetchCashOfficesByPublicId');

        $api->post('loans/calculate-monthly-payment', 'App\\Api\\V1\\Controllers\\LoanController@calculateMonthlyPaymentRange');

        $api->post('loans/calculate-schedule', 'App\\Api\\V1\\Controllers\\LoanController@calculateScheduleDetails');

        $api->group(['prefix' => 'pay4me'], function (Router $api) {
            $api->post('session', 'App\\Api\\V1\\Controllers\\Pay4MeController@createSession');

            $api->get('session', 'App\\Api\\V1\\Controllers\\Pay4MeController@getSession');

            $api->get('status', 'App\\Api\\V1\\Controllers\\Pay4MeController@checkStatus');

            $api->get('details', 'App\\Api\\V1\\Controllers\\Pay4MeController@getDetails');
        });
    });

    if (config('app.env') !== 'production') {
        $api->post('datadog', 'App\\Api\\V1\\Controllers\\MonitoringController@createSecurity');
        $api->post('build-trigger', 'App\\Api\\V1\\Controllers\\MonitoringController@buildTrigger');
    }

    $api->group(['prefix' => 'pl'], function (Router $api) {
        $api->group(['middleware' => 'authorize_vendor'], function (Router $api) {
            $api->post('purchase', 'App\\Api\\V1\\Controllers\\PurchaseController@create');

            $api->post('get-payment', 'App\\Api\\V1\\Controllers\\PurchaseController@getPayment');

            $api->post('cancel-payment', 'App\\Api\\V1\\Controllers\\PurchaseController@cancelPayment');
        });

        $api->post('submit-document-pl', 'App\\Api\\V1\\Controllers\\PayLaterController@submitDocumentPL')
            ->middleware('security_entry')
            ->middleware('security_meta:'.constants('META_STEPS.SUBMIT_DOCUMENT_PL'));

        $steps = [constants('META_STEPS.IDENTITY_VERIFICATION_SMS'), constants('META_STEPS.SUBMIT_DOCUMENT_PL')];
        $api->get('verification-sms', 'App\\Api\\V1\\Controllers\\SecurityController@sendVerificationSms')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.VERIFY_IDENTITY'), constants('META_STEPS.IDENTITY_VERIFICATION_SMS')];
        $api->post('verify-sms', 'App\\Api\\V1\\Controllers\\SecurityController@verifySms')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.ALLOW_OPEN_VIDEO'), constants('META_STEPS.CREDENTIALS_VERIFIED')];
        $api->post('allow-open-video', 'App\\Api\\V1\\Controllers\\SecurityController@allowOpenVideo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.CREATE_STREAM_SESSION'), constants('META_STEPS.ALLOW_OPEN_VIDEO')];
        $api->get('archive/session', 'App\\Api\\V1\\Controllers\\SecurityController@startSession')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.START_STREAM'), constants('META_STEPS.CREATE_STREAM_SESSION')];
        $api->post('archive/start', 'App\\Api\\V1\\Controllers\\SecurityController@startRecording')
            ->middleware('security_meta:'.join(',', $steps));

        $api->post('archive/{archive_id}/stop', 'App\\Api\\V1\\Controllers\\SecurityController@stopRecording');

        $steps = [constants('META_STEPS.SUBMIT_DOCUMENT'), constants('META_STEPS.ALLOW_OPEN_VIDEO')];
        $api->post('check-liveness', 'App\\Api\\V1\\Controllers\\SecurityController@detectFace')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.SUBMIT_DOCUMENT')];
        $api->get('personal-info', 'App\\Api\\V1\\Controllers\\RegistrationController@getPersonalInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.FETCH_CITIZEN'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.SUBMIT_DOCUMENT')];
        $api->post('create-credit', 'App\\Api\\V1\\Controllers\\PayLaterController@createCredit')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('get-credit-info', 'App\\Api\\V1\\Controllers\\PayLaterController@getCreditInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('get-code', 'App\\Api\\V1\\Controllers\\PayLaterController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_LOAN_DOCUMENTS'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('get-documents', 'App\\Api\\V1\\Controllers\\PayLaterController@getDocuments')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->post('approve', 'App\\Api\\V1\\Controllers\\PayLaterController@approveCredit')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.SET_PASSWORD'), constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('password', 'App\\Api\\V1\\Controllers\\LoginController@setPassword')
            ->middleware('security_meta:'.join(',', $steps));

        $api->post('login', 'App\\Api\\V1\\Controllers\\LoginController@plLogin');
        $api->post('send-reset-email', 'App\\Api\\V1\\Controllers\\ForgotPasswordController@sendResetEmail');
        $api->post('try-reset-password', 'App\\Api\\V1\\Controllers\\ResetPasswordController@tryResetPassword');
        $api->post('has-reset-password-attempt', 'App\\Api\\V1\\Controllers\\ResetPasswordController@hasResetPasswordAttempt');
        $api->post('get-reset-code', 'App\\Api\\V1\\Controllers\\ResetPasswordController@getCode');
        $api->post('verify', 'App\\Api\\V1\\Controllers\\ResetPasswordController@verify');
        $api->post('reset-password', 'App\\Api\\V1\\Controllers\\ResetPasswordController@resetPasswordPhone');
        $api->post('logout', 'App\\Api\\V1\\Controllers\\LogoutController@logout');

        $api->group(['middleware' => ['jwt.auth', 'add_suuid_header_pl']], function (Router $api) {
            $api->post('credit', 'App\\Api\\V1\\Controllers\\PurchaseController@getCredit')
                ->middleware('check_availability_pl')
                ->middleware('pl_failure_callback');

            $api->post('make-purchase', 'App\\Api\\V1\\Controllers\\PurchaseController@purchase')
                ->middleware('pl_failure_callback');

            $api->post('payment-status', 'App\\Api\\V1\\Controllers\\PurchaseController@paymentStatus');

            $api->post('reject-purchase', 'App\\Api\\V1\\Controllers\\PurchaseController@rejectPurchase')
                ->middleware('pl_failure_callback');

            $api->get('credit-line-info', 'App\\Api\\V1\\Controllers\\PurchaseController@getCreditLineInfo');

            $api->get('transactions', 'App\\Api\\V1\\Controllers\\PurchaseController@getTransactions');

            $api->get('schedule', 'App\\Api\\V1\\Controllers\\PurchaseController@getSchedule');

            $api->get('user', 'App\\Api\\V1\\Controllers\\PurchaseController@user');
        });

        $api->post('/pay-later-callback', function (Request $request) {
            $order_id = $request->order_id;

            return redirect("/gc-view/pay-later/$order_id");
        });

        $api->post('/pay-later-callback/menu', function (Request $request) {
            $order_id = $request->order_id;

            return redirect("/gc-view/pay-later/menu/$order_id");
        });
    });

    $api->group(['prefix' => 'bnpl', 'middleware' => ['throttle:600,1', 'bnpl_down']], function (Router $api) {
        $telcell_bnpl_vendor_type = constants('VENDOR_TYPES.BNPL');
        $api->group(['middleware' => ["authorize_vendor:$telcell_bnpl_vendor_type"]], function (Router $api) {
            $api->get('terms', 'App\\Api\\V1\\Controllers\\BNPLController@terms');

            $api->get('client-state', 'App\\Api\\V1\\Controllers\\BNPLController@clientState');

            $api->get('credit-limit', 'App\\Api\\V1\\Controllers\\BNPLController@creditLimit')
                ->middleware('check_bnpl_status');

            $steps = [constants('META_STEPS.FETCH_CITIZEN')];
            $api->post('credit', '\\App\\Api\\V1\\Controllers\\BNPLController@createCredit')
                ->middleware('security_entry')
                ->middleware('check_bnpl_status')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_LOAN_DOCUMENTS')];
            $api->get('code', 'App\\Api\\V1\\Controllers\\SmsValidationController@getCode')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_CODE')];
            $api->post('approve', '\\App\\Api\\V1\\Controllers\\BNPLController@approve')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_PURCHASE'), constants('META_STEPS.FETCH_CITIZEN')];
            $api->get('purchase', 'App\\Api\\V1\\Controllers\\BNPLController@getPurchase')
                ->middleware('check_bnpl_status')
                ->middleware('check_availability_bnpl')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.MAKE_PURCHASE'), constants('META_STEPS.GET_PURCHASE')];
            $api->post('purchase', 'App\\Api\\V1\\Controllers\\BNPLController@purchase')
                ->middleware('check_bnpl_status')
                ->middleware('security_meta:'.join(',', $steps));
        });

        $api->post('process-loan-conversion', 'App\\Api\\V1\\Controllers\\BNPLController@processLoanConversion')
            ->middleware('throttle:500,1')
            ->middleware('internal_auth');
    });

    // BNPL purchase orders, loan application orders ovil creation via WEB
    $api->group(['prefix' => 'merchant', 'middleware' => ['jwt.auth']], function (Router $api) {
        $api->group(['prefix' => 'bnpl', 'middleware' => ['authorize_bnpl_merchant_agent']], function (Router $api) {
            $api->post('purchase-order', 'App\\Api\\V2\\Controllers\\InternalBNPLController@storePurchaseOrder');
            $api->post('has-pending-purchase-order', 'App\\Api\\V2\\Controllers\\InternalBNPLController@hasPendingPurchaseOrder');
            $api->get('purchase-orders', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getPurchaseOrders');
            $api->get('merchant-details', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getMerchantDetails');
        });

        $api->group(['prefix' => 'ovil', 'middleware' => ['authorize_ovil_merchant_agent']], function (Router $api) {
            $api->post('loan-application-order', 'App\\Api\\V1\\Controllers\\LoanApplicationOrderController@storeOrderDetails');
            $api->post('has-pending-application-order', 'App\\Api\\V1\\Controllers\\LoanApplicationOrderController@hasPendingApplicationOrder');
            $api->get('loan-application-orders', 'App\\Api\\V1\\Controllers\\LoanApplicationOrderController@getOrders');
            $api->get('merchant-details', 'App\\Api\\V1\\Controllers\\LoanApplicationOrderController@getMerchantDetails');
            $api->get('vehicles', 'App\\Api\\V1\\Controllers\\LoanApplicationOrderController@getVehicles');
        });
    });
});

// Idram routes naming convention is forced by legacy system
Route::group(['prefix' => '/idram/request'], function () {
    Route::group(['middleware' => ['oidl_down', 'check_wallet_response']], function () {
        Route::get('/terms', '\\App\\Api\\V1\\Controllers\\OIDLController@getTerms');

        Route::get('/index/id/{token}', '\\App\\Api\\V1\\Controllers\\OIDLController@confirmTerms')
            ->middleware('security_entry')
            ->middleware('security_meta:'.constants('META_STEPS.CONFIRM_TERMS'));

        $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.CONFIRM_TERMS')];
        Route::get('/get-loan', '\\App\\Api\\V1\\Controllers\\OIDLController@getLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_LOAN')];
        Route::post('/approve-loan', '\\App\\Api\\V1\\Controllers\\OIDLController@approveLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.IDRAM_AGREEMENT'), constants('META_STEPS.LOAN_APPROVAL')];
        Route::get('/agreement', '\\App\\Api\\V1\\Controllers\\OIDLController@getAgreement')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.IDRAM_CONTRACT'), constants('META_STEPS.IDRAM_AGREEMENT')];
        Route::get('/contract', '\\App\\Api\\V1\\Controllers\\OIDLController@getContract')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.IDRAM_CONTRACT')];
        Route::post('/get-verification-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.VALIDATE_CODE'), constants('META_STEPS.GET_CODE')];
        Route::post('/verify-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@validateCode')
            ->middleware('security_meta:'.join(',', $steps));

        Route::get('/confirmation/{status}', '\\App\\Api\\V1\\Controllers\\OIDLController@getConfirmation');
    });

    Route::get('/down', '\\App\\Api\\V1\\Controllers\\OIDLController@down')->name('oidl_down');
});

Route::group(['prefix' => '/telcell/request'], function () {
    Route::group(['middleware' => ['otcl_down', 'check_wallet_response']], function () {
        Route::get('/terms', '\\App\\Api\\V1\\Controllers\\OTCLController@getTerms');

        Route::get('/index/id/{token}', '\\App\\Api\\V1\\Controllers\\OTCLController@confirmTerms')
            ->middleware('security_entry')
            ->middleware('security_meta:'.constants('META_STEPS.CONFIRM_TERMS'));

        $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.CONFIRM_TERMS')];
        Route::get('/get-loan', '\\App\\Api\\V1\\Controllers\\OTCLController@getLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_LOAN')];
        Route::post('/approve-loan', '\\App\\Api\\V1\\Controllers\\OTCLController@approveLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TELCELL_AGREEMENT'), constants('META_STEPS.LOAN_APPROVAL')];
        Route::get('/agreement', '\\App\\Api\\V1\\Controllers\\OTCLController@getAgreement')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.TELCELL_CONTRACT'), constants('META_STEPS.TELCELL_AGREEMENT')];
        Route::get('/contract', '\\App\\Api\\V1\\Controllers\\OTCLController@getContract')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.TELCELL_CONTRACT')];
        Route::post('/get-verification-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.VALIDATE_CODE'), constants('META_STEPS.GET_CODE')];
        Route::post('/verify-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@validateCode')
            ->middleware('security_meta:'.join(',', $steps));

        Route::get('/confirmation/{status}', '\\App\\Api\\V1\\Controllers\\OTCLController@getConfirmation');
    });

    Route::group(['prefix' => '/app'], function () {
        Route::get('/terms-and-conditions', '\\App\\Api\\V1\\Controllers\\OWLAppController@app');

        Route::get('/loan/loan-amount/{token}', '\\App\\Api\\V1\\Controllers\\OWLAppController@confirmTerms')
            ->middleware([
                'security_entry',
                'security_meta:'.constants('META_STEPS.CONFIRM_TERMS'),
            ]);

        $steps = [constants('META_STEPS.STORE_REQUESTED_LOAN_AMOUNT'), constants('META_STEPS.CONFIRM_TERMS')];
        Route::post('/store-requested-details', '\\App\\Api\\V1\\Controllers\\OWLAppController@storeRequestedLoanDetails')
            ->middleware('security_meta:'.join(',', $steps))
            ->middleware('check_wallet_response');

        $steps = [constants('META_STEPS.SUBMIT_DOCUMENT'), constants('META_STEPS.STORE_REQUESTED_LOAN_AMOUNT')];
        Route::get('/compose-citizen-info', '\\App\\Api\\V1\\Controllers\\OWLAppController@composeCitizenInfo')
            ->middleware('security_meta:'.join(',', $steps))
            ->middleware([
                'security_meta:'.constants('META_STEPS.GET_LOAN'),
            ]);

        Route::get('/{path}', [
            'uses' => '\\App\\Api\\V1\\Controllers\\OWLAppController@app',
            'where' => ['path' => '.*'],
        ]);
    });

    Route::get('/fetch-terms-info', '\\App\\Api\\V1\\Controllers\\OWLAppController@getTermsInfo')->middleware(['response_formatter']);

    Route::get('/down', '\\App\\Api\\V1\\Controllers\\OTCLController@down')->name('otcl_down');
});

Route::group(['prefix' => '/easypay/request'], function () {
    Route::group(['middleware' => ['oepl_down', 'check_wallet_response']], function () {
        Route::get('/terms', '\\App\\Api\\V1\\Controllers\\OEPLController@getTerms');

        Route::get('/index/id/{token}', '\\App\\Api\\V1\\Controllers\\OEPLController@confirmTerms')
            ->middleware('security_entry')
            ->middleware('security_meta:'.constants('META_STEPS.CONFIRM_TERMS'));

        $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.CONFIRM_TERMS')];
        Route::get('/get-loan', '\\App\\Api\\V1\\Controllers\\OEPLController@getLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_LOAN')];
        Route::post('/approve-loan', '\\App\\Api\\V1\\Controllers\\OEPLController@approveLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.EASYPAY_AGREEMENT'), constants('META_STEPS.LOAN_APPROVAL')];
        Route::get('/agreement', '\\App\\Api\\V1\\Controllers\\OEPLController@getAgreement')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.EASYPAY_CONTRACT'), constants('META_STEPS.EASYPAY_AGREEMENT')];
        Route::get('/contract', '\\App\\Api\\V1\\Controllers\\OEPLController@getContract')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.EASYPAY_CONTRACT')];
        Route::post('/get-verification-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.VALIDATE_CODE'), constants('META_STEPS.GET_CODE')];
        Route::post('/verify-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@validateCode')
            ->middleware('security_meta:'.join(',', $steps));

        Route::get('/confirmation/{status}', '\\App\\Api\\V1\\Controllers\\OEPLController@getConfirmation');
    });

    Route::get('/down', '\\App\\Api\\V1\\Controllers\\OEPLController@down')->name('oepl_down');
});

Route::group(['prefix' => '/upay/request'], function () {
    Route::group(['middleware' => ['oupl_down', 'check_wallet_response']], function () {
        Route::get('/terms', '\\App\\Api\\V1\\Controllers\\OUPLController@getTerms');

        Route::get('/index/id/{token}', '\\App\\Api\\V1\\Controllers\\OUPLController@confirmTerms')
            ->middleware('security_entry')
            ->middleware('security_meta:'.constants('META_STEPS.CONFIRM_TERMS'));

        $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.CONFIRM_TERMS')];
        Route::get('/get-loan', '\\App\\Api\\V1\\Controllers\\OUPLController@getLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_LOAN')];
        Route::post('/approve-loan', '\\App\\Api\\V1\\Controllers\\OUPLController@approveLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.OUPL_AGREEMENT'), constants('META_STEPS.LOAN_APPROVAL')];
        Route::get('/agreement', '\\App\\Api\\V1\\Controllers\\OUPLController@getAgreement')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.OUPL_CONTRACT'), constants('META_STEPS.OUPL_AGREEMENT')];
        Route::get('/contract', '\\App\\Api\\V1\\Controllers\\OUPLController@getContract')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.OUPL_CONTRACT')];
        Route::post('/get-verification-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.VALIDATE_CODE'), constants('META_STEPS.GET_CODE')];
        Route::post('/verify-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@validateCode')
            ->middleware('security_meta:'.join(',', $steps));

        Route::get('/confirmation/{status}', '\\App\\Api\\V1\\Controllers\\OUPLController@getConfirmation');
    });

    Route::get('/down', '\\App\\Api\\V1\\Controllers\\OUPLController@down')->name('oupl_down');
});

Route::group(['prefix' => '/fastshift/request'], function () {
    Route::group(['middleware' => ['ofsl_down', 'check_wallet_response']], function () {
        Route::get('/terms', '\\App\\Api\\V1\\Controllers\\OFSLController@getTerms');

        Route::get('/index/id/{token}', '\\App\\Api\\V1\\Controllers\\OFSLController@confirmTerms')
            ->middleware('security_entry')
            ->middleware('security_meta:'.constants('META_STEPS.CONFIRM_TERMS'));

        $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.CONFIRM_TERMS')];
        Route::get('/get-loan', '\\App\\Api\\V1\\Controllers\\OFSLController@getLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_LOAN')];
        Route::post('/approve-loan', '\\App\\Api\\V1\\Controllers\\OFSLController@approveLoan')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.FASTSHIFT_AGREEMENT'), constants('META_STEPS.LOAN_APPROVAL')];
        Route::get('/agreement', '\\App\\Api\\V1\\Controllers\\OFSLController@getAgreement')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.FASTSHIFT_CONTRACT'), constants('META_STEPS.FASTSHIFT_AGREEMENT')];
        Route::get('/contract', '\\App\\Api\\V1\\Controllers\\OFSLController@getContract')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.FASTSHIFT_CONTRACT')];
        Route::post('/get-verification-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.VALIDATE_CODE'), constants('META_STEPS.GET_CODE')];
        Route::post('/verify-code', '\\App\\Api\\V1\\Controllers\\SmsValidationController@validateCode')
            ->middleware('security_meta:'.join(',', $steps));

        Route::get('/confirmation/{status}', '\\App\\Api\\V1\\Controllers\\OFSLController@getConfirmation');
    });

    Route::get('/down', '\\App\\Api\\V1\\Controllers\\OFSLController@down')->name('ofsl_down');
});

Route::group(['prefix' => '/velox', 'middleware' => 'vlx_down'], function () {
    Route::post('/loan-offer', '\\App\\Api\\V1\\Controllers\\VLXController@loanOffer')
        ->middleware('security_entry')
        ->middleware('security_meta:'.constants('META_STEPS.GET_LOAN'));

    $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.GET_LOAN')];
    Route::post('/loan-approve', '\\App\\Api\\V1\\Controllers\\VLXController@loanApprove')
        ->middleware('security_meta:'.join(',', $steps));

    Route::get('/status', '\\App\\Api\\V1\\Controllers\\VLXController@getLoanOfferStatus');
});

$api->version('v2', ['middleware' => ['response_formatter']], function (Router $api) {
    $api->post('push-notifications/store-user-device', 'App\\Api\\V2\\Controllers\\NotificationController@storeUserDevice');

    $api->group(['prefix' => 'settings'], function (Router $api) {
        $api->get('app-status', 'App\\Api\\V2\\Controllers\\SettingsController@getAppStatus');
        $api->post('add-notifiable-user', 'App\\Api\\V2\\Controllers\\SettingsController@notifiableUsers');
        $api->get('qr-discount', 'App\\Api\\V2\\Controllers\\SettingsController@getQrDiscount');
        $api->post('auth/add-notifiable-user', 'App\\Api\\V2\\Controllers\\SettingsController@notifiableUsers')->middleware('jwt.auth')->middleware('old_mobile_version');
    });
});

$api->version('v2', ['middleware' => ['response_formatter', 'app_down', 'activity_log', 'old_mobile_version']], function (Router $api) {
    $api->group(['prefix' => 'settings'], function (Router $api) {
        $api->get('server-time', 'App\\Api\\V2\\Controllers\\SettingsController@getServerTime');

        $api->get('app-version', 'App\\Api\\V2\\Controllers\\SettingsController@getAppVersion');
    });

    $api->group(['prefix' => 'auth'], function (Router $api) {
        $api->group(['middleware' => ['jwt.auth']], function (Router $api) {
            $api->post('mobile/refresh', 'App\\Api\\V2\\Controllers\\RefreshController@refresh');
            $api->post('logout', 'App\\Api\\V2\\Controllers\\LogoutController@logout');
        });

        $api->group(['middleware' => 'can_access_route'], function (Router $api) {
            $api->post('mobile/login', 'App\\Api\\V2\\Controllers\\LoginController@mobileLogin');

            $api->post('enroll-pin', 'App\\Api\\V2\\Controllers\\LoginController@storePin');

            $api->post('send-recovery-code', 'App\\Api\\V2\\Controllers\\ForgotPasswordController@sendRecoveryCode');

            $api->post('resend-recovery-code', 'App\\Api\\V2\\Controllers\\ForgotPasswordController@resendRecoveryCode');

            $api->post('submit-recovery-code', 'App\\Api\\V2\\Controllers\\ForgotPasswordController@submitRecoveryCode');

            $api->get('get-security-question', 'App\\Api\\V2\\Controllers\\ForgotPasswordController@getSecurityQuestion');

            $api->post('submit-security-answer', 'App\\Api\\V2\\Controllers\\ForgotPasswordController@submitSecurityAnswer');

            $api->post('reset-password', 'App\\Api\\V2\\Controllers\\ForgotPasswordController@resetPassword');
        });
    });

    $api->group(['prefix' => 'armed', 'middleware' => ['authorize_armed_vendor']], function (Router $api) {
        $api->post('details', 'App\\Api\\V2\\Controllers\\ArmedController@storePurchaseOrder');

        $api->get('transaction-status', 'App\\Api\\V2\\Controllers\\ArmedController@getPurchaseOrderStatus');
    });

    $api->group(['prefix' => 'internal-bnpl', 'middleware' => ['jwt.auth', 'authorize_internal_bnpl_vendor', 'mobile_loan_down']], function (Router $api) {
        $api->get('details', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getValidPurchaseOrder');

        $api->get('credit-limit', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getCreditLimit')
            ->middleware('check_bnpl_status');

        $steps = [constants('META_STEPS.FETCH_CITIZEN')];
        $api->post('credit', '\\App\\Api\\V2\\Controllers\\InternalBNPLController@createCredit')
            ->middleware('security_entry')
            ->middleware('check_bnpl_status')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('credit', '\\App\\Api\\V2\\Controllers\\InternalBNPLController@getCredit')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('personal-info', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getPersonalInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO')];
        $api->post('personal-info', 'App\\Api\\V2\\Controllers\\InternalBNPLController@updatePersonalInfo')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_LOAN_DOCUMENTS'), constants('META_STEPS.UPDATE_PERSONAL_INFO')];
        $api->get('documents', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getDocuments')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('code', 'App\\Api\\V2\\Controllers\\SmsValidationController@getCode')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.LOAN_APPROVAL')];
        $api->post('approve', '\\App\\Api\\V2\\Controllers\\InternalBNPLController@approve')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_PURCHASE'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('purchase', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getPurchase')
            ->middleware('check_bnpl_status')
            ->middleware('check_availability_internal_bnpl')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.MAKE_PURCHASE'), constants('META_STEPS.GET_PURCHASE')];
        $api->post('purchase', 'App\\Api\\V2\\Controllers\\InternalBNPLController@purchase')
            ->middleware('check_bnpl_status')
            ->middleware('security_meta:'.join(',', $steps));

        $steps = [constants('META_STEPS.GET_TRANSACTION'), constants('META_STEPS.MAKE_PURCHASE'), constants('META_STEPS.FETCH_CITIZEN')];
        $api->get('transaction', 'App\\Api\\V2\\Controllers\\InternalBNPLController@getTransactionDetails')
            ->middleware('security_meta:'.join(',', $steps));
    });

    $api->get('carousel-items', 'App\\Api\\V2\\Controllers\\MarketingController@getCarouselItems');

    $api->get('stories', 'App\\Api\\V2\\Controllers\\MarketingController@getStories');

    $api->get('categories', 'App\\Api\\V2\\Controllers\\CategoriesController@getCategories');

    $api->get('categories/{category_id}', 'App\\Api\\V2\\Controllers\\CategoriesController@getCategoryProducts');

    $api->get('products/{product_id}', 'App\\Api\\V2\\Controllers\\CategoriesController@getProductDetails');

    $api->post('help/support', 'App\\Api\\V2\\Controllers\\SupportController@requestSupport');

    $api->post('help/call', 'App\\Api\\V2\\Controllers\\SupportController@requestCall');

    $api->get('loan-repayment', 'App\\Api\\V2\\Controllers\\LoanRepaymentController@getLoanRepaymentDetails');

    $api->get('loan-repayment-form', 'App\\Api\\V2\\Controllers\\LoanRepaymentController@getLoanRepaymentForm')->middleware('jwt.check');

    $api->post('verification-sms', 'App\\Api\\V2\\Controllers\\SecurityController@sendVerificationSms')
        ->middleware('security_entry:'.constants('SECURITY_ENTRY.REGISTRATION'))
        ->middleware('security_meta:'.constants('META_STEPS.IDENTITY_VERIFICATION_SMS'));

    $steps = [constants('META_STEPS.RESEND_IDENTITY_VERIFICATION_SMS'), constants('META_STEPS.IDENTITY_VERIFICATION_SMS')];
    $api->get('resend-verification-sms', 'App\\Api\\V2\\Controllers\\SecurityController@resendVerificationSms')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.VERIFY_IDENTITY'), constants('META_STEPS.IDENTITY_VERIFICATION_SMS')];
    $api->post('verify-sms', 'App\\Api\\V2\\Controllers\\SecurityController@verifySms')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.STORE_EKENG_PHOTO')];
    $api->post('store-ekeng-photo', 'App\\Api\\V2\\Controllers\\SecurityController@storeEkengPhoto')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.ALLOW_OPEN_VIDEO'), constants('META_STEPS.CREDENTIALS_VERIFIED')];
    $api->post('allow-open-video', 'App\\Api\\V2\\Controllers\\SecurityController@allowOpenVideo')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.CHECK_LIVENESS'), constants('META_STEPS.ALLOW_OPEN_VIDEO')];
    $api->post('check-liveness', 'App\\Api\\V2\\Controllers\\SecurityController@detectFace')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.CREATE_STREAM_SESSION'), constants('META_STEPS.ALLOW_OPEN_VIDEO')];
    $api->get('archive/session', 'App\\Api\\V2\\Controllers\\SecurityController@startSession')
        ->middleware('security_meta:'.join(',', $steps));

    $steps = [constants('META_STEPS.START_STREAM'), constants('META_STEPS.CREATE_STREAM_SESSION')];
    $api->post('archive/start', 'App\\Api\\V2\\Controllers\\SecurityController@startRecording')
        ->middleware('security_meta:'.join(',', $steps));

    $api->post('archive/{archive_id}/stop', 'App\\Api\\V2\\Controllers\\SecurityController@stopRecording');

    $check_liveness = constants('META_STEPS.CHECK_LIVENESS');
    $im_id_registration = constants('META_STEPS.IM_ID_REGISTRATION');
    $steps = [constants('META_STEPS.SET_PASSWORD'), "[$check_liveness;$im_id_registration]"];
    $api->post('password', 'App\\Api\\V2\\Controllers\\LoginController@registerUser')
        ->middleware('security_meta:'.join(',', $steps));

    $api->group(['prefix' => 'imid'], function (Router $api) {
        $api->post('authenticate', 'App\\Api\\V2\\Controllers\\ImIdController@authentication');
        $api->post('check-status', 'App\\Api\\V2\\Controllers\\ImIdController@checkStatus');
        $api->post('registration-session', 'App\\Api\\V2\\Controllers\\ImIdController@createRegistrationSession')
            ->middleware('security_entry:'.constants('SECURITY_ENTRY.REGISTRATION'));

        $api->post('registration-details-update', 'App\\Api\\V2\\Controllers\\ImIdController@updateRegistrationDetails')
            ->middleware('security_meta:'.constants('META_STEPS.IM_ID_REGISTRATION'));
    });

    $api->group(['middleware' => 'jwt.auth'], function (Router $api) {
        // Crediting flow endpoints through Mobile APP
        $api->group(['middleware' => 'mobile_loan_down'], function (Router $api) {
            $api->post('store-requested-details', 'App\\Api\\V2\\Controllers\\CitizenController@storeRequestedLoanDetails')
                ->middleware('profile_update:'.constants('META_STEPS.PROFILE_UPDATE'))
                ->middleware('security_entry:'.constants('SECURITY_ENTRY.LOAN_APPLICATION'))
                ->middleware('security_meta:'.constants('META_STEPS.STORE_REQUESTED_LOAN_AMOUNT'));

            $steps = [constants('META_STEPS.SUBMIT_DOCUMENT'), constants('META_STEPS.STORE_REQUESTED_LOAN_AMOUNT')];
            $api->get('citizens', 'App\\Api\\V2\\Controllers\\CitizenController@show')
                ->middleware('security_meta:'.join(',', $steps))
                ->middleware('security_meta:'.constants('META_STEPS.FETCH_CITIZEN'));

            $steps = [constants('META_STEPS.TRADE_VEHICLE')];
            $api->get('trade-vehicle', 'App\\Api\\V2\\Controllers\\CitizenController@getTradeVehicleCredit')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.CURRENT_CONFIG'), constants('META_STEPS.SUBMIT_DOCUMENT')];
            $api->get('citizen/current/config', 'App\\Api\\V2\\Controllers\\LoanController@getConfigsForCitizen')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.LOAN_APPROVAL'), constants('META_STEPS.FETCH_CITIZEN')];
            $api->post('credit-flow/approve', 'App\\Api\\V2\\Controllers\\LoanController@approve')
                ->middleware('store_type')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_LOAN'), constants('META_STEPS.LOAN_APPROVAL')];
            $api->get('credit-flow/loan', 'App\\Api\\V2\\Controllers\\LoanController@show')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_PERSONAL_INFO'), constants('META_STEPS.LOAN_APPROVAL')];
            $api->get('credit-flow/personal-info', 'App\\Api\\V2\\Controllers\\LoanController@getPersonalInfo')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO')];
            $api->put('credit-flow/personal-info', 'App\\Api\\V2\\Controllers\\LoanController@updatePersonalInfo')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.FETCH_AGENT_SCHEDULES'), constants('META_STEPS.UPDATE_PERSONAL_INFO'), constants('META_STEPS.GET_PERSONAL_INFO')];
            $api->get('fetch-agent-schedules', 'App\\Api\\V2\\Controllers\\LoanController@fetchAgentSchedules')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.CAR_VERIFICATION'), constants('META_STEPS.FETCH_AGENT_SCHEDULES'), constants('META_STEPS.GET_PERSONAL_INFO')];
            $api->post('car-verification', 'App\\Api\\V2\\Controllers\\LoanController@storeCarVerificationInfo')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_CASH_OFFICES'), constants('META_STEPS.UPDATE_PERSONAL_INFO')];
            $api->get('cash-offices', 'App\\Api\\V2\\Controllers\\LoanController@getCashOffices')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_TRANSFER_TYPES'), constants('META_STEPS.UPDATE_PERSONAL_INFO')];
            $api->get('transfer-types', 'App\\Api\\V2\\Controllers\\LoanController@getTransferTypes')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.UPDATE_PERSONAL_INFO')];
            $api->post('credit-flow/cash-transfer', 'App\\Api\\V2\\Controllers\\LoanController@storeCashTransfer')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.UPDATE_PERSONAL_INFO')];
            $api->post('credit-flow/card-to-card-transfer', 'App\\Api\\V2\\Controllers\\LoanController@storeCardToCardTransfer')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.TRANSFER_INFO'), constants('META_STEPS.UPDATE_PERSONAL_INFO')];
            $api->post('credit-flow/manual-transfer', 'App\\Api\\V2\\Controllers\\LoanController@storeManualTransfer')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_CODE'), constants('META_STEPS.TRANSFER_INFO')];
            $api->get('get-code', 'App\\Api\\V2\\Controllers\\SmsValidationController@getCode')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.VALIDATE_CODE'), constants('META_STEPS.TRANSFER_INFO')];
            $api->post('validate-code', 'App\\Api\\V2\\Controllers\\SmsValidationController@validateCode')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.GET_LOAN_DOCUMENTS'), constants('META_STEPS.TRANSFER_INFO')];
            $api->get('credit-flow/get-documents', 'App\\Api\\V2\\Controllers\\LoanController@getDocuments')
                ->middleware('security_meta:'.join(',', $steps));

            $steps = [constants('META_STEPS.EXPIRE_CODE'), constants('META_STEPS.TRANSFER_INFO')];
            $api->get('expire-validation-code', 'App\\Api\\V2\\Controllers\\SmsValidationController@expireCode')
                ->middleware('security_meta:'.join(',', $steps));

            $api->get('credit-flow/fetch', 'App\\Api\\V2\\Controllers\\LoanController@fetchLoan');

            $api->get('fetch-cash-offices', 'App\\Api\\V2\\Controllers\\LoanController@fetchCashOfficesByPublicId');

            $api->post('credit-flow/calculate-monthly-payment', 'App\\Api\\V2\\Controllers\\LoanController@calculateMonthlyPaymentRange');

            $api->post('credit-flow/calculate-schedule', 'App\\Api\\V2\\Controllers\\LoanController@calculateScheduleDetails');

            $api->get('loan-application-order/details', 'App\\Api\\V1\\Controllers\\LoanApplicationOrderController@getValidLoanApplicationOrder');
        });

        $api->get('profile', 'App\\Api\\V2\\Controllers\\UserController@me');

        $api->post('update-email', 'App\\Api\\V2\\Controllers\\UserController@updateEmail');

        $api->get('loans', 'App\\Api\\V2\\Controllers\\LoanController@getAll');

        $api->get('loans/{contract_number}', 'App\\Api\\V2\\Controllers\\LoanController@getOne');

        $api->get('payments/{contract_number}', 'App\\Api\\V2\\Controllers\\LoanController@getPayments');

        $api->get('transactions/{contract_number}', 'App\\Api\\V2\\Controllers\\LoanController@getTransactions');

        $api->group(['prefix' => 'cards'], function (Router $api) {
            $api->post('/register-payment', 'App\\Api\\V2\\Controllers\\CreditCardController@registerPayment');
            $api->post('/get-urls', 'App\\Api\\V2\\Controllers\\CreditCardController@getUrls');
            $api->post('/payment-order-binding', 'App\\Api\\V2\\Controllers\\CreditCardController@paymentOrderBinding');
            $api->get('/', 'App\\Api\\V2\\Controllers\\CreditCardController@getCreditCards');
            $api->put('/', 'App\\Api\\V2\\Controllers\\CreditCardController@setPrimary');
            $api->delete('/', 'App\\Api\\V2\\Controllers\\CreditCardController@delete');
        });

        $api->group(['prefix' => 'applications'], function (Router $api) {
            $api->get('form-list', 'App\\Api\\V2\\Controllers\\ClientApplicationController@getApplicationFormList');
            $api->post('register', 'App\\Api\\V2\\Controllers\\ClientApplicationController@registerApplication');
            $api->get('status', 'App\\Api\\V2\\Controllers\\ClientApplicationController@getApplicationStatus');
        });

        $api->group(['prefix' => 'notification'], function (Router $api) {
            $api->get('/in-app/get-all', '\\App\\Api\\V2\\Controllers\\NotificationController@getInAppNotifications');
            $api->post('/in-app/mark-read', '\\App\\Api\\V2\\Controllers\\NotificationController@markAsRead');
        });
    });
});

$api->version('v2', ['middleware' => ['response_formatter', 'app_down', 'activity_log']], function (Router $api) {
    $api->group(['prefix' => 'client-application'], function (Router $api) {
        $api->get('check', 'App\\Api\\V2\\Controllers\\ClientApplicationController@check');
        $api->post('send-email', 'App\\Api\\V2\\Controllers\\ClientApplicationController@sendEmail');
        $api->post('send-sms', 'App\\Api\\V2\\Controllers\\ClientApplicationController@sendSms');
    });
});

// Used by Notifier and triggered when some In-app notification is created.
$api->version('v1', ['middleware' => ['throttle:600,1']], function (Router $api) {
    $api->post('notification/in-app/create', '\\App\\Api\\V2\\Controllers\\NotificationController@createInApp')->middleware('internal_auth');
});
