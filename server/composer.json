{"name": "francescomalatesta/laravel-api-boilerplate-jwt", "description": "An API Boilerplate to create a ready-to-use REST API in seconds.", "keywords": ["laravel", "api", "boilerplate", "jwt", "auth", "rest"], "license": "MIT", "type": "project", "require": {"php": ">=7.1.3", "ext-curl": "*", "ext-json": "*", "ext-openssl": "*", "ext-zip": "*", "ext-zlib": "*", "ampeco/nova-date-range-filter": "dev-master", "aobyte/gc-alerting": "^1.0", "artisaninweb/laravel-soap": "*******", "aws/aws-sdk-php": "^3.130", "barryvdh/laravel-dompdf": "0.8.5", "cdbeaton/boolean-tick": "dev-master", "chriskonnertz/string-calc": "^1.0", "dingo/api": "2.0.0-alpha1", "doctrine/dbal": "^2.9", "elasticsearch/elasticsearch": "^7.11", "epartment/nova-dependency-container": "^1.3", "fideloper/proxy": "^4.0", "flynsarmy/csv-seeder": "1.*", "friendsofphp/php-cs-fixer": "^2.16", "globalcredit/acra-monitoring": "*", "globalcredit/action-button": "*", "globalcredit/approve-arpi-solar-loan": "*", "globalcredit/approve-vehicle-loan": "*", "globalcredit/bank-report": "*", "globalcredit/cancel-button": "*", "globalcredit/deviated-schedules": "*", "globalcredit/evaluation-company-form": "*", "globalcredit/home": "*", "globalcredit/loan-expiration-card": "*", "globalcredit/media-view": "*", "globalcredit/payment-invoice": "*", "globalcredit/per-page-filter": "*", "globalcredit/reset-all-filter": "*", "globalcredit/rule-history": "*", "globalcredit/secret-key": "*", "globalcredit/settings": "*", "globalcredit/upload-card": "*", "guzzlehttp/guzzle": "^6.5", "hisorange/browser-detect": "3.1.6", "http-interop/http-factory-guzzle": "^1.1", "inspheric/nova-indicator-field": "^1.43", "intervention/image": "^2.5", "jumbojett/openid-connect-php": "^1.0", "jwadhams/json-logic-php": "^1.3", "koss-shtukert/laravel-nova-select2-auto-complete": "^1.21", "kristories/nova-qrcode-field": "^0.0.3", "laravel/framework": "5.7.*", "laravel/nova": "*", "laravel/tinker": "^1.0", "league/flysystem-aws-s3-v3": "^1.0", "league/pipeline": "0.3.0", "lifeonscreen/nova-sort-relations": "^0.0.2", "lstrojny/functional-php": "^1.9", "maatwebsite/excel": "^3.1", "maatwebsite/laravel-nova-excel": "1.1", "milon/barcode": "^5.3", "nyholm/psr7": "^1.6", "nesbot/carbon": "^1.36.2", "nyholm/effective-interest-rate": "^1.0", "opentok/opentok": "4.4.x", "php-smpp/php-smpp": "^1.2", "predis/predis": "^1.1", "rap2hpoutre/fast-excel": "^1.3", "rap2hpoutre/laravel-log-viewer": "^0.22.1", "simplesoftwareio/simple-qrcode": "~4", "sixlive/nova-text-copy-field": "^1.5", "smalot/pdfparser": "^0.16.1", "sofa/model-locking": "~5.3", "spatie/laravel-fractal": "^5.6", "spatie/laravel-permission": "^2.21", "titasgailius/search-relations": "dev-master#dc043261b954fca72b85ae208d52b27276385c30", "tymon/jwt-auth": "1.0.*", "vyuldashev/nova-permission": "^1.7", "webpatser/laravel-uuid": "^3.0"}, "require-dev": {"filp/whoops": "^2.0", "fzaninotto/faker": "^1.4", "laracasts/generators": "^1.1", "mockery/mockery": "^1.0", "nunomaduro/collision": "^2.0", "phpunit/phpunit": "^7.0"}, "autoload": {"classmap": ["database"], "psr-4": {"App\\": "app/"}, "files": ["bootstrap/helpers.php"]}, "autoload-dev": {"classmap": ["tests"], "psr-4": {"App\\": "tests/"}}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate", "php artisan jwt:secret -f"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": false, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "./nova"}, {"type": "path", "url": "./nova-components/Home"}, {"type": "path", "url": "./nova-components/ActionButton"}, {"type": "path", "url": "./nova-components/CancelButton"}, {"type": "path", "url": "./nova-components/ApproveVehicleLoan"}, {"type": "path", "url": "./nova-components/UploadCard"}, {"type": "vcs", "url": "https://github.com/ao-gayane/nova-date-range-filter"}, {"type": "path", "url": "./nova-components/PerPageFilter"}, {"type": "path", "url": "./nova-components/MediaView"}, {"type": "path", "url": "./nova-components/ApproveArpiSolarLoan"}, {"type": "path", "url": "./nova-components/BankReport"}, {"type": "path", "url": "./nova-components/SecretKey"}, {"type": "path", "url": "./nova-components/DeviatedSchedules"}, {"type": "path", "url": "./nova-components/RuleHistory"}, {"type": "path", "url": "./nova-components/AcraMonitoring"}, {"type": "path", "url": "./nova-components/LoanExpirationCard"}, {"type": "path", "url": "./nova-components/EvaluationCompanyForm"}, {"type": "vcs", "url": "**************:AOByte/gc-alerting.git"}, {"type": "path", "url": "./nova-components/Settings"}, {"type": "path", "url": "./nova-components/ResetAllFilter"}, {"type": "path", "url": "./nova-components/CustomLoadingCard"}, {"type": "path", "url": "./nova-components/PaymentInvoice"}]}