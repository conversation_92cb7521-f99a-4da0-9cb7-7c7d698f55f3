<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#000000">
    <title>Cash Me</title>

    <style>
        html {
            scroll-behavior: smooth;
            font-size: 14px;
            height: 100%;
            line-height: 1.15;
            -webkit-text-size-adjust: 100%;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'DejaVu Sans Book', 'Arial', sans-serif;
            overflow-x: hidden;
            min-width: 320px;
            background-color: #f7f7f9;
            font-size: 14px;
            line-height: 1.4285em;
            color: rgba(0,0,0,.87);
        }

        #renovation {
            position: inherit;
            width: 100%;
            background-color: #f7f7f9;
            overflow: hidden;
            box-sizing: border-box;
        }

        .bg_bottom_left{
            position: absolute;
            bottom: 0;
            left: 0;
            max-width: 100%;
            z-index: 1;
        }

        .bg_top_right{
            position: absolute;
            top: 0;
            right: 0;
            max-width: 100%;
            z-index: 1;
        }

        #renovation .phone {
            position: absolute;
            z-index: 2;
        }

        #renovation  .logo {
            position: absolute;
            z-index: 2;
            width: 270px;
            left: calc(44% + 15px);
            top: 25%;
        }

        #renovation .section {
            max-width: 700px;
            position: absolute;
            z-index: 2;
            left: 44%;
            top: calc(25% + 100px);
            padding: 15px;
            font-weight: bold;
        }

        #renovation .section .description {
            font-family: 'DejaVu Sans Book';
        }

        #renovation .section .description  .title {
            font-weight: bold;
            font-size: 24px;
            color: #c83d34;
        }

        #renovation .section .description  .text {
            font-size: 18px;
            color: rgba(0,0,0,.87);
        }

        #renovation .section .description  p {
            line-height: 1.4285em;
            margin-top: 0;
        }

        @media (orientation: landscape) {
            #renovation .phone {
                max-height: 80%;
                max-width: 40%;
                left: 5%;
                top: 50%;
                transform: translateY(-50%);
            }

            @media screen and (max-height: 600px) {
                #renovation  .logo {
                    width: 180px;
                    top: 7%;
                    left: calc(38% + 15px);
                }

                #renovation .section {
                    top: calc(7% + 50px);
                    left: 38%;
                }

                #renovation .section .description  .title {
                    font-size: 15px;
                    margin-bottom: 10px;
                }

                #renovation .section .description  .text {
                    font-size: 13px;
                    margin-bottom: 5px;
                }
            }
        }

        @media (orientation: portrait) {
            #renovation .phone {
                max-height: 60%;
                max-width: 80%;
                left: -1%;
                top: 5%;
            }

            #renovation  .logo {
                left: 50%;
                top: 10%;
                max-width: 48%;
            }

            #renovation .section {
                position: unset;
                text-align: left;
                bottom: 15px;
                top: auto;
                width: 100%;
                left: 0;
            }

            #renovation .section .description {
                position: absolute;
                z-index: 2;
                bottom: 40px;
            }

            @media screen and (max-width: 480px) {
                #renovation  .logo {
                    top: 5%;
                    width: 170px;
                }

                #renovation .section {
                    bottom: 15px;
                    width: 100%;
                }

                #renovation .section .description {
                    bottom: 20px;
                }

                #renovation .section .description  .title {
                    font-size: 16px;
                    margin-bottom: 10px;
                }

                #renovation .section .description  .text {
                    font-size: 14px;
                    margin-bottom: 5px;
                }
            }

            @media screen and (max-width: 350px) {
                #renovation .section .description  .title {
                    font-size: 14px;
                }

                #renovation .section .description  .text {
                    font-size: 12px;
                }
            }
        }
    </style>
</head>
<body>
    <div id="root">
        <div id="renovation">
            <img class="bg_bottom_left" src="images/renovation_bg_corner_left_bottom.png" alt="Background">
            <img class="bg_top_right" src="images/renovation_bg_corner_top_right.png" alt="Background">
            <img class="phone" src="images/renovation_phone.png" alt="Phone">
            <img class="logo" src="images/renovation_cashme_logo.svg" alt="Cashme logo">
            <div class="section">
                <div class="description">
                    <p class="title">Հարգելի հաճախորդ, հայցում ենք Ձեր ներողամտությունը:</p>
                    <p class="text">Հարթակը տեխնիկական սահմանափակման հետևանքով ժամանակավորապես անհասանելի է:</p>
                    <p class="text">Խնդրում ենք փորձել մի փոքր ուշ:</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
