.dropdown-menu > div > div.scroll-wrap {
  max-height: unset !important;
}

/* we hide it, to use our filter */
.dropdown-menu > div > div > div:last-child {
  display: none;
}

div[dusk='select-all-dropdown'] > div > div {
  width: 100% !important;
}

/*
  Do not show delete button when clicking all matching checkbox
*/
div[dusk="delete-menu"] {
  display: none !important;
}

/* 
  Roles and Permissions are used by default, because we show default filter
*/
div[dusk="roles-index-component"] .dropdown-menu > div > div > div:last-child {
  display: block !important;
}

div[dusk="permissions-index-component"] .dropdown-menu > div > div > div:last-child {
  display: block !important;
}
