body {
  line-height: 1.15;
}

h1 {
  font-size: 2rem;
  margin-block-start: 0.67em;
  margin-block-end: 0.67em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  font-weight: bold;
}

img {
  display: inline;
}

#capture-preview {
  background-color: white;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  padding: 50px;
}

#progress {
  width: 100%;
  background-color: #c6c6c6;
  border-radius: 10px;
  overflow: hidden;
}

#bar {
  width: 1%;
  height: 7px;
  background-color: #3cb0f7;
}

#bar.error {
  background-color: #ff7d8c;
}

#image-preview {
  max-width: 100%;
  max-height: 100%;
  height: auto;
  overflow: hidden;
  text-align: center;
  border-radius: 14px;
  flex: 1;
  margin-bottom: 50px;
}

#message {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  min-height: 1.25em;
  line-height: 1.25;
}

.hidden {
  display: none !important;
}
