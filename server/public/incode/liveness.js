/* eslint-disable no-console */
/* eslint-disable no-undef */
/* eslint-disable no-alert */
let onBoarding;
let session;

const id = 'camera-container';
const $message = document.getElementById('message');
const $capturePreview = document.getElementById('capture-preview');
const $bar = document.getElementById('bar');
const $imagePreview = document.getElementById('image-preview');
const $loader = document.getElementById('loader');

const toImage = base64 => `data:image/png;base64,${base64}`;

function createOnBoarding() {
  const apiKey = 'f35844c8bcb417be1a64fd6e8d622cc93b0fcfaa';
  const apiURL = 'https://stage-api.incodesmile.com/';
  const lang = 'en';
  onBoarding = OnBoarding.create({
    apiKey: apiKey,
    apiURL: apiURL,
    lang,
  });
}

function renderSelfieTutorial() {
  onBoarding.renderSelfieTutorial(id, {
    onSuccess: () => {
      renderSelfieCamera();
    },
  });
}

function renderSelfieCamera() {
  onBoarding.autodetect('selfie', id, {
    onSuccess: uploadSelfie,
  });
}

async function uploadSelfie({ encryptedImage, imageBase64 }) {
  try {
    $capturePreview.classList.remove('hidden');
    $imagePreview.src = toImage(imageBase64);
    const response = await onBoarding.capture({
      token: session.token,
      image: encryptedImage,
      type: onBoarding.selfieType,
      onProgress: percentage => ($bar.style.width = `${percentage}%`),
    });
    validateSelfie(response);
  } catch (e) {
    $bar.classList.add('error');
    $message.innerText =
      e.response?.data?.errorMessage ||
      e.response?.data?.message ||
      e.response?.data?.reason ||
      e.message;

    setTimeout(() => {
      // try again after threr seconds
      $capturePreview.classList.add('hidden');
      $bar.style.width = `0%`;
      $bar.classList.remove('error');
      $message.innerText = '';
      renderSelfieCamera();
    }, 3000);
  }
}

function validateSelfie({ confidence }) {
  const liveness = confidence < 0.6;
  if (liveness) {
    alert('Person is real');
  } else {
    alert('Spoof attemp detected');
  }
  window.location.reload();
}

function createSession() {
  return onBoarding.createSession('MX');
}

// document is ready
document.addEventListener('DOMContentLoaded', function() {
  createOnBoarding(); // initialize the instance
  createSession().then(_session => {
    session = _session;
    renderSelfieTutorial();
    // renderFrontTutorial(); // render and start autodetect of the front ID camera
  });
});
