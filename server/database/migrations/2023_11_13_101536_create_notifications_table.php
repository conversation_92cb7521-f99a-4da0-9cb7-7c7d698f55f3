<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('reference_id')->nullable();
            $table->string('recipient');
            $table->string('type');
            $table->string('subject')->nullable();
            $table->text('body')->nullable();
            $table->dateTime('read_at')->nullable();
            // We should avoid storing the same notification twice
            $table->unique(['reference_id', 'type']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notification');
    }
}
