<?php

use App\Models\Tag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TagsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('tags')->insert([
            [
                'name' => 'Վիդեոն բացակայում է',
                'type' => Tag::REJECT,
                'description' => 'Վիդեոն բացակայում է',
            ],
            [
                'name' => 'Միջնորդով',
                'type' => Tag::REJECT,
                'description' => 'Միջնորդով',
            ],
            [
                'name' => 'Օգտվողը անտեղյակ է',
                'type' => Tag::REJECT,
                'description' => 'Օգտվողը անտեղյակ է',
            ],
            [
                'name' => 'Կասկածելի',
                'type' => Tag::REJECT,
                'description' => 'Կասկածելի   ',
            ],
            [
                'name' => 'Վիդեոն բացակայում է, սակայն գործակալի նշույլներ չկան։',
                'type' => Tag::APPROVE,
                'description' => 'Վիդեոն բացակայում է',
            ],
            [
                'name' => 'Գլոբալի գրասենյակ',
                'type' => Tag::APPROVE,
                'description' => 'Գլոբալի գրասենյակ',
            ],
            [
                'name' => 'Բազմաթիվ դիմումներ',
                'type' => Tag::APPROVE,
                'description' => 'Բազմաթիվ դիմումներ',
            ],
        ]);
    }
}
