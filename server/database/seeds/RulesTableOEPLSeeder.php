<?php

namespace Database\Seeders;

class RulesTableOEPLSeeder extends AbstractRulesTableSeeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $oepl_rules = [
            [
                'name' => 'walletEmail',
                'label' => 'walletEmail',
                'formula' => json_encode(['==' => [['var' => 'walletEmail'], '']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'citizenship',
                'label' => 'citizenship',
                'formula' => json_encode(['!=' => [['var' => 'citizenship'], 'ARM']]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasAddress',
                'label' => 'hasAddress',
                'formula' => json_encode(['==' => [['var' => 'hasAddress'], false]]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
            ],
            [
                'name' => 'isDead',
                'label' => 'isDead',
                'formula' => json_encode(['==' => [['var' => 'isDead'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'age',
                'label' => 'age',
                'formula' => json_encode(['or' => [['<' => [['var' => 'age'], 21]], ['>' => [['var' => 'age'], 65]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletUserDuration',
                'label' => 'walletUserDuration',
                'formula' => json_encode(['<' => [['var' => 'walletUserDuration'], 91]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'loanClass',
                'label' => 'loanClass',
                'formula' => json_encode(['and' => [['!=' => [['var' => 'loanClass'], 'Ստանդարտ']], ['!=' => [['var' => 'loanClass'], null]]]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasOverdueLoans',
                'label' => 'hasOverdueLoans',
                'formula' => json_encode(['==' => [['var' => 'hasOverdueLoans'], true]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasNonSyncedCredits',
                'label' => 'hasNonSyncedCredits',
                'formula' => json_encode(['==' => [['var' => 'hasNonSyncedCredits'], true]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'delayQuantityInYear',
                'label' => 'delayQuantityInYear',
                'formula' => json_encode(['>' => [['var' => 'delayQuantityInYear'], 31]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletAmount',
                'label' => 'walletAmount_20000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 20000]]),
                'amount' => '20000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletAmount',
                'label' => 'walletAmount_40000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 40000]]),
                'amount' => '40000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletAmount',
                'label' => 'walletAmount_80000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 80000]]),
                'amount' => '80000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletAmount',
                'label' => 'walletAmount_100000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 100000]]),
                'amount' => '100000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletAmount',
                'label' => 'walletAmount_150000',
                'formula' => json_encode(['==' => [['var' => 'walletAmount'], 150000]]),
                'amount' => '150000',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.02_walletLoansCount_0',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>=' => [['var' => 'drScore'], 0]],
                            ['<=' => [['var' => 'drScore'], 0.02]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '40000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.02_walletLoansCount_1_4',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>=' => [['var' => 'drScore'], 0]],
                            ['<=' => [['var' => 'drScore'], 0.02]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '150000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0_0.02_walletLoansCount_5',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>=' => [['var' => 'drScore'], 0]],
                            ['<=' => [['var' => 'drScore'], 0.02]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '150000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.02_0.05_walletLoansCount_0',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.02]],
                            ['<=' => [['var' => 'drScore'], 0.05]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '40000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.02_0.05_walletLoansCount_1_4',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.02]],
                            ['<=' => [['var' => 'drScore'], 0.05]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '100000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.02_0.05_walletLoansCount_5',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.02]],
                            ['<=' => [['var' => 'drScore'], 0.05]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '150000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_walletLoansCount_0',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.05]],
                            ['<=' => [['var' => 'drScore'], 0.1]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '20000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_walletLoansCount_1_4',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.05]],
                            ['<=' => [['var' => 'drScore'], 0.1]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '80000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.05_0.1_walletLoansCount_5',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.05]],
                            ['<=' => [['var' => 'drScore'], 0.1]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '100000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.165_walletLoansCount_0',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.1]],
                            ['<=' => [['var' => 'drScore'], 0.165]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '20000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.165_walletLoansCount_1_4',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.1]],
                            ['<=' => [['var' => 'drScore'], 0.165]],
                            ['>=' => [['var' => 'walletLoansCount'], 1]],
                            ['<=' => [['var' => 'walletLoansCount'], 4]],
                        ],
                    ]),
                'amount' => '80000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.1_0.165_walletLoansCount_5',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.1]],
                            ['<=' => [['var' => 'drScore'], 0.165]],
                            ['>=' => [['var' => 'walletLoansCount'], 5]],
                        ],
                    ]),
                'amount' => '100000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_0.165_0.4_walletLoansCount_0',
                'formula' => json_encode(
                    [
                        'and' => [
                            ['>' => [['var' => 'drScore'], 0.165]],
                            ['<=' => [['var' => 'drScore'], 0.4]],
                            ['==' => [['var' => 'walletLoansCount'], 0]],
                        ],
                    ]),
                'amount' => '20000',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_lower_0_greater_0.4_newCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['==' => [['var' => 'walletLoansCount'], 0]]]],
                        ['or' => [
                            ['>' => [['var' => 'drScore'], 0.4]],
                            ['<' => [['var' => 'drScore'], 0]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'drScore',
                'label' => 'dr_score_lower_0_greater_0.165_oldCustomer',
                'formula' => json_encode(
                    ['if' => [['and' => [['>' => [['var' => 'walletLoansCount'], 0]]]],
                        ['or' => [
                            ['>' => [['var' => 'drScore'], 0.165]],
                            ['<' => [['var' => 'drScore'], 0]],
                        ]],
                        false,
                    ],
                    ]
                ),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'acraStatus',
                'label' => 'acraStatus',
                'formula' => json_encode(['==' => [['var' => 'acraStatus'], '2']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'isQKH',
                'label' => 'isQKH',
                'formula' => json_encode(['==' => [['var' => 'isQKH'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletActiveLoansBalance',
                'label' => 'walletActiveLoansBalance',
                'formula' => true,
                'amount' => json_encode(['var' => 'walletActiveLoansBalance']),
                'duration' => 12,
                'resolvable' => false,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletMaxAmount',
                'label' => 'loanAmount',
                'formula' => true,
                'amount' => 'iflt($drScore - $walletActiveLoansBalance, 1000, 0, $drScore - $walletActiveLoansBalance)',
                'duration' => 12,
                'resolvable' => false,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'walletAvailableAmount',
                'label' => 'walletAvailableAmount',
                'formula' => true,
                'amount' => 'min($walletAmount, $walletMaxAmount)',
                'duration' => 12,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
        ];

        $this->seed($oepl_rules, constants('LOAN_TYPES.OEPL'));
    }
}
