<?php

use App\Models\Loan;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Seeder;

class LoanExpirationDateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $payment_service = resolve('App\Interfaces\IPaymentService');

        $loans = Loan::where('loan_type_id', constants('LOAN_TYPES.OASL'))
            ->whereIn('status', [Loan::PROCESSING, Loan::CONFIRMED])
            ->whereNotNull('sign_date')
            ->whereHas('solar_panel', function (Builder $query) {
                $query->whereNull('expiration_date');
            })
            ->get();

        foreach ($loans as $loan) {
            $days = $payment_service->getPaymentExpirationDays($loan);

            $loan->solar_panel->update([
                'expiration_date' => $loan->sign_date->addDays($days),
            ]);
        }
    }
}
