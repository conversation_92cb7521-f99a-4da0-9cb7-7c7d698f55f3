<?php

use App\Models\Paymentable;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentableTableSeeder extends Seeder
{
    const CHUNK_SIZE = 1000;

    const PAYMENT_STATUSES = [
        'PAYMENT_PENDING' => Paymentable::PAYMENT_PENDING,
        'PAYMENT_PROCESSING' => Paymentable::PAYMENT_PROCESSING,
        'PAYMENT_PROCESSED' => Paymentable::PAYMENT_PROCESSED,
        'PAYMENT_FAILED' => Paymentable::PAYMENT_FAILED,
        'PAYMENT_EXPIRED' => Paymentable::PAYMENT_EXPIRED,
    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('loans')
            ->whereNotNull('payment_type')
            ->whereNotNull('payment_id')
            ->orderBy('id')
            ->chunk(self::CHUNK_SIZE, function ($loans) {
                Log::info('Paymentable seeder. Chunk start. Loans id ', [$loans[0]->id]);
                foreach ($loans as $loan) {
                    try {
                        if (!Paymentable::where('loan_id', $loan->id)->exists()) {
                            DB::table('paymentables')->insert([
                                [
                                    'loan_id' => $loan->id,
                                    'paymentable_type' => $loan->payment_type,
                                    'paymentable_id' => $loan->payment_id,
                                ],
                            ]);

                            if ($loan->payment_status != null || $loan->payment_status != '') {
                                $type = $loan->payment_type;
                                $status = self::PAYMENT_STATUSES[$loan->payment_status];
                                $type::where('id', $loan->payment_id)->update(['payment_status' => $status]);
                            }
                        }
                    } catch (Exception $e) {
                        Log::error('Could not get loan ', ['loan id' => $loan->id, 'error' => $e->getMessage()]);
                        throw $e;
                    }
                }
            });
    }
}
