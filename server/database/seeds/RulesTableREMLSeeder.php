<?php

namespace Database\Seeders;

class RulesTableREMLSeeder extends AbstractRulesTableSeeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $reml_rules = [
            [
                'name' => 'isDead',
                'label' => 'isDead',
                'formula' => json_encode(['==' => [['var' => 'isDead'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'age',
                'label' => 'age',
                'formula' => json_encode(['and' => [
                    ['or' => [['<' => [['var' => 'age'], 21]], ['>' => [['var' => 'age'], 60]]]], ],
                ]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'netSalary',
                'label' => 'netSalary',
                'formula' => json_encode(['==' => [['var' => 'netSalary'], 0]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'citizenship',
                'label' => 'citizenship',
                'formula' => json_encode(['if' => [['in' => [['var' => 'citizenship'], 'ARM']], false, true]]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'hasAddress',
                'label' => 'hasAddress',
                'formula' => json_encode(['==' => [['var' => 'hasAddress'], false]]),
                'amount' => '0',
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'interestRate',
                'label' => 'interestRate',
                'formula' => true,
                'rate' => json_encode(['if' => [
                    ['==' => [['var' => 'loanSubTypeId'], 2]], ['if' => [
                        ['<=' => [['var' => 'realEstatePrice'], 35000000]], 12.5, 13, ],
                    ], ['if' => [
                        ['and' => [
                                ['==' => [['var' => 'premlSubType'], 2]],
                                ['<=' => [['var' => 'realEstatePrice'], 35000000]],
                            ],
                        ], 11, 12, ],
                    ],
                ]]),
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'serviceFeeRate',
                'label' => 'serviceFeeRate',
                'formula' => true,
                'service_fee_rate' => 0.3,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'delayQuantityInYear',
                'label' => 'delayQuantityInYear',
                'formula' => json_encode(['>' => [['var' => 'delayQuantityInYear'], 30]]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'isQKH',
                'label' => 'isQKH',
                'formula' => json_encode(['==' => [['var' => 'isQKH'], 'true']]),
                'amount' => '0',
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 1,
            ],
            [
                'name' => 'calculatedMaxDuration',
                'label' => 'calculatedMaxDuration',
                'formula' => true,
                'duration' => json_encode(['if' => [['>=' => [['var' => 'allowedDurationREML'], ['var' => 'REMLDuration']]], ['var' => 'REMLDuration'], 0]]),
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            [
                'name' => 'isAllowedDuration',
                'label' => 'isAllowedDuration',
                'formula' => json_encode(['if' => [['>=' => [['var' => 'allowedDurationREML'], ['var' => 'REMLDuration']]], false, true]]),
                'amount' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            [
                'name' => 'finalAmount',
                'label' => 'finalAmount',
                'formula' => true,
                'amount' => json_encode(['if' => [
                    ['or' => [
                    ['<' => [['var' => 'realEstatePrice'], 2230000]],
                    ['>' => [['var' => 'realEstatePrice'], 90000000]],
                ]], 0, ['var' => 'realEstatePrice'], ]]),
                'resolvable' => true,
                'disabled' => false,
                'phase' => 2,
            ],
            [
                'name' => 'mortgageSolvency',
                'label' => 'mortgageSolvency',
                'formula' => json_encode(['<=' => [['var' => 'mortgageSolvency'], 0]]),
                'amount' => 0,
                'duration' => 0,
                'resolvable' => true,
                'disabled' => false,
                'phase' => 3,
            ],
        ];

        $this->seed($reml_rules, constants('LOAN_TYPES.REML'));
    }
}
