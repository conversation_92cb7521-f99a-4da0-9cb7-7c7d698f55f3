<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MobileScreenPathsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('mobile_screen_paths')->insert([
            [
                'name' => 'MARKETING_SCREEN',
                'navigation_path' => 'MAIN_NAVIGATOR/MARKETING_SCREEN',
            ],
            [
                'name' => 'FINANCE_SCREEN',
                'navigation_path' => 'MAIN_NAVIGATOR/FINANCE_SCREEN',
            ],
            [
                'name' => 'HELP_SCREEN',
                'navigation_path' => 'MAIN_NAVIGATOR/HELP_SCREEN',
            ],
            [
                'name' => 'SIG<PERSON>_IN_SCREEN',
                'navigation_path' => 'AUTH_NAVIGATOR/SIGN_IN_SCREEN',
            ],
            [
                'name' => 'SIGN_UP_SCREEN',
                'navigation_path' => 'AUTH_NAVIGATOR/SIGN_UP_SCREEN',
            ],
            [
                'name' => 'FORGOT_PASSWORD_SCREEN',
                'navigation_path' => 'AUTH_NAVIGATOR/FORGOT_PASSWORD_SCREEN',
            ],
            [
                'name' => 'APPLICATION_SCREEN',
                'navigation_path' => 'ACCOUNT_NAVIGATOR/APPLICATION_SCREEN',
            ],
            [
                'name' => 'CLIENT_APPLICATION_SCREEN',
                'navigation_path' => 'ACCOUNT_NAVIGATOR/CLIENT_APPLICATION_SCREEN',
            ],
            [
                'name' => 'NOTIFICATION_SCREEN',
                'navigation_path' => 'ACCOUNT_NAVIGATOR/NOTIFICATION_SCREEN',
            ],
            [
                'name' => 'MY_LOANS_SCREEN',
                'navigation_path' => 'ACCOUNT_NAVIGATOR/MY_LOANS_SCREEN',
            ],
            [
                'name' => 'PROFILE_SCREEN',
                'navigation_path' => 'ACCOUNT_NAVIGATOR/PROFILE_SCREEN',
            ],
            [
                'name' => 'TERMS_SCREEN',
                'navigation_path' => 'ACCOUNT_NAVIGATOR/TERMS_SCREEN',
            ],
            [
                'name' => 'PAYMENT_FORM_SCREEN',
                'navigation_path' => 'PAYMENT_NAVIGATOR/PAYMENT_FORM_SCREEN',
            ],
        ]);
    }
}
