<?php

use App\Models\LoanType;
use App\Models\TransferType;
use Illuminate\Database\Seeder;

class TransferTypesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $ocl_loan = LoanType::find(constants('LOAN_TYPES.OCL'));
        $ovl_loan = LoanType::find(constants('LOAN_TYPES.OVL'));
        $oiql_loan = LoanType::find(constants('LOAN_TYPES.OIQL'));
        $oasl_loan = LoanType::find(constants('LOAN_TYPES.OASL'));
        $vlx_loan = LoanType::find(constants('LOAN_TYPES.VLX'));
        $oiwl_loan = LoanType::find(constants('LOAN_TYPES.OIWL'));

        $transfer_type = TransferType::create([
            'name' => 'cash_payment',
            'limit' => null,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($ocl_loan, ['disabled' => false]);
        $transfer_type->loan_types()->attach($ovl_loan, ['disabled' => false]);
        $transfer_type->loan_types()->attach($oiwl_loan, ['disabled' => false]);

        $transfer_type = TransferType::create([
            'name' => 'idram_wallet',
            'limit' => 1000000,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($ocl_loan, ['disabled' => true]);
        $transfer_type->loan_types()->attach($ovl_loan, ['disabled' => true]);

        $transfer_type = TransferType::create([
            'name' => 'card_to_card',
            'limit' => null,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($ocl_loan, ['disabled' => false]);
        $transfer_type->loan_types()->attach($ovl_loan, ['disabled' => true]);
        $transfer_type->loan_types()->attach($oiwl_loan, ['disabled' => false]);

        $transfer_type = TransferType::create([
            'name' => 'wire_transfer',
            'limit' => null,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($ocl_loan, ['disabled' => true]);
        $transfer_type->loan_types()->attach($ovl_loan, ['disabled' => true]);

        $transfer_type = TransferType::create([
            'name' => 'easypay_wallet',
            'limit' => null,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($ocl_loan, ['disabled' => true]);
        $transfer_type->loan_types()->attach($ovl_loan, ['disabled' => true]);

        $transfer_type = TransferType::create([
            'name' => 'product_provision',
            'limit' => null,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($oiql_loan, ['disabled' => false]);
        $transfer_type->loan_types()->attach($oasl_loan, ['disabled' => false]);

        $transfer_type = TransferType::create([
            'name' => 'velox_transfer',
            'limit' => null,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($vlx_loan, ['disabled' => false]);

        $transfer_type = TransferType::create([
            'name' => 'manual_transfer',
            'limit' => null,
            'percentage' => 0,
        ]);
        $transfer_type->loan_types()->attach($ovl_loan, ['disabled' => false]);
    }
}
