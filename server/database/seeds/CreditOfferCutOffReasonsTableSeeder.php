<?php

use App\Models\CreditOfferCutOffReason;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CreditOfferCutOffReasonsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('credit_offer_cut_off_reasons')->insert([
            [
                'name' => CreditOfferCutOffReason::CITIZEN_HAS_OVERDUE_CREDIT,
            ],
            [
                'name' => CreditOfferCutOffReason::CITIZEN_HAS_GUARANTEE_OVERDUE_DAYS,
            ],
            [
                'name' => CreditOfferCutOffReason::CITIZEN_HAS_NOT_STANDARD_HC_LOAN_CLASS,
            ],
            [
                'name' => CreditOfferCutOffReason::CITIZEN_HAS_PAYMENT_ON_APPLICATION_DAY,
            ],
            [
                'name' => CreditOfferCutOffReason::CITIZEN_IN_HC_BLACKLIST,
            ],
        ]);
    }
}
