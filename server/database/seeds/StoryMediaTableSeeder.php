<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StoryMediaTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('story_media')->insert([
            [
                'logo' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'prefix' => null,
                'title' => 'Արագ գումար',
                'description' => 'Օրվա ցանկացած ժամի, 0% տոկոսադրույքով',
                'button_text' => 'Կարդալ ավելին',
                'image_src' => '/assets/pallaton/stories/story-1.png',
                'background_image_src' => '/assets/pallaton/carousel/cash-me.png',
                'navigator' => 'LOAN_NAVIGATOR',
                'screen' => 'GET_IT_NOW_OCL_SCREEN',
                'screen_title' => 'Սպառողական վարկ',
                'loan_type_id' => 1,
                'is_trading' => false,
                'auth_required' => false,
            ],
            [
                'logo' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'prefix' => null,
                'title' => 'Ավտոմեքենայի գրավադրում',
                'description' => 'Կարող եք դիմել օրվա ցանկացած ժամի',
                'button_text' => 'Կարդալ ավելին',
                'image_src' => '/assets/pallaton/stories/story-2.jpg',
                'background_image_src' => '/assets/pallaton/carousel/vehicle-trading.jpg',
                'navigator' => 'LOAN_NAVIGATOR',
                'screen' => 'GET_IT_NOW_OVL_SCREEN',
                'screen_title' => 'Սպառողական վարկ',
                'loan_type_id' => 2,
                'is_trading' => false,
                'auth_required' => false,
            ],
            [
                'logo' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'prefix' => null,
                'title' => 'Ավտոմեքենայի ձեռքբերում',
                'description' => 'Մեր մասնագետն անձամբ է մոտենում Ձեզ հարմար վայր, Ձեզ հարմար ժամի',
                'button_text' => 'Կարդալ ավելին',
                'image_src' => '/assets/pallaton/stories/story-3.jpg',
                'background_image_src' => '/assets/pallaton/carousel/travel.jpg',
                'navigator' => 'LOAN_NAVIGATOR',
                'screen' => 'GET_IT_NOW_OVL_SCREEN',
                'screen_title' => 'Ավտոմեքենայի ձեռքբերում',
                'loan_type_id' => 2,
                'is_trading' => true,
                'auth_required' => false,
            ],
            [
                'logo' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'prefix' => null,
                'title' => 'Մաս-մաս',
                'description' => 'Վճարում ես միայն ապրանքի/ծառայության համար, քո ընտրած ժամանակահատվածում՝ մաս֊մաս',
                'button_text' => 'Կարդալ ավելին',
                'image_src' => '/assets/pallaton/stories/story-7.png',
                'background_image_src' => '/assets/pallaton/carousel/bnpl.jpg',
                'navigator' => 'LOAN_NAVIGATOR',
                'screen' => 'GET_IT_NOW_BNPL_SCREEN',
                'screen_title' => null,
                'loan_type_id' => null,
                'is_trading' => false,
                'auth_required' => false,
            ],
            [
                'logo' => env('APP_URL').'/assets/pallaton/carousel/cashme-white.svg',
                'prefix' => null,
                'title' => 'Անշարժ գույքի գրավադրում',
                'description' => 'Ձեռք բերեք բնակարան ցանկացած պահի',
                'button_text' => 'Կարդալ ավելին',
                'image_src' => '/assets/pallaton/stories/story-4.jpg',
                'background_image_src' => '/assets/pallaton/carousel/cash-me.png',
                'navigator' => null,
                'screen' => null,
                'screen_title' => null,
                'loan_type_id' => null,
                'is_trading' => false,
                'auth_required' => false,
            ],
        ]);
    }
}
