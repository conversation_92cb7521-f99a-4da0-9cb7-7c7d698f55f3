<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class VendorsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('vendors')->insert([
            [
                'id' => 1,
                'client_identifier' => 'nike_id',
                'client_secret' => Hash::make('nike_secret'),
                'name' => 'NIKE',
                'duration' => 3,
                'limit' => 200000,
                'transaction_min_amount' => 2000,
                'account_number' => '***********',
                'phone_number' => '+***********',
                'cancelation_day_limit' => 14,
                'type' => constants('VENDOR_TYPES.PAY_LATER'),
            ],
            [
                'id' => 2,
                'client_identifier' => 'menu_id',
                'client_secret' => Hash::make('menu_secret'),
                'name' => 'MENU.AM',
                'duration' => 1,
                'limit' => 50000,
                'transaction_min_amount' => 1000,
                'account_number' => '***********',
                'phone_number' => '+***********',
                'cancelation_day_limit' => 14,
                'type' => constants('VENDOR_TYPES.PAY_LATER'),
            ],
            [
                'id' => 3,
                'client_identifier' => 'internal_id',
                'client_secret' => Hash::make('internal_secret'),
                'name' => 'INTERNAL',
                'duration' => 3,
                'limit' => 1200000,
                'transaction_min_amount' => 72000,
                'account_number' => null,
                'phone_number' => '+***********',
                'cancelation_day_limit' => null,
                'type' => constants('VENDOR_TYPES.INTERNAL_BNPL'),
            ],
            [
                'id' => 4,
                'client_identifier' => 'telcell_id',
                'client_secret' => Hash::make('telcell_secret'),
                'name' => 'TELCELL',
                'duration' => 3,
                'limit' => null,
                'transaction_min_amount' => null,
                'account_number' => '***********',
                'phone_number' => '+***********',
                'cancelation_day_limit' => 14,
                'type' => constants('VENDOR_TYPES.BNPL'),
            ],
            [
                'id' => 5,
                'client_identifier' => 'armed_id',
                'client_secret' => Hash::make('armed_secret'),
                'name' => 'ARMED',
                'duration' => 3,
                'limit' => 1200000,
                'transaction_min_amount' => 72000,
                'account_number' => null,
                'phone_number' => '+***********',
                'cancelation_day_limit' => null,
                'type' => constants('VENDOR_TYPES.ARMED'),
            ],
            [
                'id' => 6,
                'client_identifier' => 'vehicle_import_id',
                'client_secret' => Hash::make('vehicle_import_secret'),
                'name' => 'VEHICLE_IMPORT',
                'duration' => 3,
                'limit' => null,
                'transaction_min_amount' => null,
                'account_number' => '***********',
                'phone_number' => '+***********',
                'cancelation_day_limit' => null,
                'type' => constants('VENDOR_TYPES.VEHICLE_IMPORT'),
            ],
        ]);
    }
}
