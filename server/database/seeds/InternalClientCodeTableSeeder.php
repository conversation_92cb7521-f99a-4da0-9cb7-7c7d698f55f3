<?php

use App\Models\HC\HcGCCLIENTS;
use App\Models\LoanSecurity;
use Illuminate\Database\Seeder;

class InternalClientCodeTableSeeder extends Seeder
{
    const CHUNK_SIZE = 300;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Log::info('Start to insert local client info');
        $this->getOrCreateClientBySsnAndClientType();
        Log::info('Inserted local client info');

        Log::info('Start to insert HC client info');
        $this->getOrCreateHcClientCodeInfo();
        Log::info('Inserted HC client info');
    }

    private function getOrCreateHcClientCodeInfo()
    {
        $internal_client_service = resolve('App\Interfaces\IInternalClientService');

        HcGCCLIENTS::whereNotNull('fREGNUM')
            ->whereIn('fJURSTAT', [HcGCCLIENTS::LEGAL_ENTITY, HcGCCLIENTS::INDIVIDUAL]) //because in armsoft we have another jurstats (not only 21 and 22)
            ->where(DB::raw('LEN(fREGNUM)'), '>', 0) // Exclude empty strings
            ->whereNull('fDATECLOSE')
            ->select(['fJURSTAT', 'fREGNUM'])
            ->orderBy('fCODE')
            ->chunk(self::CHUNK_SIZE, function ($clients) use ($internal_client_service) {
                foreach ($clients as $client) {
                    $internal_client_service->getOrCreateClient($client->fREGNUM, $client->fJURSTAT);
                }
            });
    }

    private function getOrCreateClientBySsnAndClientType()
    {
        $internal_client_service = resolve('App\Interfaces\IInternalClientService');

        $now = now();
        LoanSecurity::distinct()
            ->whereNotNull('ssn')
            ->where('loan_type_id', '<>', constants('LOAN_TYPES.OBL'))
            ->where('created_at', '<', $now)
            ->select('ssn')
            ->orderBy('ssn')
            ->chunk(self::CHUNK_SIZE, function ($clients) use ($internal_client_service) {
                foreach ($clients as $client) {
                    $client_type = HcGCCLIENTS::INDIVIDUAL;
                    $internal_client_service->getOrCreateClient($client->ssn, $client_type);
                }
            });
    }
}
