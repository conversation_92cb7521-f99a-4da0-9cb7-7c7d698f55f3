<?php

namespace App\Nova;

use App\Helpers\NumberHelper;
use App\Models\CitizenPassport;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use Sixlive\TextCopy\TextCopy;

class ReferralCode extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Models\ReferralCode';

    /**
     * @var string[]
     */
    public static $with = [];

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = ['ssn', 'code'];

    public static function label()
    {
        return __('Referral Code');
    }

    /**
     * @return bool
     */
    public static function availableForNavigation(Request $request)
    {
        return $request->user()->can('view-referral-code');
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        $citizen = $this->ssn ? CitizenPassport::where([
            'passport_number' => $this->ssn,
            'type' => constants('SOC_CARD'),
        ])->latest()->first()->citizen : null;

        return [
            ID::make()->sortable(),

            Text::make(__('Soc Card'), 'ssn')->hideWhenUpdating(),

            Text::make(__('Referral Code'), 'code')->hideWhenUpdating(),

            Text::make(__('First Name'), function () use ($citizen) {
                return $citizen->first_name;
            }),

            Text::make(__('Last Name'), function () use ($citizen) {
                return $citizen->last_name;
            }),

            Text::make(__('Phone Number'), function () use ($citizen) {
                return $citizen->phone_number;
            }),

            TextCopy::make(__('Referral Link'), 'referral_link')
                ->hideWhenUpdating()
                ->onlyOnDetail(),

            Text::make(__('Quantity'), function () {
                return $this->repayments()->count();
            }),

            Text::make(__('Amount'), function () {
                return NumberHelper::numberToStringDram($this->repayments()->sum('amount'));
            }),

            Date::make(__('Start Date'), 'start_date')
                ->updateRules('required', 'date_format:Y-m-d', 'before:end_date', 'after_or_equal:'.date('Y-m-d'))
                ->onlyOnDetail(),

            Date::make(__('End Date'), 'end_date')
                ->updateRules('required', 'date_format:Y-m-d', 'after:start_date', 'after_or_equal:'.date('Y-m-d')),

            HasMany::make(__('Referral Loan Repayment'), 'repayments', 'App\Nova\ReferralCodeRepayment'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return array_merge(
            parent::actions($request),
            [
                (new \App\Nova\Actions\DownloadReferralCodes())->canRun(function ($request) {
                    return $request->user()->can('view-referral-code');
                })->canSee(function ($request) {
                    return $request->user()->can('view-referral-code');
                }),
            ]
        );
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }
}
