<?php

namespace App\Nova\Actions;

use App\Exports\DownloadKFWLoansExport;
use App\Models\KfwExcel;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Maatwebsite\Excel\Facades\Excel;

class DownloadKFWLoans extends BaseAction
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $file_name = 'KFW_Loans';

    public function name()
    {
        return __('DownloadKFWLoans');
    }

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $now = Carbon::now()->setTimezone(constants('ARM_TIMEZONE'))->format(constants('FILE_TIME_FORMAT'));
        $file = $this->file_name."_$now.xlsx";
        $path = env('KFW_DOWNLOADS_PATH')."/$file";
        $kfw_excel = new KfwExcel([
            'path' => $path,
        ]);
        $kfw_excel->save();
        Excel::store(new DownloadKFWLoansExport($models, $kfw_excel->id), "/$file", 'arpi_solar_kfw_excels');

        return Action::download($path, $file);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }
}
