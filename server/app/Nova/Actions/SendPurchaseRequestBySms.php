<?php

namespace App\Nova\Actions;

use App\Helpers\NumberHelper;
use App\Jobs\SendPurchaseRequestSms;
use App\Validators\Rules\PhoneNumber;
use <PERSON>vel\Nova\Fields\Text;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Actions\Action;

class SendPurchaseRequestBySms extends Action
{
    public $onlyOnDetail = true;

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $phone_number = NumberHelper::phoneMask($fields->phone_number);

            SendPurchaseRequestSms::dispatch($model, $phone_number);
        }
    }

    public function name()
    {
        return __('send purchase request by sms');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Text::make(__('Phone Number'), 'phone_number')
                ->rules('required', new PhoneNumber())
        ];
    }
}
