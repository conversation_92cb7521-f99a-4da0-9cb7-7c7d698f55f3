<?php

namespace App\Nova\Actions;

use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Text;

class EditEmailAndRegenerateDocuments extends RegenerateDocuments
{
    const REGENERATE_LOAN_DOCUMENTS = true;
    const REGENERATE_LAWYER_DOCUMENTS = true;

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->citizen->update([
                'email' => $fields->email,
            ]);

            $fields = (object) [
                'loan_documents' => self::REGENERATE_LOAN_DOCUMENTS,
                'lawyer_document' => self::REGENERATE_LAWYER_DOCUMENTS,
                'police_document' => false,
            ];

            $this->regenerateProcess($fields, $model);
        }
    }

    public function name()
    {
        return __('Edit Email And Regenerate Documents');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Text::make(__('Email'), 'email')->rules('required', 'email', 'max:254'),
        ];
    }
}
