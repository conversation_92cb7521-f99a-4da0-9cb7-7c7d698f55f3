<?php

namespace App\Nova\Actions;

use App\Exports\LoanExport;
use Auth;
use File;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Maatwebsite\Excel\Facades\Excel;
use ZipArchive;

class DownloadDocuments extends BaseAction
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $zip;

    public function __construct(ZipArchive $zip)
    {
        $this->zip = $zip;
    }

    public function name()
    {
        return __('DownloadDocuments');
    }

    /**
     * Perform the action on the given models.
     *
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        // storing
        $path = env('NOVA_DOWNLOADS_PATH');
        $directory = public_path($path);

        $this->zip = new ZipArchive();
        $zip_name = $this->composeArchiveName();

        if (!File::exists($directory)) {
            File::makeDirectory($directory);
            chmod($directory, 0777);
        }

        if ($this->zip->open("$directory/$zip_name", ZipArchive::CREATE) === true) {
            /* @var Loan $model */
            foreach ($models as $loan) {
                $folder_name = $this->composeFolderName($loan->citizen, $loan->id);

                if (Auth::user()->isOASLNMCAgent()) {
                    $documents = $loan->documents->whereIn('document_type', [
                        \App\Models\LoanDocument::JO_CONTRACT_OASL['name'],
                        \App\Models\LoanDocument::JO_MORTGAGE_CONTRACT_OASL['name'],
                    ]);
                } else {
                    $documents = $loan->documents;

                    $media = $loan->solar_panel->arpi_solar_media;

                    $this->addMedia($media, $folder_name);

                    if (!empty($loan->solar_panel->evaluation_report)) {
                        $this->addLoanExcel($loan, $folder_name);
                    }
                }

                $this->addDocuments($documents, $folder_name);
            }

            $this->zip->close();

            return Action::download("$path/$zip_name", $zip_name);
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }

    protected function addMedia($media, $folder_name)
    {
        foreach ($media as $m) {
            $download_file = file_get_contents($m->full_path_for_download);

            $this->zip->addFromString($folder_name.'/'.$this->composeMediaFileName($m), $download_file);
        }
    }

    protected function addDocuments($documents, $folder_name)
    {
        foreach ($documents as $document) {
            $download_file = file_get_contents($document->full_path_for_download);

            $this->zip->addFromString($folder_name.'/'.$document->document_type.'.pdf', $download_file);
        }
    }

    protected function composeMediaFileName($media, $length = 16)
    {
        // Getting media file extension
        preg_match("/(?:[^\/](?!(\/)))+$/", $media->type, $matches);

        $name = str_random($length);
        $ext = $matches[0];

        return "$media->document_type-$name.$ext";
    }

    protected function composeArchiveName()
    {
        return 'OASL_ARCHIVE_'.time().'.zip';
    }

    protected function composeFolderName($citizen, $loan_id)
    {
        return $citizen->first_name.'-'.$citizen->last_name.'-'.$citizen->middle_name.'-'.$loan_id;
    }

    protected function addLoanExcel($loan, $folder_name)
    {
        $hash = str_random();
        $file_name = "Loan_$hash.xlsx";

        $excel = Excel::download(new LoanExport($loan, 1), $file_name);

        $this->zip->addFile($excel->getFile(), $folder_name.'/'.$file_name);
    }
}
