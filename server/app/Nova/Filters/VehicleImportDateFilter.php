<?php

namespace App\Nova\Filters;

use Ampeco\Filters\DateRangeFilter;
use Carbon\Carbon;
use Illuminate\Http\Request;

class VehicleImportDateFilter extends DateRangeFilter
{
    public function name()
    {
        return __('Import Date');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        $start_date = $value[0];
        $end_date = $value[1];

        return $query
            ->whereHas('loan_security', function ($q) use ($start_date, $end_date) {
                $q->whereHas('loan_application_order', function ($q) use ($start_date, $end_date) {
                    $q->whereHas('vehicle_order_detail', function ($q) use ($start_date, $end_date) {
                        return $q->where('import_date', '>=', Carbon::parse($start_date, constants('ARM_TIMEZONE')))
                            ->where('import_date', '<=', Carbon::parse($end_date ?? $start_date, constants('ARM_TIMEZONE')));
                    });
                });
            });
    }
}
