<?php

namespace App\Nova\Filters;

use App\Models\User;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\Filter;

class OASLAgentFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function name()
    {
        return __('OASL Agent Filter');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->whereHas('solar_panel', function ($q) use ($value) {
            return $q->where('seller_id', $value);
        });
    }

    /**
     * Get the filter's available options.
     *
     * @return array
     */
    public function options(Request $request)
    {
        return User::getOASLAgents()->reduce(function ($carry, $agent) {
            $carry["$agent->first_name $agent->last_name"] = $agent->id;

            return $carry;
        }, []);
    }
}
