<?php

namespace App\Nova\Filters;

use Ampeco\Filters\DateRangeFilter;
use Illuminate\Http\Request;

class LoanApplicationDateFilter extends DateRangeFilter
{
    public function name()
    {
        return __('LoanApplicationDateFilter');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        $startDate = $value[0];
        $endDate = $value[1];

        return $query
            ->where('created_at', '>=', carbon_parse($startDate, constants('ARM_TIMEZONE'))->setTimezone('UTC'))
            ->where('created_at', '<=', carbon_parse($endDate ?? $startDate, constants('ARM_TIMEZONE'))->endOfDay()->setTimezone('UTC'));
    }
}
