<?php

namespace App\Nova\Filters;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\Filter;

class OVLTypeFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function name()
    {
        return __('OVL Type');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        switch ($value) {
            case 'PLEDGE':
                return $query->whereHas('vehicle', function ($q) {
                    $q->doesntHave('seller');
                });

            case 'TRADE':
                return $query->whereHas('vehicle', function ($q) {
                    $q->has('seller');
                });

            case 'VEHICLE_IMPORT':
                return $query->whereHas('loan_security', function ($q) {
                    $q->where('loan_type_id', constants('LOAN_TYPES.OVL'));
                    $q->where('loan_subtype_id', constants('LOAN_SUBTYPES.OVIL'));
                });

            default:
                return $query;
        }
    }

    /**
     * @return array
     */
    public function options(Request $request)
    {
        return [
            __('Pledge Type') => 'PLEDGE',
            __('Trade Type') => 'TRADE',
            __('Vehicle Import Type') => 'VEHICLE_IMPORT',
        ];
    }
}
