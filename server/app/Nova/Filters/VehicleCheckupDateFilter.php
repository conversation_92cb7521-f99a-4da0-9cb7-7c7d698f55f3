<?php

namespace App\Nova\Filters;

use Ampeco\Filters\DateRangeFilter;
use Illuminate\Http\Request;

class VehicleCheckupDateFilter extends DateRangeFilter
{
    public function name()
    {
        return __('Checkup Date');
    }

    /**
     * Apply the filter to the given query.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param mixed                                 $value
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        $start_date = $value[0];
        $end_date = $value[1];

        return $query
            ->whereHas('mortgage', function ($q) use ($start_date, $end_date) {
                return $q->where('checkup_date', '>=', carbon_parse($start_date, constants('ARM_TIMEZONE'))->setTimezone('UTC'))
                    ->where('checkup_date', '<=', carbon_parse($end_date ?? $start_date, constants('ARM_TIMEZONE'))->endOfDay()->setTimezone('UTC'));
            });
    }
}
