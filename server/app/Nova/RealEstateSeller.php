<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class RealEstateSeller extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\\Models\\RealEstateSeller';

    /**
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'Name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make(__('First Name'), 'first_name')
                ->rules(['string', 'required', 'max:255'])
                ->sortable(),

            Text::make(__('Last Name'), 'last_name')
                ->rules(['string', 'required', 'max:255'])
                ->sortable(),

            Text::make(__('Middle Name'), 'middle_name')
                ->rules(['string', 'required', 'max:255'])
                ->sortable(),

            Text::make(__('Phone Number'), 'phone_number')
                ->rules(['string', 'required', 'max:20']),

            Text::make(__('Passport Number'), 'passport_number')
                ->rules(['string', 'required']),

            Date::make(__('Given At'), 'given_date')
                ->format(constants('PASSPORT_DATE_FORMAT'))
                ->rules(['required', 'date', 'before:today']),

            Date::make(__('Birthday'), 'birthday')
                ->format(constants('PASSPORT_DATE_FORMAT'))
                ->rules(['required', 'date', 'before:18 years ago'])
                ->hideFromIndex(),

            Text::make(__('Passport From'), 'from')
                ->rules(['string', 'required', 'size:3']),

            Text::make(__('Registration Address'), 'registration_address')
                ->rules(['string', 'required', 'max:255'])
                ->hideFromIndex(),

            Text::make(__('Bank'), 'bank')
                ->rules(['string', 'nullable', 'max:50'])
                ->hideFromIndex(),

            Text::make(__('Bank Account'), 'bank_account')
                ->rules(['numeric', 'nullable', 'digits:16'])
                ->hideFromIndex(),
        ];
    }

    /**
     * @return array|string|null
     */
    public static function label()
    {
        return __('Seller');
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
