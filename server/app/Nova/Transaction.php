<?php

namespace App\Nova;

use App\Helpers\NumberHelper;
use App\Models\CreditLine\PurchaseOrder;
use App\Models\CreditLine\PurchaseSession;
use App\Nova\Filters\TransactionPaidDateFilter;
use App\Nova\Filters\VendorFilter;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Transaction extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Models\CreditLine\Transaction';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static function label()
    {
        return __('Transactions');
    }

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = ['vendor_id'];

    public static function availableForNavigation(Request $request)
    {
        $user = $request->user();

        return $user->isAdmin() || $user->hasPermissionTo('view-bnpl-transactions');
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            Text::make(__('Vendor Type'), 'name'),

            Text::make(__('Contract Number'), 'contract_number'),

            Number::make(__('Amount'), function () {
                return NumberHelper::numberToStringDram($this->amount, 0);
            })
                ->min(0),

            Text::make(__('Merchant'), 'market'),

            Date::make(__('Date'), function () {
                return Carbon::parse($this->paid)->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT'));
            }),

            Text::make(__('Description'), 'description')
                ->onlyOnDetail(),

            Text::make(__('Passport'), 'document_number')
                ->onlyOnDetail(),

            Text::make(__('Ssn'), 'ssn')
                ->onlyOnDetail(),

            Text::make(__('Phone Number'), 'phone_number')
                ->onlyOnDetail(),

            Text::make(__('Email Address'), 'email')
                ->onlyOnDetail(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            new TransactionPaidDateFilter(),
            new VendorFilter(),
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            (new \App\Nova\Actions\ExportTransactions())->askForFilename(__('File name'))->askForWriterType(null, __('Choose format'))->canRun(function ($request) {
                return $request->user()->isAdmin() || $request->user()->hasPermissionTo('view-bnpl-transactions');
            }), ];
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return self::getTransactionQuery($query);
    }

    public static function detailQuery(NovaRequest $request, $query)
    {
        return self::getTransactionQuery($query)->where('transactions.id', $request->route('resourceId'));
    }

    protected static function applySearch($query, $search)
    {
        $search = trim($search);

        return $query->where(function ($query) use ($search) {
            $query->where('bnpl_details.market_name', 'ILIKE', "%$search%")
                  ->orWhere('merchants.name', 'ILIKE', "%$search%")
                  ->orWhere('loans.contract_number', 'ILIKE', "%$search%");
        });
    }

    public static function getTransactionQuery($query)
    {
        return $query->select(
            'transactions.id',
            'loan_securities.ssn',
            'loan_securities.document_number',
            'loan_securities.phone_number',
            'loan_securities.email',
            'loans.contract_number',
            'transactions.amount',
            'transactions.paid',
            'transactions.description',
            'transactions.vendor_id',
            'vendors.name',
            DB::raw('CASE WHEN merchants.id IS NOT NULL THEN merchants.name ELSE bnpl_details.market_name END as market')
        )
        ->join('purchase_sessions', function ($join) {
            $join->on('purchase_sessions.order_id', '=', 'transactions.order_id')
                ->where('purchase_sessions.status', PurchaseSession::CONFIRMED);
        })
        ->join('vendors', 'vendors.id', '=', 'transactions.vendor_id')
        ->join('loans', 'loans.id', '=', 'transactions.loan_id')
        ->join('loan_securities', 'loan_securities.loan_id', '=', 'transactions.loan_id')
        ->leftJoin('purchase_orders', function ($join) {
            $join->on('purchase_orders.order_id', '=', 'transactions.order_id')
                ->where('purchase_orders.status', PurchaseOrder::APPROVED);
        })
        ->leftJoin('bnpl_details', 'bnpl_details.purchase_session_id', '=', 'purchase_sessions.id')
        ->leftJoin('merchants', 'merchants.id', '=', 'purchase_orders.merchant_id')
        ->whereIn('vendors.type', [
            constants('VENDOR_TYPES.PAY_LATER'),
            constants('VENDOR_TYPES.INTERNAL_BNPL'),
            constants('VENDOR_TYPES.BNPL'),
            constants('VENDOR_TYPES.ARMED'),
        ]);
    }
}
