<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class TransactionDocument extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Models\TransactionDocument';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [];

    public static $displayInNavigation = false;

    public static $perPageViaRelationship = 10;

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            Text::make(__('Path'), function () {
                $title = __(title_case(str_replace('_', ' ', $this->document_type)));

                return "<a href={$this->full_path} target='_blank' rel='noopener noreferrer'>{$title}</a>";
            })->asHtml(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query
            ->whereIn('document_type', [
                \App\Models\TransactionDocument::AGREEMENT_BNPL['name'],
                \App\Models\TransactionDocument::PERSONAL_SHEET_BNPL['name'],
                // Because old transactions already have generated CONTRACT_BNPL instead of AGREEMENT_BNPL,
                // that's why we need to show this doc(if exists) too.
                \App\Models\LoanDocument::CONTRACT_BNPL['name'],
            ]);
    }
}
