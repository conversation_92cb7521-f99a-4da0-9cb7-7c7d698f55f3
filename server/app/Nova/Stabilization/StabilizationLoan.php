<?php

namespace App\Nova\Stabilization;

use App\Helpers\NumberHelper;
use App\Nova\Resource;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use ZipArchive;

class StabilizationLoan extends Resource
{
    public static $model = 'App\Models\Stabilization\Loan';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'contract_number',
    ];

    public function title()
    {
        return $this->contract_number;
    }

    public static function availableForNavigation(Request $request)
    {
        return $request->user()->can('stabilization-loan');
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            Text::make(__('Contract Number'), 'contract_number')
                ->creationRules('required', 'string', 'min:10', 'max:10')
                ->updateRules('required', 'string', 'min:10', 'max:10')
                ->withMeta(['extraAttributes' => [
                    'placeholder' => 'CV21-AA001', ],
                ]),

            Select::make(__('Status'), 'status')
            ->options([
                'PENDING' => 'Pending',
                'VERIFIED' => 'Verified',
                'CONFIRMED' => 'Confirmed',
                'CONFIRMED_NO_PAY' => 'Confirmed without payment',
            ])
            ->hideFromIndex(),

            Text::make(__('Passport Number'), function () {
                $passport = $this->citizen->getPrimaryDocument();

                return "{$passport['passport_number']}
                    ({$passport['from']},
                    {$passport['given_date']->format(constants('EKENG_DATE_FORMAT'))})";
            }),

            Text::make(__('Soc Card'), function () {
                return $this->citizen->getSocCard()['passport_number'];
            }),

            Text::make(__('Phone Number'), function () {
                return $this->citizen->phone_number;
            })
            ->onlyOnDetail(),

            Text::make(__('First Name'), function () {
                return $this->citizen->first_name;
            }),

            Text::make(__('Last Name'), function () {
                return $this->citizen->last_name;
            }),

            Text::make(__('Middle Name'), function () {
                return $this->citizen->middle_name;
            }),

            Number::make(__('Amount'), 'amount')->min(0)->displayUsing(function ($v) {
                return NumberHelper::numberToStringDram($v);
            }),
        ];
    }

    public static function label()
    {
        return __('StabilizationLoan');
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            (new \App\Nova\Actions\Stabilization\DownloadLoanDocuments(new ZipArchive()))->canRun(function ($request) {
                return $request->user()->can('download-loan-documents');
            })
            ->canSee(function ($request) {
                return $request->user()->can('download-loan-documents');
            }),
        ];
    }
}
