<?php

namespace App\Nova;

use App\Helpers\NumberHelper;
use App\Nova\Abstracts\AbstractLoan;
use App\Nova\Filters\LoanStatusFilter;
use App\Nova\Filters\ReferrerSourceFilter;
use App\Nova\Filters\VehicleCheckupDateFilter;
use Cdbeaton\BooleanTick\BooleanTick;
use function Functional\filter;
use Globalcredit\ApproveVehicleLoan\ApproveVehicleLoan;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class VehicleLoan extends AbstractLoan
{
    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'contract_number'
    ];

    public static $with = [
        'citizen',
        'vehicle',
        'mortgage',
        'loan_security',
    ];

    public static $searchRelations = [
        'vehicle' => ['number', ['vin']],
        'citizen' => ['first_name', 'last_name', ['first_name', 'last_name']],
    ];

    public static function availableForNavigation(Request $request)
    {
        return $request->user()->hasPermissionTo('view-ovl-loan', config('nova.guard'));
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(Request $request)
    {
        $agent = $this->getLastAssignedAgent();

        return [
            ID::make()->sortable()->hideFromIndex(),

            Text::make(__('Contract Number'), 'contract_number')
                ->sortable()
                ->hideWhenUpdating(),

            Text::make(__('Passport Number'), function () {
                return $this->citizen ? $this->citizen->getPrimaryDocument()['passport_number'] : null;
            })->onlyOnDetail(),

            Text::make(__('First Name'), function () {
                return $this->citizen->first_name ?? null;
            })->onlyOnDetail(),

            Text::make(__('Last Name'), function () {
                return $this->citizen->last_name ?? null;
            })->onlyOnDetail(),

            Text::make(__('Full Name'), function () {
                $first_name = $this->citizen->first_name ?? null;
                $last_name = $this->citizen->last_name ?? null;

                return "{$first_name} {$last_name}";
            })
            ->onlyOnIndex(),

            Text::make(__('Vehicle Number'), function () {
                return $this->vehicle->number ?? '-';
            }),

            Text::make(__('Vehicle Name'), function () {
                $mark = $this->vehicle->vehicle_model->mark ?? '-';
                $model = $this->vehicle->vehicle_model->model ?? '-';

                return "{$mark} ($model)";
            }),

            Text::make(__('VIN'), function () {
                return $this->vehicle->vin ?? '-';
            })->onlyOnDetail(),

            Text::make(__('Address'), function () {
                return $this->mortgage->address ?? '-';
            })
            ->canSee(function ($request) {
                return !$request->user()->can('confirm-ovl-loan');
            }),

            Text::make(__('Address'), function () {
                return $this->mortgage->address ?? '-';
            })
            ->onlyOnDetail()
            ->canSee(function ($request) {
                return $request->user()->can('confirm-ovl-loan');
            }),

            DateTime::make(__('Checkup Date'), 'mortgage.checkup_date')->fillUsing(function ($request, $model) {
                if (empty($request->mortgage_checkup_date)) {
                    $request->mortgage_checkup_date = null;
                }

                $model->mortgage->checkup_date = $request->mortgage_checkup_date;
                $model->mortgage->save();
            })->format(constants('MORTGAGE_DATE_FORMAT_NOVA')),

            Text::make(__('Citizen Notes'), function () {
                return $this->mortgage->notes ?? '-';
            })
            ->onlyOnDetail(),

            Number::make(__('Trade Amount'), 'mortgage.trade_amount')->fillUsing(function ($request, $model) {
                if (empty($request->mortgage_trade_amount)) {
                    $request->mortgage_trade_amount = null;
                }

                $model->mortgage->trade_amount = $request->mortgage_trade_amount;
                $model->mortgage->save();
            })
                ->displayUsing(function ($v) {
                    return NumberHelper::numberToStringDram($v);
                })
                ->hideFromIndex()
                ->canSee(function () {
                    return $this->loan_security && $this->isOVIL();
                }),

            Text::make(__('Agent'), function () use ($agent) {
                return $agent ? "$agent->first_name $agent->last_name" : '-';
            }),

            Text::make(__('Notes'), function () use ($agent) {
                return $agent->pivot->notes ?? '-';
            })
            ->onlyOnDetail(),

            Text::make(__('Status'), 'status')
                ->sortable()
                ->hideWhenUpdating(),

            Date::make(__('Sign Date'), 'sign_date')
            ->sortable()
            ->resolveUsing(function ($value) {
                return $value->setTimezone(constants('ARM_TIMEZONE'))->format(constants('SMS_DATE_FORMAT')); // The sorting logic can be implemented here
            })
            ->hideWhenUpdating(),

            Text::make(__('Type'), function () {
                $mappings = [
                    'VEHICLE_IMPORT' => __('Vehicle Import Type'),
                    'TRADE' => __('Trade Type'),
                    'PLEDGE' => __('Pledge Type'),
                ];

                if ($this->loan_security) {
                    return $mappings[$this->detectOVLType()];
                }

                return '-';
            }),

            Text::make(__('Referrer Source'), function () {
                return $this->loan_security->referrer_source ?? 'CashMe';
            })->canSee(function ($request) {
                return $request->user()->can('confirm-ovl-loan');
            })->onlyOnDetail(),

            BooleanTick::make(__('Attached pic'), function () {
                if ($this->mortgage && !$this->mortgage->vehicle_media()->first()) {
                    return false;
                }

                return true;
            })
            ->onlyOnIndex()
            ->canSee(function ($request) {
                return $request->user()->can('confirm-ovl-loan');
            }),

            BooleanTick::make(__('Attached file'), function () {
                $police_doc = filter($this->documents, function ($doc) {
                    return $doc->document_type === 'police_file';
                });

                return (bool) $police_doc;
            })
            ->onlyOnIndex()
            ->canSee(function ($request) {
                return $request->user()->can('confirm-ovl-loan');
            }),

            HasMany::make(__('Loan Documents'), 'documents', 'App\Nova\LoanDocument'),

            ApproveVehicleLoan::make()
                ->withMeta([
                    'canVerify' => $request->user()->hasPermissionTo('verify-ovl-loan', config('nova.guard')),
                    'canConfirm' => $request->user()->hasPermissionTo('confirm-ovl-loan', config('nova.guard')),
                    'pledge_flow' => $this->loan_type ? $this->loan_type->loan_configs->where('key', 'pledge_flow')->first() : null,
                    'status' => $this->status,
                ])
                ->canSee(function ($request) {
                    return $request->user()->can('verify-ovl-loan') || $request->user()->can('confirm-ovl-loan');
                }),
        ];
    }

    public static function label()
    {
        return __('Vehicle Loans');
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(Request $request)
    {
        $loan = $request->findModelQuery()->first();

        $restricted_statuses = [
            \App\Models\Loan::CONFIRMED,
            \App\Models\Loan::REJECTED,
            \App\Models\Loan::PLEDGED,
        ];

        return array_merge([
            (new \App\Nova\Actions\ReassignAgent($loan))
                ->canSee(function ($request) {
                    return $request->user()->can('confirm-ovl-loan');
                })
                ->canRun(function ($request, $model) use ($restricted_statuses) {
                    // After restricted statuses, not allowing to touch the Loan
                    return !in_array($model->status, $restricted_statuses);
                }),
            (new \App\Nova\Actions\RegenerateOVILDocuments())
                ->canSee(function ($request) {
                    return $request->user()->can('confirm-ovl-loan');
                })->canRun(function ($request, $model) use ($restricted_statuses) {
                    // If restricted status, not allowing to touch the Loan,
                    //also this action is accessible for only OVIL
                    return !in_array($model->status, $restricted_statuses) && $model->isOVIL();
                })->onlyOnDetail(),
            ],

            parent::actions($request)
        );
    }

    public function filters(Request $request)
    {
        return array_merge(
            [
                (new VehicleCheckupDateFilter())
                    ->locale(env('NOVA_LOCALE'))
                    ->canSee(function ($request) {
                        return $request->user()->can('confirm-ovl-loan');
                    }),
                (new LoanStatusFilter())->canSee(function ($request) {
                    return $request->user()->can('confirm-ovl-loan');
                }),
                (new \App\Nova\Filters\OVLTypeFilter())
                    ->canSee(function ($request) {
                        return $request->user()->can('confirm-ovl-loan');
                    }),
                new ReferrerSourceFilter(),
            ],
            parent::filters($request)
        );
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        $user = $request->user();

        $query->where('loan_type_id', constants('LOAN_TYPES.OVL'));

        if ($user->isAdmin()) {
            return $query;
        }

        if ($user->isGCAgentAdmin() || $user->isOVLViewer()) {
            return $query
                ->whereIn('status', [
                    \App\Models\Loan::PROCESSING,
                    \App\Models\Loan::PROCESSED,
                    \App\Models\Loan::REVIEW,
                    \App\Models\Loan::PLEDGED,
                    \App\Models\Loan::CONFIRMED,
                    \App\Models\Loan::REJECTED,
                ]);
        }

        if ($user->isGCAgent()) {
            return $query
                ->join('loan_agent', 'loans.id', '=', 'loan_agent.loan_id')
                ->where('loan_agent.user_id', $user->id)
                ->whereIn('status', [\App\Models\Loan::PROCESSING, \App\Models\Loan::PROCESSED]);
        }
    }
}
