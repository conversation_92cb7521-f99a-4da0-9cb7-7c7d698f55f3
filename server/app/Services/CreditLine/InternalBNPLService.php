<?php

namespace App\Services\CreditLine;

use App\Abstracts\AbstractCreditLineService;
use App\Interfaces\CreditLine\IInternalBNPLService;
use App\Interfaces\IInternalRuleException;
use App\Services\SecurityUtilityService;
use Exception;
use Log;

class InternalBNPLService extends AbstractCreditlineService implements IInternalBNPLService
{
    public function resolveCredit()
    {
        return auth()->user()->bnpl_loan();
    }

    public function createCredit($loan_security, $payload = [])
    {
        $loan = parent::createCredit($loan_security, $payload);

        if (is_null($loan)) {
            $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');

            $purchase_service->rejectOrder();
        }

        return $loan;
    }

    public function storeCredit($loan_security, $details)
    {
        return $this->startTransaction(function () use ($loan_security, $details) {
            if ($loan_security->loan) {
                return $loan_security->loan;
            }

            return parent::storeCredit($loan_security, array_merge($details, ['sign_date' => now(), 'email' => $loan_security->email]));
        });
    }

    public function approve($code)
    {
        $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');
        $purchase_service->getValidPurchaseOrder();

        return parent::approve($code);
    }

    public function getLimit()
    {
        try {
            $redis_service = resolve('App\Interfaces\IRedisService');
            $auth_service = resolve('App\Services\Pallaton\AuthService');
            $profile = $auth_service->getUserProfile();

            $ssn = $profile->ssn;

            $cached = $redis_service->get("$ssn:credit_limit_internal_bnpl");
            // Coz cached value may be '0' which can cause some checking issues,
            //that's the reason that we need to check by is a null case
            if (!is_null($cached)) {
                Log::info('Return cached Credit limit data', ['ssn' => $ssn, 'cached' => $cached]);

                return $cached;
            }

            $this->associateUserWithLoanSecurity($ssn, $profile->user_id);

            $result = $this->getCreditLimitDetails($ssn, $profile);

            Log::info('Internal BNPL get limit', ['result' => $result]);

            $redis_service->set("$ssn:credit_limit_internal_bnpl", $result, constants('CREDIT_LIMIT_CACHE_EXPIRATION'));

            return $result;
        } catch (Exception $e) {
            throw $e;
        }
    }

    private function getCreditLimitDetails($ssn, $profile)
    {
        $result = self::INITIAL_LIMIT;

        try {
            $this->handleInternalRules($ssn, $profile->document_number);

            $citizen_service = resolve('App\Interfaces\CreditLine\ICitizenServiceBNPL');

            $result = $citizen_service->getCreditLimit($profile->document_number)['amount'];
        } catch (IInternalRuleException $e) {
            Log::warning('Internal Merchant Get Limit, '.get_class($e), ['error' => $e->getMessage()]);
            // e.g. App\Exceptions\HCBlacklistedException
            $affected_internal_rule = get_class($e);

            // Set affected_internal_rules in cache whether it was an exception
            // IMPORTANT NOTE: We are using redis set(add) method here,
            //to avoid an overriding existing key if it already exists,
            //because it may cause possible issues and the key TTL will always increase
            $redis_service = resolve('App\Interfaces\IRedisService');
            $redis_service->set("$ssn:affected_internal_rules", $affected_internal_rule, constants('ONE_HOUR_IN_MINUTES'));
            Log::info('Affected internal rule(s) stored in cache with key if it does not already exist', ['ssn' => $ssn]);
        }

        return $result;
    }

    public function resolveCreditBySSN()
    {
        return $this->resolveCredit();
    }

    private function associateUserWithLoanSecurity($ssn, $user_id)
    {
        if ($loan_security = SecurityUtilityService::getExistingBNPLLoanSecurity($ssn)) {
            // If BNPL loan created via non Cashme App, that user may not have user_id attached to loan security;
            // or the user re-registered and the user_id has changed,
            //that's why we need try to associate it
            if (isset($user_id) && (!$loan_security->user_id || $user_id != $loan_security->user_id)) {
                $loan_security->update(['user_id' => $user_id]);
            }
        }
    }
}
