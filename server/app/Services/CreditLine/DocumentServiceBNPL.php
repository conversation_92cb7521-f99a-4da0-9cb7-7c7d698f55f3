<?php

namespace App\Services\CreditLine;

use App\Abstracts\AbstractCreditLineDocumentService;
use App\Models\LoanDocument;
use App\Models\TransactionDocument;
use Carbon\Carbon;
use function Functional\pluck;

class DocumentServiceBNPL extends AbstractCreditLineDocumentService
{
    public function composePdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        $pdf_data_credit_line = [
            'application_name' => lang('application.name_BNPL'),
            'ssn' => $loan->loan_security->ssn,
            // NOTE: We have saved the requested vendor name as a referrer_source during the loan_security creation
            'vendor_name' => $loan->loan_security->referrer_source ?? '',
        ];

        return array_merge($pdf_data, $pdf_data_credit_line);
    }

    public function composeConversionPdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        $transaction = $loan->transactions()->orderBy('id', 'desc')->first();
        $pdf_data_credit_line = [
            'ssn' => $loan->loan_security->ssn,
            'vendor_name' => $transaction->vendor->name ?? '',
            'application_name' => lang('application.name_OCL'),
            'mortgage_exist' => lang('personal_sheet.mortgage_exist_OCL'),
            'provision_fee' => lang('personal_sheet.provision_fee_OCL'),
            'encashment_fee' => lang('personal_sheet.encashment_fee_OCL'),
            'other_fee' => lang('personal_sheet.other_fee_OCL'),
            'pledge_valuation' => '',
        ];

        $configs = $this->getLoanConfigs($loan);

        return array_merge($pdf_data, $pdf_data_credit_line, $this->conversionContractDates($loan), [
            'penalty_base_amount' => $configs['amortized_penalty_base_amount'],
            'penalty_percentage_amount' => $configs['amortized_penalty_percentage_amount'],
        ]);
    }

    public function composeTransactionPdfData($transaction)
    {
        $pdf_data = parent::composePdfData($transaction->loan);

        $pdf_data_transaction = array_merge([
            'application_name' => lang('application.name_BNPL'),
            'mortgage_exist' => lang('personal_sheet.mortgage_exist_BNPL'),
            'provision_fee' => lang('personal_sheet.provision_fee_BNPL'),
            'encashment_fee' => lang('personal_sheet.encashment_fee_BNPL'),
            'other_fee' => lang('personal_sheet.other_fee_BNPL'),
        ], parent::prepareTransactionPdfPayload($transaction));

        return array_merge($pdf_data, $pdf_data_transaction);
    }

    public function getDocumentTypes($obfuscated = null, $loan = null): array
    {
        return array_merge(
            [
                LoanDocument::CONTRACT_BNPL,
                LoanDocument::ARBITRATION,
            ],
            parent::getDocumentTypes(),
            $this->getDisputeSolution($loan->dispute_solution_method)
        );
    }

    public function getTransactionDocumentTypes(): array
    {
        return [
            TransactionDocument::PERSONAL_SHEET_BNPL,
            TransactionDocument::AGREEMENT_BNPL,
        ];
    }

    public function getDocumentsForRemove($obfuscated = true, $loan = null): array
    {
        // Sine we can't get previously selected arbitration document name,
        // we add all types in deleting list
        $documents = [
            LoanDocument::GNM,
            LoanDocument::UBA,
            LoanDocument::OPTIMUS_LEX,
        ];

        $documents = array_merge(
            $documents,
            $this->getDocumentTypes($obfuscated, $loan)
        );

        return pluck($documents, 'name');
    }

    public function getTransactionDocuments($transaction)
    {
        $transaction_documents = $transaction->documents->filter(function ($document) {
            return $document->public;
        });

        return $transaction_documents->toArray();
    }

    public function getConversionDocuments(): array
    {
        return [
            LoanDocument::PERSONAL_SHEET,
            LoanDocument::AMORTIZED_LOAN_SCHEDULE,
        ];
    }

    public function getConversionDocumentNames()
    {
        $documents = $this->getConversionDocuments();

        return pluck($documents, 'name');
    }

    protected function conversionContractDates($loan)
    {
        $loan_last_payment = $loan->loan_schedule->sortByDesc('date')->first();

        $loan_exp_date = Carbon::createFromFormat(constants('SMS_DATE_FORMAT'), $loan_last_payment->date, constants('ARM_TIMEZONE'));
        $loan_exp_date_in_words = $this->composeLocalizedDate($loan_exp_date);

        $loan_next_payment_date = $loan->next_payment_date->format(constants('EKENG_DATE_FORMAT'));
        $loan_exp_date_format = $loan_exp_date->format(constants('EKENG_DATE_FORMAT'));

        $date_now = Carbon::now()->setTimezone(constants('ARM_TIMEZONE'));
        $date_now = $this->composeLocalizedDate($date_now);

        $localized_sign_date = $this->composeLocalizedDate(Carbon::parse($loan->sign_date)->setTimezone(constants('ARM_TIMEZONE')));
        $localized_verified_at = $this->composeLocalizedDate(Carbon::parse($loan->verified_at)->setTimezone(constants('ARM_TIMEZONE')));

        return array_merge(parent::composePdfData($loan),
            [
                'date_now' => $date_now,
                'localized_sign_date' => $localized_sign_date,
                'localized_verified_at' => $localized_verified_at,
                'loan_exp_date' => $loan_exp_date_in_words,
                'loan_exp_date_format' => $loan_exp_date_format,
                'loan_next_payment_date' => $loan_next_payment_date,
            ]);
    }
}
