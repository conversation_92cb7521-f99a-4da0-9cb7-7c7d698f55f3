<?php

namespace App\Services\CreditLine;

use App\Abstracts\AbstractPurchaseService;

class PurchaseServiceBNPL extends AbstractPurchaseService
{
    public function __construct()
    {
        parent::__construct(constants('CREDIT_LINE.BNPL_PROJECT_START_YEAR'));
    }

    public function getPurchase($payment_id): array
    {
        $purchase_session = $this->getPurchaseSession($payment_id);

        return $this->processGetPurchase($purchase_session);
    }

    public function makePurchase($payment_id)
    {
        $transaction = parent::makePurchase($payment_id);

        $purchase_session = $this->getPurchaseSession($payment_id);

        $this->storeTransactionToHC($transaction);

        $this->expireSession($purchase_session);

        return $transaction;
    }
}
