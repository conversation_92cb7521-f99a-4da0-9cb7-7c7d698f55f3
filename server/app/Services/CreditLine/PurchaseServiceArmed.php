<?php

namespace App\Services\CreditLine;

use App\Exceptions\CreditLine\PurchaseOrderNotFoundException;
use App\Exceptions\InvalidArmedDataException;
use App\Exceptions\ResourceLockedException;
use App\Models\ArmedDetail;
use App\Models\CreditLine\PurchaseOrder;
use App\Services\SecurityUtilityService;
use Exception;
use Illuminate\Support\Facades\Log;

class PurchaseServiceArmed extends PurchaseServiceInternalBNPL
{
    public function storePurchaseOrder(array $payload)
    {
        try {
            if (!array_has($payload, ['mobile', 'ssn', 'amount', 'expiration', 'transaction_id'])) {
                throw new InvalidArmedDataException();
            }

            $redis_service = resolve('App\Interfaces\IRedisService');
            $hash_ssn = $payload['ssn'];

            $lock = $redis_service->lock("purchase_order_lock_$hash_ssn", constants('PURCHASE_ORDER_LOCK_TIMEOUT'));
            if (!$lock->acquire()) {
                Log::warning('Trying to access Armed Purchase Order locked resource', ['hash_ssn' => $hash_ssn]);

                throw new ResourceLockedException();
            }

            $this->checkForExistingPurchaseOrder($payload['order_id']);

            $this->expireExistingPurchaseOrderAndSession($payload['ssn']);

            // Armed's payload expiration date's tz is UTC+4, that's why we need to set it UTC before inserting to DB
            $expiration_utc = carbon_parse($payload['expiration'])->tz(constants('UTC_TIME'));
            $result = PurchaseOrder::create([
                'order_id' => $payload['order_id'],
                'phone_number' => $payload['mobile'],
                'hash_ssn' => $hash_ssn,
                'amount' => $payload['amount'],
                'expiration' => $expiration_utc,
                'vendor_id' => SecurityUtilityService::getVendor()->id,
            ]);

            ArmedDetail::create([
                'armed_id' => $payload['armed_id'],
                'phone' => $payload['mobile'],
                'hash_ssn' => $hash_ssn,
                'amount' => $payload['amount'],
                'expiration' => $expiration_utc,
                'order_id' => $payload['order_id'],
                'purchase_order_id' => $result->id,
            ]);

            $lock->release();

            Log::info('Armed Purchase Order stored, releasing lock', ['result ' => $result]);

            return $result;
        } catch (ResourceLockedException $e) {
            Log::warning('Store Armed Purchase Order, ResourceLockedException', ['error' => $e->getTraceAsString()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Armed Purchase Order, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            optional($lock)->release();
            Log::warning('Releasing locked Store Armed Purchase Order request because of Exception', ['hash_ssn' => $hash_ssn]);

            throw $e;
        }
    }

    public function getPurchaseOrderStatus($payload)
    {
        $armed_service = resolve('App\Interfaces\IArmedService');
        $payload = $armed_service->decrypt($payload);

        Log::info('Getting Purchase Order status', ['payload ' => $payload]);

        if (!array_has($payload, ['transaction_id'])) {
            throw new InvalidArmedDataException();
        }

        // we assume that status check going to be called after the expiration date
        $purchase_order = PurchaseOrder::where('order_id', $payload['transaction_id'])->latest()->first();
        if (is_null($purchase_order)) {
            throw new PurchaseOrderNotFoundException();
        }

        return $purchase_order->status;
    }

    protected function resolvePaymentIdExpiration($payload = null)
    {
        return carbon_parse($payload['expiration'])->tz(constants('UTC_TIME'));
    }
}
