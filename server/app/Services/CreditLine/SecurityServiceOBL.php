<?php

namespace App\Services\CreditLine;

use App\Abstracts\BaseSecurityService;
use App\Exceptions\InvalidSuuidException;
use App\Models\LoanSecurity;
use App\Services\SecurityUtilityService;
use Log;

class SecurityServiceOBL extends BaseSecurityService
{
    protected $suuid = null;

    public function __construct($suuid)
    {
        $this->suuid = $suuid;
        parent::__construct($suuid);
    }

    public function extractLoanSecurity()
    {
        $loan_security = SecurityUtilityService::getExistingOblLoanSecurity($this->suuid);

        if ($loan_security) {
            Log::info('OBL loanSecurity resolved', [
                    'loan_security_id' => $loan_security->id,
                ]);

            return $loan_security;
        }

        $loan_security = LoanSecurity::where('suuid', $this->suuid)->where('suuid_exp', '>', now())->first();

        if (!$loan_security) {
            Log::info('OBL loanSecurity cannot be resolved', [
                'suuid' => $this->suuid,
            ]);

            throw new InvalidSuuidException();
        }

        Log::info('OBL loanSecurity resolved', [
            'suuid' => $this->suuid,
            'loan_security_id' => $loan_security->id,
        ]);

        return $loan_security;
    }
}
