<?php

namespace App\Services;

use App\Exceptions\CreditLine\ModelLockedException;
use Log;

class ModelLockingService
{
    public function conditionalLock($model)
    {
        $this->checkModelLock($model);

        $model->lock();
    }

    private function checkModelLock($model)
    {
        if ($model->isLocked()) {
            Log::error('ModelLockedException, model is locked', $model->toArray());

            throw new ModelLockedException();
        }
    }

    public function unlock($model)
    {
        $model->unlock();
    }
}
