<?php

namespace App\Services;

use App\Abstracts\AbstractCreditOfferService;
use App\Interfaces\IPreapprovedCreditOfferService;
use App\Models\LegalEntity;
use App\Models\PreapprovedCreditOfferType;
use App\Traits\StoreThirdPartyResponse;
use function Functional\some;

class PreapprovedCreditOfferService extends AbstractCreditOfferService implements IPreapprovedCreditOfferService
{
    use StoreThirdPartyResponse;

    public function getCreditOffer($citizen, $loan_type_id): array
    {
        return $this->rule_service->processRules($citizen, $loan_type_id);
    }

    public function saveResult($result, $loan_type_id, $rule_set_id, $details, $type = null, $top_up_offer_details = [])
    {
        if ($this->hasExistingCreditOffer($details['identifier'], $loan_type_id, $type)) {
            return;
        }

        $legal_entity = LegalEntity::create($this->withExtraDetails($details));

        return $this->startTransaction(function () use ($legal_entity, $loan_type_id, $result, $rule_set_id, $type, $top_up_offer_details) {
            $credit_offer = $this->saveCreditOffer($legal_entity, $loan_type_id, $result['credit'], $rule_set_id, $type, $top_up_offer_details);

            $this->saveContext($credit_offer, $result['context']);
            $this->saveAffectedRules($credit_offer, $result['affected_rules']);

            return $credit_offer;
        });
    }

    protected function saveCreditOffer($legal_entity, $loan_type_id, $credit, $rule_set_id, $type, $top_up_offer_details)
    {
        $rejected = $this->rule_service->isCreditRejected($credit);

        $preapproved_credit_offer_type = PreapprovedCreditOfferType::where('name', $type)->first();

        $preapproved_credit_offer = $legal_entity->preapproved_credit_offers()->create(array_merge($credit, [
            'loan_type_id' => $loan_type_id,
            'rule_set_id' => $rule_set_id,
            'rejected' => $rejected,
            'expiration_date' => $this->getExpirationDateByType($type, $rejected),
            'credit_offer_type_id' => $preapproved_credit_offer_type->id ?? null,
        ]));

        if (in_array($type, [PreapprovedCreditOfferType::TOP_UP_LOAN_APPLICATION, PreapprovedCreditOfferType::TOP_UP_PERIODIC_OFFER])) {
            $preapproved_credit_offer->top_up_offer_detail()->create([
                'contract_number' => $top_up_offer_details['activeCreditContractNumber'],
                'balance' => $top_up_offer_details['activeCreditBalance'],
                'accrued_interest_amount' => $top_up_offer_details['activeCreditAccruedInterestAmount'],
                'accrued_service_fee_amount' => $top_up_offer_details['activeCreditAccruedServiceFeeAmount'],
            ]);
        }

        return $preapproved_credit_offer;
    }

    protected function saveContext($credit_offer, $context)
    {
        $context = $this->prepareContext($context);

        $credit_offer->preapproved_context()->createMany($context);
    }

    protected function saveAffectedRules($credit_offer, $affected_rules)
    {
        $rules = $this->prepareAffectedRules($affected_rules);

        $credit_offer->preapproved_affected_rules()->createMany($rules);
    }

    protected function hasExistingCreditOffer($identifier, $loan_type_id, $type): bool
    {
        $legal_entity = LegalEntity::whereIdentifier($identifier)->latest()->first();

        if (is_null($legal_entity) || $legal_entity->valid_preapproved_credit_offers->isEmpty()) {
            return false;
        }

        $preapproved_credit_offer_type_id = PreapprovedCreditOfferType::where('name', $type)->first()->id;

        return some($legal_entity->valid_preapproved_credit_offers, function ($offer) use ($loan_type_id, $preapproved_credit_offer_type_id) {
            return $loan_type_id == $offer->loan_type_id && $offer->expiration_date > now() && $offer->credit_offer_type_id == $preapproved_credit_offer_type_id;
        });
    }

    private function getExpirationDateByType($type, $rejected)
    {
        $now = now();

        switch ($type) {
            case PreapprovedCreditOfferType::INITIAL_CREDIT_LIMIT:
            case PreapprovedCreditOfferType::TOP_UP_LOAN_APPLICATION:
            case PreapprovedCreditOfferType::TOP_UP_PERIODIC_OFFER && $rejected:
                return $now->addDays(constants('ONE_DAY'));
            case PreapprovedCreditOfferType::TOP_UP_PERIODIC_OFFER:
                return $now->addDays(constants('TEN_DAY'));
            default:
                // 'null' value means that expirationDate is not actual
                // e.g., PERIODIC_CREDIT_LIMIT_CALCULATION, TRANSACTION_CREDIT_LIMIT_CALCULATION
                return null;
        }
    }
}
