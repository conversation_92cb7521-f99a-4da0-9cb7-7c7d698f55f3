<?php

namespace App\Services;

use App\Exceptions\AwsConnectionException;
use App\Exceptions\BlacklistedException;
use App\Exceptions\FaceNotFoundException;
use App\Exceptions\UnmatchedFaceException;
use App\Interfaces\IFaceComparisonService;
use App\Models\CitizenFaceRecognition;
use Aws\Rekognition\RekognitionClient;
use Exception;
use Intervention\Image\Facades\Image as ImageFacade;
use Log;

class FaceComparisonService implements IFaceComparisonService
{
    public function compareCitizen($target_image, $source_image)
    {
        try {
            $target_image = $this->prepareImageForFaceComparing($target_image);

            $result = $this->awsCompareFaces($source_image, $target_image);

            $matched = count($result['FaceMatches']) > 0;

            $verifications = [
                'face_similarity' => $result['FaceMatches'][0]['Similarity'] ?? $result['UnmatchedFaces'][0]['Similarity'] ?? null,
                'status' => $matched ? CitizenFaceRecognition::MATCHED : CitizenFaceRecognition::UNMATCHED,
            ];

            Log::info('Face comparison match', ['MATCHED' => $matched]);

            if (!$matched) {
                throw new UnmatchedFaceException($verifications);
            }

            return $verifications;
        } catch (BlacklistedException $e) {
            Log::error('Citizen face comparison, BlacklistedException', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Function to prepare uploaded image for aws face compare.
     * Reduces image size by converting Image to Binary without loosing much quality if file size > 5.
     * Tested for max size 20MB images, converts to 3-4MB.
     * AWS limit is 5MB.
     *
     * @return string
     */
    protected function prepareImageForFaceComparing(string $image)
    {
        $file_size = $this->getFileSize($image);

        if ($file_size > constants('AWS_MAX_IMAGE_SIZE')) {
            // Creating Intervention Image to reduce file size.
            $image = ImageFacade::make($image);

            return (string) $image->encode('jpeg', 85);
        }

        return $image;
    }

    /**
     * Function to get file size.
     *
     * @param $file
     *
     * @return float|int
     */
    protected function getFileSize(string $file)
    {
        $size = strlen($file);

        return $size / 1024 / 1024;
    }

    protected function awsCompareFaces($citizen_photo, $target_image)
    {
        $client = $this->getAwsClient();

        try {
            Log::info('Start Citizen face comparison');

            $result = $client->compareFaces([
                'SimilarityThreshold' => constants('FACE_SIMILARITY_THRESHOLD'),
                'SourceImage' => [
                    'Bytes' => $citizen_photo,
                ],
                'TargetImage' => [
                    'Bytes' => $target_image,
                ],
            ]);

            Log::info('Citizen face comparison result', [
                'FaceMatches' => $result->get('FaceMatches'),
                'UnmatchedFaces' => $result->get('UnmatchedFaces'),
            ]);

            return $result;
        } catch (Exception $e) {
            Log::error('FaceNotFoundException', ['error' => $e->getMessage()]);
            throw new FaceNotFoundException(['status' => CitizenFaceRecognition::FACE_NOT_FOUND]);
        }
    }

    private function getAwsClient()
    {
        try {
            $args = [
                'credentials' => [
                    'key' => env('AWS_API_KEY'),
                    'secret' => env('AWS_API_SECRET'),
                ],
                'region' => env('AWS_USER_REGION'),
                'version' => env('AWS_API_VERSION'),
            ];

            $client = new RekognitionClient($args);

            return $client;
        } catch (Exception $e) {
            Log::critical('AwsException', ['error' => $e->getMessage()]);
            throw new AwsConnectionException(['status' => CitizenFaceRecognition::AWS_FAIL]);
        }
    }
}
