<?php

namespace App\Services;

use App\Abstracts\AbstractDocumentService;
use App\Helpers\NumberHelper;
use App\Jobs\GenerateLoanDocuments;
use App\Models\LoanDocument;

class LoanDocumentServiceOUPL extends AbstractDocumentService
{
    public function composePdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        $pdf_data_oupl = [
            'contract_number' => $loan->contract_number,
            'upay_id' => $loan->wallet_details->account_id,
            'sign_date' => $loan->sign_date->format('Y-m-d'),
            'rate_in_words' => NumberHelper::floatToText($loan->interest_rate),
        ];

        return array_merge($pdf_data, $pdf_data_oupl);
    }

    public function getDocumentTypes($obfuscated = null, $loan = null)
    {
        return array_merge(
            [
                LoanDocument::CONTRACT_OUPL,
                LoanDocument::WHAT_TO_DO,
                LoanDocument::PERSONAL_SHEET_OUPL,
                LoanDocument::ARBITRATION,
            ],
            $this->getDisputeSolution($loan->dispute_solution_method)
        );
    }

    public function getLoanDocumentsJob($loan)
    {
        return new GenerateLoanDocuments($loan);
    }
}
