<?php

namespace App\Services;

use Log;

class DevOIDLService extends OIDLService
{
    protected function requestWalletInfo($token, $suuid = null)
    {
        Log::info('Requesting user data from Idram with token', ['token' => $token]);

        $res = FakerService::getMockedIdramPayload($token);

        Log::info('Requesting user data from Idram', ['result' => $res]);

        return $res;
    }

    public function transfer($token, $amount, $agreement_id, $suuid)
    {
    }

    public function checkTokenAvailability($token)
    {
    }
}
