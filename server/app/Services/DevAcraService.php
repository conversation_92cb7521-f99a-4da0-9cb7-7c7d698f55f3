<?php

namespace App\Services;

use App\Abstracts\AbstractAcraService;
use Carbon\Carbon;
use function Functional\map;

class DevAcraService extends AbstractAcraService
{
    public function __construct($report_type)
    {
        parent::__construct($report_type ?? constants('ACRA_REPORT_TYPE.CREDIT_WITH_FICO'));
    }

    public function getCitizen($ssn, $first_name, $last_name, $passport_data)
    {
        if (FakerService::isFakePassport($passport_data)) {
            $citizen = FakerService::getMockedCitizen($passport_data);

            return $citizen['acra'];
        }

        $citizen = parent::getCitizen($ssn, $first_name, $last_name, $passport_data);

        // To force RuleEngine return valid data for testing
        $citizen['PARTICIPIENT']['TheWorstClassLoan'] = 'Ստանդարտ';
        $citizen['PARTICIPIENT']['TheWorsClassGuarantee'] = 'Ստանդարտ';
        $citizen['PARTICIPIENT']['DelayQuantity'] = 0;
        $citizen['PARTICIPIENT']['RequestQuantity30'] = 0;

        if (array_key_exists('Loans', $citizen['PARTICIPIENT'])) {
            // Make all vehicle loans paid, so that rule engine doesn't reject
            $citizen['PARTICIPIENT']['Loans']['Loan'] = map($citizen['PARTICIPIENT']['Loans']['Loan'], function ($loan) {
                if (
                    mb_strtolower($loan['CreditStatus']) === constants('ACRA.CURRENT_CREDIT') &&
                    $loan['PledgeSubject'] !== [] &&
                    mb_strtolower($loan['PledgeSubject']) === constants('ACRA.CAR')
                ) {
                    $loan['CreditStatus'] = 'մարված';
                }

                return $loan;
            });
        }

        $citizen['PARTICIPIENT']['Loans']['Loan'] = [
            [
                'Balance' => 10,
                'CreditStart' => '01-01-2020',
                'CreditStatus' => 'գործող',
                'PledgeSubject' => 'Անշարժ գույք',
                'Amount' => '1500000',
                'Currency' => 'USD',
                'CloseDate' => Carbon::now()->addMonth()->format('d-m-Y'),
                'Interest' => '12.50',
                'CreditUsePlace' => 2,
                'TheWorstClassLoan1-12' => [],
                'CreditTypeID' => 35,
                'SourceName' => 'Գլոբալ Կրեդիտ ՈՒՎԿ ՓԲԸ',
            ],
            [
                'Balance' => 5000,
                'CreditStart' => '01-01-2020',
                'CreditStatus' => 'գործող',
                'PledgeSubject' => 'Անշարժ գույք',
                'Amount' => '1500000',
                'Currency' => 'AMD',
                'CloseDate' => Carbon::now()->addMonth()->format('d-m-Y'),
                'Interest' => '12.50',
                'CreditUsePlace' => 2,
                'TheWorstClassLoan1-12' => [],
                'CreditTypeID' => 35,
                'SourceName' => 'Գլոբալ Կրեդիտ ՈՒՎԿ ՓԲԸ',
            ],
        ];

        $citizen['PARTICIPIENT']['CurrentOverdueLoans']['CurrentOverdueLoan'] = [];

        if (array_key_exists('Score', $citizen['PARTICIPIENT'])) {
            $citizen['PARTICIPIENT']['Score']['FICOScore'] = 606;
        }

        return $citizen;
    }

    protected function composeAcraLoginPayload($first_name, $last_name, $passport_data)
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->tryResolveLoanSecurity();
        if ($loan_security && $loan_security->loan_type_id == constants('LOAN_TYPES.OBL')) {
            return $this->businessRequestData();
        }

        return $this->requestData();
    }

    private function businessRequestData()
    {
        return [
            'ReqID' => str_random(13),
            'AppNumber' => str_random(15),
            'DateTime' => date('d/m/Y h:i:s'),
            'ReportType' => '05',
            'KindBorrower' => 1,
            'RequestTarget' => '1',
            'UsageRange' => '1',
            'citizens' => [[
                'Participient' => str_random(12),
                'FirstName' => 'ԿԱՐԵՆ',
                'LastName' => 'ԳԱԼՍՏՅԱՆ',
                'DateofBirth' => '20-01-1980',
                'PassportNumber' => '*********',
                'SocCardNumber' => '1102800112',
                'IdCardNumber' => '',
            ]],
        ];
    }

    private function requestData()
    {
        return [
            'ReqID' => str_random(13),
            'AppNumber' => str_random(15),
            'DateTime' => date('d/m/Y h:i:s'),
            'ReportType' => $this->report_type,
            'KindBorrower' => 1,
            'RequestTarget' => '1',
            'UsageRange' => '1',
            'citizens' => [[
                'Participient' => str_random(12),
                'FirstName' => 'ԳՐԻԳՈՐ',
                'LastName' => 'ԳԱԼՍՏՅԱՆ',
                'DateofBirth' => '20-01-1980',
                'PassportNumber' => '*********',
                'SocCardNumber' => '1625890314',
                'IdCardNumber' => '',
            ]],
        ];
    }
}
