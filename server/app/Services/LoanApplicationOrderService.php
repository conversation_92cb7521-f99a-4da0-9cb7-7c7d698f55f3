<?php

namespace App\Services;

use App\Exceptions\ExpiredLoanApplicationOrderException;
use App\Exceptions\LoanApplicationOrderAlreadyExistsException;
use App\Exceptions\LoanApplicationOrderNotFoundException;
use App\Exceptions\ResourceLockedException;
use App\Interfaces\ILoanApplicationOrderService;
use App\Models\CreditLine\PurchaseOrder;
use App\Models\Loan;
use App\Models\LoanApplicationOrder;
use App\Models\VehicleModel;
use App\Models\VehicleOrderDetail;
use App\Traits\Transaction;
use Exception;
use Log;

class LoanApplicationOrderService implements ILoanApplicationOrderService
{
    use Transaction;

    const PER_PAGE = 15;

    public function storeOrderDetails(array $payload)
    {
        try {
            $redis_service = resolve('App\Interfaces\IRedisService');
            $document_number = strtoupper($payload['document_number']);
            $lock = $redis_service->lock("loan_application_order_lock_$document_number", constants('LOAN_APPLICATION_ORDER_LOCK_TIMEOUT'));
            if (!$lock->acquire()) {
                Log::warning('Trying to access Loan application order locked resource', ['document_number' => $document_number]);

                throw new ResourceLockedException();
            }

            $this->checkForExistingOrder($payload['order_id']);

            $ssn = $this->getSSNFromAggregator($document_number);
            $this->expireExistingOrder($ssn);

            // The Auth user is a some merchant agent
            $user = auth()->user();
            $merchant = $user->merchants()->first();

            $result = LoanApplicationOrder::create([
                'order_id' => $payload['order_id'],
                'merchant_id' => $merchant->id,
                'agent_id' => $user->id,
                'phone_number' => $payload['phone_number'],
                'ssn' => $ssn,
                'document_number' => $document_number,
                'amount' => $payload['amount'],
                'expiration' => now()->addDays(constants('LOAN_APPLICATION_ORDER_EXP')),
                'vendor_id' => SecurityUtilityService::getVendor()->id,
            ]);

            VehicleOrderDetail::create([
                'police_code' => $payload['police_code'],
                'vin' => $payload['vin'],
                'released' => $payload['released'],
                'loan_application_order_id' => $result->id,
            ]);

            $lock->release();

            Log::info('Loan application order stored, releasing lock', ['result ' => $result]);

            return $result;
        } catch (ResourceLockedException $e) {
            Log::warning('Store Loan application order, ResourceLockedException', ['error' => $e->getTraceAsString()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Store Loan application order, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            optional($lock)->release();
            Log::warning('Releasing locked Store Loan application order request because of Exception', ['document_number' => $document_number]);

            throw $e;
        }
    }

    protected function checkForExistingOrder($order_id)
    {
        if (PurchaseOrder::where('order_id', $order_id)->whereNotIn('status', [PurchaseOrder::PENDING, PurchaseOrder::EXPIRED])->exists()) {
            Log::warning('Loan application order already exists', ['order_id' => $order_id]);

            throw new LoanApplicationOrderAlreadyExistsException();
        }
    }

    private function getSSNFromAggregator($document_number)
    {
        $aggregator_service = resolve('App\Interfaces\IAggregatorService');

        return $aggregator_service->getSSN($document_number);
    }

    protected function expireExistingOrder($ssn)
    {
        $loan_application_order = LoanApplicationOrder::where('ssn', $ssn)
            ->where('status', LoanApplicationOrder::PENDING)
            ->latest()
            ->first();

        if (isset($loan_application_order)) {
            $this->expireOrder($loan_application_order);
        }
    }

    public function getOrdersByMerchant($searchable_value)
    {
        $user = auth()->user();
        $merchant = $user->merchants()->first();

        $loan_application_orders = LoanApplicationOrder::with(['agent' => function ($query) {
            // Even when the user does not exist anymore, we need to show his authored purchase orders
            $query->withTrashed();
        }, 'loan_security.loan' => function ($query) {
            // We should involve the loan amount into details,
            //if Citizen processed flow completely (the loan status is not PENDING)
            $query->where('status', '<>', Loan::PENDING);
        },
        'vehicle_order_detail',
        ])->where('merchant_id', $merchant->id);

        if (!empty($searchable_value)) {
            $loan_application_orders->where(function ($query) use ($searchable_value) {
                $query->orWhere('ssn', 'ilike', "%{$searchable_value}%")
                    ->orWhere('document_number', 'ilike', "%{$searchable_value}%")
                    ->orWhere('phone_number', 'ilike', "%{$searchable_value}%")
                    ->orWhere('amount', 'ilike', "%{$searchable_value}%");
            });

            $loan_application_orders->orWhereHas('vehicle_order_detail', function ($query) use ($searchable_value) {
                $query->where('vin', 'ilike', "%{$searchable_value}%");
            });
        }
        $loan_application_orders = $loan_application_orders->orderBy('created_at', 'desc')->paginate(self::PER_PAGE);

        $result = $this->mergeVehicleModels($loan_application_orders);
        Log::info('Loan application orders', ['result' => $result]);

        return $result;
    }

    protected function mergeVehicleModels($loan_application_orders)
    {
        foreach ($loan_application_orders as $order) {
            if ($order->vehicle_order_detail) {
                $police_code = $order->vehicle_order_detail->police_code;
                $vehicle_model = VehicleModel::getVehicleModelByPoliceCode($police_code);
                $order->vehicle_order_detail->vehicle_model = $vehicle_model;
            }
        }

        return $loan_application_orders;
    }

    public function resolveMerchantDetailsByAgent()
    {
        $user = auth()->user();

        return $user->merchants()->first();
    }

    public function hasPendingApplicationOrder($document_number)
    {
        $document_number = strtoupper($document_number);
        $ssn = $this->getSSNFromAggregator($document_number);

        return LoanApplicationOrder::where('ssn', $ssn)
            ->where('status', LoanApplicationOrder::PENDING)
            ->where('expiration', '>', now())
            ->exists();
    }

    public function getValidLoanApplicationOrder()
    {
        $auth_service = resolve('App\Services\Pallaton\AuthService');
        $profile = $auth_service->getUserProfile();

        Log::info('Get Valid Loan Application Order, User ssn', ['ssn' => $profile->ssn]);

        $loan_application_order = LoanApplicationOrder::where('ssn', $profile->ssn)
            ->where('status', LoanApplicationOrder::PENDING)
            ->latest()
            ->first();

        if (is_null($loan_application_order)) {
            throw new LoanApplicationOrderNotFoundException();
        }

        if (!$loan_application_order->expiration->isFuture()) {
            throw new ExpiredLoanApplicationOrderException();
        }

        return $loan_application_order;
    }

    public function approveOrder()
    {
        $loan_application_order = $this->getValidLoanApplicationOrder();

        $loan_application_order->update([
            'status' => LoanApplicationOrder::APPROVED,
            'expiration' => now(),
        ]);
    }

    public function rejectOrder()
    {
        $loan_application_order = $this->getValidLoanApplicationOrder();

        $loan_application_order->update([
            'status' => LoanApplicationOrder::REJECTED,
            'expiration' => now(),
        ]);
    }

    public function expireOrder($order)
    {
        $order->update([
            'expiration' => $this->resolveExpirationDate($order->expiration),
            'status' => LoanApplicationOrder::EXPIRED,
        ]);

        Log::info('Loan Application Order Expired', ['order_id' => $order->id]);
    }

    private function resolveExpirationDate($expiration): \Illuminate\Support\Carbon
    {
        // If expiration date is in the future, keep it, otherwise set it to now
        return $expiration->isFuture() ? now() : $expiration;
    }
}
