<?php

namespace App\Services;

use App\Abstracts\AbstractDocumentService;
use App\Jobs\GenerateLoanDocuments;
use App\Models\LoanDocument;
use function Functional\pluck;

class LoanDocumentServiceOCL extends AbstractDocumentService
{
    public function composePdfData($loan)
    {
        $pdf_data = parent::composePdfData($loan);

        $pdf_data_ocl = [
            'application_name' => lang('application.name_OCL'),
            'mortgage_exist' => lang('personal_sheet.mortgage_exist_OCL'),
            'provision_fee' => lang('personal_sheet.provision_fee_OCL'),
            'encashment_fee' => lang('personal_sheet.encashment_fee_OCL'),
            'other_fee' => lang('personal_sheet.other_fee_OCL'),
            'pledge_valuation' => '',
        ];

        return array_merge($pdf_data, $pdf_data_ocl);
    }

    public function getDocumentTypes($obfuscated = null, $loan = null)
    {
        $documents = [
            LoanDocument::CONTRACT_OCL,
            LoanDocument::PERSONAL_SHEET,
            LoanDocument::APPLICATION,
        ];

        if ($obfuscated) {
            $documents = [
                LoanDocument::CONTRACT_OCL_PRIVATE,
                LoanDocument::PERSONAL_SHEET_PRIVATE,
                LoanDocument::APPLICATION_PRIVATE,
            ];
        }

        if ($loan->isTopUp()) {
            $documents = $this->getTopUpDocumentTypes($obfuscated);
        }

        return array_merge($documents, parent::getDocumentTypes($obfuscated, $loan));
    }

    public function getTempPrivateDocs($loan = null)
    {
        $private_docs = array_merge(
            [
                LoanDocument::CONTRACT_OCL_PRIVATE['name'],
            ],
            parent::getTempPrivateDocs($loan)
        );

        if ($loan->isTopUp()) {
            $private_docs = array_merge(
                pluck($this->getTopUpDocumentTypes(true), 'name'),
                $private_docs
            );
        }

        return $private_docs;
    }

    public function getTempPublicDocs($loan)
    {
        $public_docs = array_merge(
            [
                LoanDocument::CONTRACT_OCL['name'],
            ],
            parent::getTempPublicDocs($loan)
        );

        if ($loan->isTopUp()) {
            $public_docs = array_merge(
                pluck($this->getTopUpDocumentTypes(), 'name'),
                $public_docs
            );
        }

        return $public_docs;
    }

    public function getLoanDocumentsJob($loan)
    {
        return GenerateLoanDocuments::withChain([
            new GenerateLoanDocuments($loan, $obfuscate = false),
        ]);
    }

    public function getTopUpDocumentTypes($obfuscated = false): array
    {
        $documents = [
            LoanDocument::AGREEMENT_TOP_UP,
            LoanDocument::PERSONAL_SHEET,
        ];

        $private_documents = [
            LoanDocument::AGREEMENT_TOP_UP_PRIVATE,
            LoanDocument::PERSONAL_SHEET_PRIVATE,
        ];

        return $obfuscated ? $private_documents : $documents;
    }
}
