<?php

namespace App\Services;

use App\Abstracts\AbstractDataRobotService;

class DWDataRobotService extends AbstractDataRobotService
{
    protected $warehouse_service;

    public function __construct()
    {
        parent::__construct();

        $this->warehouse_service = resolve('App\Interfaces\IWarehouseService');
    }

    protected function predict(int $application_id, string $model, array $details = null): array
    {
        return $this->warehouse_service->getPrediction($application_id, $model, $details);
    }
}
