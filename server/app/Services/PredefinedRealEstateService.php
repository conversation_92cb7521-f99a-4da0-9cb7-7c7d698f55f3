<?php

namespace App\Services;

use App\Api\V1\Transformers\PredefinedRealEstateTransformer;
use App\Interfaces\IPredefinedRealEstateService;
use App\Models\PredefinedRealEstate;
use App\Traits\Transaction;
use Illuminate\Support\Str;

class PredefinedRealEstateService implements IPredefinedRealEstateService
{
    use Transaction;

    public function generateToken()
    {
        $token = strtolower(Str::random(constants('PREDEFINED_REAL_ESTATE.TOKEN_LENGTH')));

        // Call the same function if the code exists already
        if (PredefinedRealEstate::whereToken($token)->exists()) {
            return $this->generateToken();
        }

        // Otherwise, it's valid and can be used
        return $token;
    }

    public function getDetails($token)
    {
        $predefined_real_estate = PredefinedRealEstate::whereToken($token)
            ->whereIn('status', [PredefinedRealEstate::FREE, PredefinedRealEstate::RESERVED])
            ->whereDisabled(false)
            ->first();

        if (!$predefined_real_estate) {
            return [];
        }

        $transformed_data = fractal()
            ->item($predefined_real_estate->toArray())
            ->transformWith(new PredefinedRealEstateTransformer());

        return $transformed_data->toArray();
    }
}
