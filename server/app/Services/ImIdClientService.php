<?php

namespace App\Services;

use App\Clients\OpenId\OpenIDClient;
use App\Exceptions\ImIdInternalErrorException;
use App\Interfaces\IImIdClientService;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Log;
use Throwable;

class ImIdClientService implements IImIdClientService
{
    public function generateClientRequest($phone_number, $session_id, $notification_token)
    {
        Log::info('ImId generating client request', [
            'phone_number' => $phone_number,
            'session_id' => $session_id,
        ]);

        $params = [
            'client_id' => config('im_id.client_id'),
            'scope' => config('im_id.scope'),
            'aud' => config('im_id.base_url'),
            'response_type' => config('im_id.response_type'),
            'version' => 'mc_si_r2_v1.0',
            'nonce' => generate_uuid(),
            'login_hint' => $phone_number,
            'acr_values' => '4',
            'binding_message' => 'Authorize imID Access',
            'context' => 'Do you want to authenticate using imID?',
            'correlation_id' => $session_id,
            'client_notification_token' => $notification_token,
            'notification_uri' => config('im_id.redirect_url'),
        ];

        Log::info('ImId Client request parameters generated', [
            'params' => $params,
        ]);

        return $params;
    }

    public function getClient($code_verifier, $should_set_key_generator, $client_request)
    {
        Log::info('ImId creating OpenID client', [
            'code_verifier_provided' => $code_verifier,
            'should_set_key_generator' => $should_set_key_generator,
        ]);

        try {
            $client = new OpenIDClient(
                config('im_id.provider_url'),
                config('im_id.client_id'),
                config('im_id.client_secret')
            );

            Log::debug('ImId client instance created successfully');

            $client->setRedirectURL(config('im_id.redirect_url'));
            $client->setResponseTypes(config('im_id.response_type'));
            $client->addScope([config('im_id.scope')]);

            if ($code_verifier) {
                Log::debug('Setting code verifier');
                $client->setCodeVerifier($code_verifier);
            }

            if ($should_set_key_generator) {
                Log::debug('ImId setting private key JWT generator');
                $auth_method = config('im_id.token_endpoint_auth_methods_supported');
                $client->setTokenEndpointAuthMethodsSupported([$auth_method]);

                $client->setPrivateKeyJwtGenerator(function () use ($client_request) {
                    try {
                        $private_key_path = storage_path(config('im_id.private_key_path'));
                        Log::debug('ImId loading private key from path', [
                            'path' => $private_key_path,
                        ]);

                        $private_key = openssl_pkey_get_private('file://'.$private_key_path);

                        if (!$private_key) {
                            Log::warning('ImId failed to load private key', [
                                'openssl_error' => openssl_error_string(),
                            ]);
                            throw new ImIdInternalErrorException();
                        }

                        $jwt = JWT::encode($client_request, $private_key, config('im_id.jwt_algorithm'));
                        Log::debug('ImId JWT encoded successfully');

                        openssl_free_key($private_key);

                        return $jwt;
                    } catch (Throwable $e) {
                        Log::error('ImId JWT generation failed', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                        ]);
                        throw new ImIdInternalErrorException();
                    }
                });
            }

            Log::info('ImId client configured successfully');

            return $client;
        } catch (Throwable $e) {
            Log::error('ImId failed to create or configure client', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ImIdInternalErrorException();
        }
    }
}
