<?php

namespace App\Services\TopUp;

use App\Exceptions\TopUpEligibilityInternalCheckerException;
use App\Models\HC\HcGCCRDTCODE;
use App\Models\Loan;
use App\Models\Paymentable;
use App\Models\PreapprovedCreditOffer;
use App\Models\TopUpSelection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class TopUpEligibilityInternalChecker
{
    protected $loan = null;
    protected $profile = null;
    protected $top_up_selection = null;

    protected $warehouse_service;

    public function __construct($loan, $profile, $top_up_selection)
    {
        $this->warehouse_service = resolve('App\Interfaces\IWarehouseService');

        $this->loan = $loan;
        $this->profile = $profile;
        $this->top_up_selection = $top_up_selection;
    }

    public function run(): bool
    {
        try {
            $this->isLoanClosed()
                ->isMinDisbursementDateNotPassed()
                ->isProfileMissing()
                ->hasExistingTopUpOffer()
                ->hasDisbursementIssue()
                ->isIneligibleOfferDate();

            return false;
        } catch (TopUpEligibilityInternalCheckerException $e) {
            Log::warning('TopUpEligibilityInternalCheckerException, top-up offer postponed',
                [
                    'contract_number' => $this->loan->contract_number,
                    'message' => $e->getMessage(),
                ]
            );

            return true;
        }
    }

    private function isProfileMissing()
    {
        if (is_null($this->profile)) {
            // Postponed about 2 months
            $this->top_up_selection->update([
                'selection_date' => $this->top_up_selection->resolveNextSelectionDate(),
            ]);

            throw new TopUpEligibilityInternalCheckerException('Profile does not exist');
        }

        return $this;
    }

    private function hasDisbursementIssue()
    {
        $payment = $this->loan->payment;

        if ($this->loan->status == Loan::CONFIRMED) {
            // Disbursement(Payment) successfully completed
            if (isset($payment->withdrawn) && $payment->payment_status == Paymentable::PAYMENT_PROCESSED) {
                return $this;
            }
            // The loan disbursement action is in process
            if (is_null($payment->withdrawn) && $payment->payment_status == Paymentable::PAYMENT_PROCESSING) {
                // Postpone the next selection date
                if ($this->loan->isTopUp()) {
                    $case = 1;
                    $this->top_up_selection->update([
                        'selection_date' => $this->top_up_selection->resolveNextSelectionDate(),
                    ]);
                } else {
                    $case = 2;
                    // do not observe this loan the next time
                    $this->top_up_selection->update([
                        'status' => TopUpSelection::NOT_DISBURSED,
                    ]);
                }
            } else {
                // The top-up loan isn't disbursed (PENDING OR EXPIRED PAYMENT),
                //we need to postpone the next selection date
                if ($this->loan->isTopUp()) {
                    // 2 months have passed since the latest top-up sign_date which was not disbursed,
                    //then pass condition
                    if ($this->loan->sign_date->addMonthWithOverflow(constants('TOP_UP_OFFER_PERIOD_IN_MONTHS'))->lte(now())) {
                        return $this;
                    }

                    $case = 3;
                    $this->top_up_selection->update([
                        'selection_date' => $this->top_up_selection->resolveNextSelectionDate(),
                    ]);
                } else {
                    $case = 4;
                    // do not observe this loan the next time
                    $this->top_up_selection->update([
                        'status' => TopUpSelection::NOT_DISBURSED,
                    ]);
                }
            }

            throw new TopUpEligibilityInternalCheckerException("Has disbursement issue, case $case, payment_type {$this->loan->payment_type}, payment_id $payment->id");
        }

        return $this;
    }

    private function isMinDisbursementDateNotPassed()
    {
        $min_required_passed_disb_date = $this->top_up_selection->last_disbursement_date
            ->addMonthWithOverflow(constants('TOP_UP_OFFER_PERIOD_IN_MONTHS'));

        if ($this->top_up_selection->selection_date <= $min_required_passed_disb_date) {
            // Postponed about 2 months
            $this->top_up_selection->update([
                'selection_date' => $this->top_up_selection->resolveNextSelectionDate(),
            ]);

            throw new TopUpEligibilityInternalCheckerException("Min disbursement date not passed, last_disbursement_date {$this->top_up_selection->last_disbursement_date}");
        }

        return $this;
    }

    private function isLoanClosed()
    {
        if (HcGCCRDTCODE::isCreditClosed($this->loan->contract_number)) {
            // Postponed permanently
            $this->top_up_selection->update([
                'status' => TopUpSelection::HC_CLOSED,
            ]);

            throw new TopUpEligibilityInternalCheckerException('Credit is HC closed');
        }

        return $this;
    }

    private function hasExistingTopUpOffer()
    {
        $top_up_offer = PreapprovedCreditOffer::getValidTopUpOfferByLoanType(constants('LOAN_TYPES.OCL'), $this->loan->loan_security->ssn);

        if (isset($top_up_offer)) {
            // Postponed about 2 months
            $this->top_up_selection->update([
                'selection_date' => $this->top_up_selection->resolveNextSelectionDate(),
            ]);

            throw new TopUpEligibilityInternalCheckerException("User has existing top-up offer, existing_top_up_offer_id $top_up_offer->id");
        }

        return $this;
    }

    private function isIneligibleOfferDate()
    {
        $dwh_loan = $this->warehouse_service->getLoan($this->loan->contract_number);

        $considered_offer_date = $this->top_up_selection->selection_date->startOfDay();
        $next_payment_date = Carbon::parse($dwh_loan->NextPaymentDate)->startOfDay();
        // Check if the top-up selection offers date falls within the 5-day window before the next payment date.
        // If the offer date is within this 5-day period, adjust the offer date to selection next day until payment date will in the past
        if ($considered_offer_date >= $next_payment_date->copy()->subDays(5) && $considered_offer_date <= $next_payment_date) {
            // Postponed about 1 day
            $this->top_up_selection->update([
                'selection_date' => $this->top_up_selection->selection_date->addDay(),
            ]);

            throw new TopUpEligibilityInternalCheckerException("Offer date is ineligible, considered_offer_date $considered_offer_date, next_payment_date $next_payment_date");
        }

        return $this;
    }
}
