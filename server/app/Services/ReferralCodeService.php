<?php

namespace App\Services;

use App\Exceptions\ExpiredReferralCodeException;
use App\Exceptions\HcBankException;
use App\Exceptions\InvalidReferralCodeException;
use App\Exceptions\InvalidReferralCodeUrlException;
use App\Exceptions\ReferralCodeOwnerIsCitizenException;
use App\Interfaces\IReferralCodeService;
use App\Jobs\GenerateReferralCodeAgreement;
use App\Jobs\SendReferralCodeRepaySMS;
use App\Jobs\SendReferralCodeVerifySMS;
use App\Models\CitizenPassport;
use App\Models\ReferralCode;
use Carbon\Carbon;
use Exception;
use function Functional\filter;
use function Functional\first;
use function Functional\sort;
use Log;

class ReferralCodeService implements IReferralCodeService
{
    public function generateAgreement($hash)
    {
        $referral_code = ReferralCode::getReferralCode($hash);

        if (!isset($referral_code)) {
            throw new InvalidReferralCodeUrlException();
        }

        if (isset($referral_code->path)) {
            Log::info('Generate Agreement, agreement already exists');

            return;
        }

        Log::info('Generate Agreement', ['referral_code' => $referral_code]);

        GenerateReferralCodeAgreement::dispatch($referral_code);
    }

    public function getReferralCode($hash)
    {
        $referral_code = ReferralCode::getReferralCode($hash);

        Log::info('Get Referral Code', ['referral_code' => $referral_code]);

        return $referral_code;
    }

    public function verifyReferralCode($hash)
    {
        $referral_code = ReferralCode::getReferralCode($hash);

        Log::info('Verify Referral Code', ['referral_code' => $referral_code]);

        if (!isset($referral_code)) {
            throw new InvalidReferralCodeUrlException();
        }

        if ($referral_code->expired) {
            throw new ExpiredReferralCodeException();
        }

        $now = Carbon::now();

        $referral_code->update([
            'verified' => true,
            'verified_at' => $now,
        ]);

        $citizen = CitizenPassport::where([
            'passport_number' => $referral_code->ssn,
            'type' => constants('SOC_CARD'),
        ])->latest()->first()->citizen;

        SendReferralCodeVerifySMS::dispatch([
            'phone_number' => $citizen->phone_number,
            'referral_code' => $referral_code->code,
        ]);

        return $referral_code;
    }

    public function setReferralCode($code)
    {
        $securityService = resolve('App\Interfaces\ISecurityService');
        $loan_security = $securityService->resolveLoanSecurity();

        $referral_code = ReferralCode::whereCode($code)->whereVerified(true)->first();

        if (!isset($referral_code)) {
            throw new InvalidReferralCodeException();
        }

        if ($referral_code->ssn === $loan_security->ssn) {
            throw new ReferralCodeOwnerIsCitizenException();
        }

        $loan_security->update(['referral_code' => $code]);
    }

    public function applyReferralCode($current_loan)
    {
        try {
            $loan_security = $current_loan->loan_security;
            $loan_type_id = $current_loan->loan_type_id;

            if (!isset($loan_security->referral_code)) {
                return;
            }

            $referral_code = ReferralCode::where('code', $loan_security->referral_code)->first();

            $loan = $this->getRepaymentLoan($referral_code->ssn);

            if (empty($loan)) {
                return;
            }

            $repayment = $this->repay($loan, $loan_type_id);

            $referral_code->repayments()->create([
                'contract_number' => $loan['loanid'],
                'loan_type_id' => $loan['origindet'],
                'amount' => $repayment,
            ]);

            $citizen = CitizenPassport::where('passport_number', $referral_code->ssn)->latest()->first()->citizen;
            SendReferralCodeRepaySMS::dispatch([
                'phone_number' => $citizen->phone_number,
                'referral_code' => $loan_security->referral_code,
                'repayment' => $repayment,
                'contract_number' => $loan['loanid'],
            ]);
        } catch (HcBankException $e) {
            Log::critical('Apply Referral Code HcBank Exception', ['current loan ' => $current_loan->contract_number, 'loan' => $loan, 'error' => $e->getTraceAsString()]);
        } catch (Exception $e) {
            Log::critical('Apply Referral Code Exception', ['current_loan' => $current_loan, 'loan' => $loan, 'error' => $e->getTraceAsString()]);
        }
    }

    protected function getRepaymentLoan($ssn)
    {
        $werhouse_service = resolve('App\Interfaces\IWarehouseService');
        $loans = $werhouse_service->getLoansByType($ssn, [constants('LOAN_TYPES.OCL'), constants('LOAN_TYPES.OVL')]);
        Log::info('Referral Code Get Repayment', ['loans' => $loans]);

        if (empty($loans)) {
            return null;
        }

        // Filter only disbursed loans
        $filtered_loans = filter($loans, function ($loan) {
            return $loan['is_disbursed'] === '1';
        });

        // Choose OCL or the biggist one loan
        $sorted_loans = sort($filtered_loans, function ($prev, $curr) {
            if ($prev['outs'] === $curr['outs']) {
                return 0;
            }

            return $curr['outs'] > $prev['outs'] ? 1 : -1;
        });

        $loan = first($sorted_loans, function ($loan) {
            return $loan['origindet'] == constants('LOAN_TYPES.OCL');
        });

        if (empty($loan)) {
            $loan = first($loans);
        }

        if ($loan['outs'] <= 0) {
            return null;
        }

        Log::info('Referral Code Get Repayment', ['loan' => $loan]);

        return $loan;
    }

    protected function repay($loan, $loan_type_id)
    {
        $repayment = constants("REFERRAL_CODE.REPAYMENT.$loan_type_id");
        if ($loan['outs'] < $repayment) {
            $repayment = $loan['outs'];
        }

        $hc_bank_service = resolve('App\Interfaces\IHcBankService');
        $pay_date = Carbon::now()->format(constants('REFERRAL_CODE.DATE_FORMAT'));

        $transaction_id = $hc_bank_service->generateTransactionId();

        $payment_id = $hc_bank_service->repay($loan['loanid'], $repayment, $transaction_id, $pay_date);
        $hc_bank_service->confirmRepay($loan['loanid'], $repayment, $transaction_id, $pay_date, $payment_id);

        return $repayment;
    }
}
