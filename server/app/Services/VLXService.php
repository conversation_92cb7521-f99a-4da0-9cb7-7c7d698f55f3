<?php

namespace App\Services;

use App\Exceptions\InvalidVeloxCitizenException;
use App\Exceptions\MerchantBlacklistedException;
use App\Exceptions\ProductCategoryIsNotActiveException;
use App\Helpers\XmlHelper;
use App\Interfaces\IVLXService;
use App\Models\Loan;
use App\Models\LoanSecurity;
use App\Models\MerchantBlacklist;
use App\Models\VeloxLoanOfferDetail;
use App\Models\VeloxProductCategory;
use App\Traits\LoanConfirmationLock;
use Exception;
use function Functional\reduce_left;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class VLXService implements IVLXService
{
    use LoanConfirmationLock;

    protected $redis_service;
    protected $client;

    public function __construct()
    {
        $this->redis_service = resolve('App\Interfaces\IRedisService');
    }

    protected function getConnection()
    {
        if (!$this->client) {
            $this->client = new Client([
                'base_uri' => env('VELOX_BASE_URL'),
            ]);
        }

        return $this->client;
    }

    public function parseAndCacheCitizen($citizen, $ssn, $document_number)
    {
        $this->parseAndCacheEkengData($citizen['ekeng'], $ssn, $document_number);
        $this->parseAndCacheNorkData($citizen['nork'], $ssn);
        $this->parseAndCacheAcraData($citizen['acra'], $ssn);
    }

    public function getCitizenFromCache($document_number)
    {
        $citizen = [];

        $citizen['ekeng'] = $this->redis_service->get("$document_number:ekeng_vlx");
        $citizen['nork'] = $this->redis_service->get("$document_number:nork_vlx");
        $citizen['acra'] = $this->redis_service->get("$document_number:acra_vlx");

        Log::info('Get Velox citizen data from cache', ['document_number' => $document_number, 'citizen' => $citizen]);

        return $citizen;
    }

    public function composeVeloxDetailsPayload($details, $loan_security)
    {
        return reduce_left($details['pledge'], function ($value, $index, $collection, $reduction) use ($details) {
            $reduction[] = [
                'product_name' => $value['name'],
                'product_price' => $value['price'],
                'product_ratio' => $value['ratio'],
                'product_category_id' => $details['productCategoryId'],
                'product_category_name' => $details['productCategoryName'],
                'product_merchant_name' => $details['merchantName'],
                'product_merchant_address' => $details['merchantAddress'],
            ];

            return $reduction;
        }, []);
    }

    public function createVeloxDetails($loan_security, $payload)
    {
        $loan_security->velox_details()->createMany($payload);
    }

    public function updateVeloxDetails($loan_security, $payload)
    {
        $loan_security->velox_details()->update($payload);
    }

    public function createVeloxLoanOfferDetails($loan_security, $payload)
    {
        if (!empty($payload)) {
            $loan_security->velox_loan_offer_detail()->create($payload);
        }
    }

    protected function parseAndCacheEkengData($ekeng_data, $ssn, $document_number)
    {
        $result = json_decode(json_encode($ekeng_data)) ?? null;

        if (isset($result) && !empty($result) && !empty($result->passport_data)) {
            Log::debug('Storing Citizen EKENG data from Velox in cache with document number and ssn', ['document_number' => $document_number, 'ssn' => $ssn]);
            $this->redis_service->update("$document_number:ekeng_vlx", $result->passport_data, constants('ONE_DAY_IN_MINUTES'));
            $this->redis_service->update("$ssn:ekeng_vlx", (array) $result, constants('ONE_DAY_IN_MINUTES'));
            Log::debug('Citizen EKENG data from Velox stored in cache with document number and ssn', ['document_number' => $document_number, 'ssn' => $ssn]);
        } else {
            throw new InvalidVeloxCitizenException();
        }
    }

    protected function parseAndCacheNorkData($nork_xml, $ssn)
    {
        $result = XmlHelper::xmlToArrayOrObject($nork_xml, true, false)->sBody->GetUserData_v2018Response ?? null;

        $private_data = $result->argPrivateData ?? null;
        $work_data = isset($result->argWorkData) ? $result->argWorkData->WorkData_v2018 : [];

        if (isset($private_data) && !empty($private_data)) {
            $citizen = array_merge_recursive((array) $private_data, ['WorkData' => json_decode(json_encode($work_data), true)]);

            Log::debug('Storing citizen NORK data from Velox in cache with key', ['social_card_number' => $ssn]);
            $this->redis_service->update("$ssn:nork_vlx", $citizen, constants('ONE_DAY_IN_MINUTES'));
            Log::debug('Citizen NORK data from Velox stored in cache with key', ['social_card_number' => $ssn]);
        } else {
            throw new InvalidVeloxCitizenException();
        }
    }

    protected function parseAndCacheAcraData($acra_xml, $ssn)
    {
        $result = XmlHelper::xmlToArrayOrObject($acra_xml) ?? null;

        if (isset($result) && !empty($result)) {
            Log::debug('Storing citizen ACRA data from Velox in cache with key', ['social_card_number' => $ssn]);
            $this->redis_service->update("$ssn:acra_vlx", $result, constants('ONE_DAY_IN_MINUTES'));
            Log::debug('Citizen ACRA data from Velox stored in cache with key', ['social_card_number' => $ssn]);
        } else {
            throw new InvalidVeloxCitizenException();
        }
    }

    public function requestVeloxLoanDocuments($lender_token, $document_types = [])
    {
        try {
            Log::info('Requesting Velox loan documents', ['lender_token' => $lender_token, 'document_types' => $document_types]);

            $client = $this->getConnection();

            $result = [];

            foreach ($document_types as $type) {
                try {
                    $response = $client->post('/lender/documents/download', [
                        'json' => [
                            'lenderToken' => $lender_token,
                            'documentType' => $type,
                        ],
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Accept' => 'application/pdf',
                        ],
                    ]);

                    $body = $response->getBody();

                    $result[$type] = $body->getContents();
                } catch (Exception $e) {
                    Log::critical('Requesting Velox loan documents, Exception', ['lender_token' => $lender_token, 'There are not requested type document:' => $type, 'message' => $e->getMessage()]);
                    continue;
                }
            }

            Log::info('Requesting Velox loan documents success', ['lender_token' => $lender_token]);

            return $result;
        } catch (RequestException $e) {
            Log::critical('Requesting Velox loan documents, RequestException', ['lender_token' => $lender_token, 'error' => $e->getTraceAsString()]);
        } catch (Exception $e) {
            Log::critical('Requesting Velox loan documents, Exception', ['lender_token' => $lender_token, 'error' => $e->getTraceAsString()]);
        }
    }

    public function checkMerchant($merchant_name)
    {
        $is_merchant_blacklisted = MerchantBlacklist::isBlacklisted($merchant_name, constants('LOAN_TYPES.VLX'));

        if ($is_merchant_blacklisted) {
            throw new MerchantBlacklistedException();
        }
    }

    public function checkProductCategoryIsActive($category_id)
    {
        $is_category_active = VeloxProductCategory::where('category_id', $category_id)
            ->where('is_active', true)
            ->exists();

        if (!$is_category_active) {
            throw new ProductCategoryIsNotActiveException();
        }
    }

    public function getLoanOfferStatus($identifier): array
    {
        $loan_security = LoanSecurity::where('suuid', $identifier)
            ->where('loan_type_id', constants('LOAN_TYPES.VLX'))
            ->first();

        $result = [
            'suuid' => $identifier,
            'contract_number' => $loan_security->velox_loan_offer_detail->contract_number ?? null,
            'status' => VeloxLoanOfferDetail::PENDING,
            'approved_at' => null,
        ];

        $loan = $loan_security->loan ?? null;
        if (!$loan_security) {
            $result['status'] = VeloxLoanOfferDetail::NOT_FOUND;
        } elseif ($loan && $loan->status === Loan::CONFIRMED) {
            $result['status'] = VeloxLoanOfferDetail::APPROVED;
            $result['approved_at'] = $loan_security->loan->sign_date->toIso8601ZuluString();
        } elseif ($loan_security->suuid_exp < now()) {
            $result['status'] = VeloxLoanOfferDetail::DECLINED;
        }

        return $result;
    }
}
