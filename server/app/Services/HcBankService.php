<?php

namespace App\Services;

use App;
use App\Exceptions\HcBankException;
use App\Exceptions\Pallaton\HcBankContractNotFoundException;
use App\Interfaces\IHcBankService;
use App\Traits\MeasureExecutionTime;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Log;

class HcBankService extends ExternalXmlService implements IHcBankService
{
    use MeasureExecutionTime;

    protected const SUCCESS = 0;
    protected const APPROVE = 1;
    protected const CONTRACT_NOT_FOUND = '130';
    protected const TIME_STAMP = 'D, d M Y H:i:s';

    protected $client = null;
    protected $partner_id = null;
    protected $partner_sk = null;
    protected $our_sk = null;
    protected $hc_domain = null;
    protected $hc_path = null;
    protected $repayments_url = null;
    protected $full_repayments_url = null;
    protected $warehouse_service = null;

    public function __construct()
    {
        $this->hc_domain = env('HC_URL_DOMAIN');
        $this->hc_path = env('HC_URL_PATH');
        $this->setHCCredentials();
        $this->warehouse_service = resolve('App\Interfaces\IWarehouseService');
    }

    protected function getConnection()
    {
        if (!$this->client) {
            $this->client = new Client([
                'base_uri' => "$this->hc_domain/$this->hc_path/",
                'cookies' => false, // Disable automatic cookie handling
            ]);
        }

        return $this->client;
    }

    protected function composeAuthHeaders($http_method, $canonicalized_resource)
    {
        // Because if APP_ENV='bnpl_mocked_credit_line_creation' this API does not work correctly (the reason is mocked old Carbon Time),
        // that's why we need to use real now during the request
        if (App::environment('bnpl_mocked_credit_line_creation')) {
            $test_now = Carbon::now();
            Carbon::setTestNow();
        }

        $time_stamp = Carbon::now()->format(self::TIME_STAMP).' GMT';

        $signature_base_string = "$http_method\r\n$time_stamp\r\n$canonicalized_resource";
        Log::info('Auth Signature Base String', ['string' => $signature_base_string]);

        $signature = $this->createSignature($signature_base_string);
        // We need to reset mocked time after the request
        if (App::environment('bnpl_mocked_credit_line_creation')) {
            Carbon::setTestNow($test_now);
        }

        return [
            'TimeStamp' => $time_stamp,
            'Authorization' => "AS-WS $this->partner_id:$signature",
        ];
    }

    public function setHCCredentials($partner_name = null)
    {
        switch ($partner_name) {
            case constants('BANK_REPORT_PARSER.DOC_TYPES.CONVERSE'):
                $this->partner_id = env('HC_CONVERSE_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_CONVERSE_BANK_PARTNER_SK');
                $this->our_sk = env('HC_CONVERSE_BANK_OUR_SK');
                break;
            case constants('BANK_REPORT_PARSER.DOC_TYPES.AEB'):
                $this->partner_id = env('HC_ARMECONOM_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_ARMECONOM_BANK_PARTNER_SK');
                $this->our_sk = env('HC_ARMECONOM_BANK_OUR_SK');
                break;
            case constants('BANK_REPORT_PARSER.DOC_TYPES.ABB'):
                $this->partner_id = env('HC_ARMBUSINESS_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_ARMBUSINESS_BANK_PARTNER_SK');
                $this->our_sk = env('HC_ARMBUSINESS_BANK_OUR_SK');
                break;
            case constants('BANK_REPORT_PARSER.DOC_TYPES.AMERIA'):
                $this->partner_id = env('HC_AMERIA_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_AMERIA_BANK_PARTNER_SK');
                $this->our_sk = env('HC_AMERIA_BANK_OUR_SK');
                break;
            case constants('BANK_REPORT_PARSER.DOC_TYPES.EVOCA'):
                $this->partner_id = env('HC_EVOCA_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_EVOCA_BANK_PARTNER_SK');
                $this->our_sk = env('HC_EVOCA_BANK_OUR_SK');
                break;
            case constants('BANK_REPORT_PARSER.DOC_TYPES.ARDSHIN'):
                $this->partner_id = env('HC_ARDSHIN_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_ARDSHIN_BANK_PARTNER_SK');
                $this->our_sk = env('HC_ARDSHIN_BANK_OUR_SK');
                break;
            case constants('BANK_REPORT_PARSER.DOC_TYPES.ARARAT'):
                $this->partner_id = env('HC_ARARAT_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_ARARAT_BANK_PARTNER_SK');
                $this->our_sk = env('HC_ARARAT_BANK_OUR_SK');
                break;
            case constants('VPOS_BANKS.ARARAT'):
                $this->partner_id = env('HC_PAYMENT_ARARAT_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_PAYMENT_ARARAT_BANK_PARTNER_SK');
                $this->our_sk = env('HC_PAYMENT_ARARAT_BANK_OUR_SK');
                break;
            case constants('VPOS_BANKS.EVOCA'):
                $this->partner_id = env('HC_PAYMENT_EVOCA_BANK_PARTNER_ID');
                $this->partner_sk = env('HC_PAYMENT_EVOCA_BANK_PARTNER_SK');
                $this->our_sk = env('HC_PAYMENT_EVOCA_BANK_OUR_SK');
                break;
            default:
                $this->partner_id = env('HC_PARTNER_ID');
                $this->partner_sk = env('HC_PARTNER_SK');
                $this->our_sk = env('HC_OUR_SK');
                break;
        }

        $this->repayments_url = strtolower('/'.$this->hc_path.'/partners/'.$this->partner_id.'/contractrepaymentswithrepaytype');
        $this->full_repayments_url = strtolower('/'.$this->hc_path.'/partners/'.$this->partner_id.'/contractrepayments');
    }

    protected function createSignature($sign_data)
    {
        return base64_encode(hash_hmac('sha256', $sign_data, $this->partner_sk, true));
    }

    public function getDebt($contract_number)
    {
        $result = null;
        $hc_path = strtolower($this->hc_path);
        $url = "/$hc_path/partners/$this->partner_id/contractdebts?code=$contract_number&paymentcurrency=AMD";
        $auth_headers = $this->composeAuthHeaders('GET', $url);

        try {
            $client = $this->getConnection();
            $response = $this->measureExecutionTime(function () use ($client, $contract_number, $auth_headers) {
                return $client->get("Partners/$this->partner_id/ContractDebts?Code=$contract_number&paymentcurrency=AMD", [
                    'headers' => $auth_headers,
                ]);
            },
                __FUNCTION__,
                ['contract_number' => $contract_number]
            );

            $result = $this->xmlToArray($response->getBody());
            Log::info('HcBank get debt response', ['response' => $result]);

            if ($result && $result['Status'] == self::SUCCESS) {
                return $result;
            }
        } catch (Exception $e) {
            Log::error('HcBank Get Debt Exception', ['error' => $e->getMessage()]);
            throw new HcBankException();
        }

        if ($result && $result['Status'] == self::CONTRACT_NOT_FOUND) {
            throw new HcBankContractNotFoundException();
        }

        Log::error('HcBank Get Debt Error');
        throw new HcBankException();
    }

    public function getFullDebt($contract_number)
    {
        $hc_path = strtolower($this->hc_path);
        $url = "/$hc_path/partners/$this->partner_id/contractfulldebts?code=$contract_number&paymentcurrency=AMD";
        $auth_headers = $this->composeAuthHeaders('GET', $url);

        try {
            $client = $this->getConnection();
            $response = $this->measureExecutionTime(function () use ($client, $contract_number, $auth_headers) {
                return $client->get("Partners/$this->partner_id/ContractFullDebts?Code=$contract_number&PaymentCurrency=AMD", [
                    'headers' => $auth_headers,
                ]);
            },
                __FUNCTION__,
                ['contract_number' => $contract_number]
            );

            $result = $this->xmlToArray($response->getBody());
            Log::info('HcBank get full debt response', ['response' => $result]);

            if ($result && $result['Status'] == self::SUCCESS) {
                return $result;
            }
        } catch (Exception $e) {
            Log::error('HcBank Get Full Debt Exception', ['error' => $e->getMessage()]);
            throw new HcBankException();
        }

        if ($result && $result['Status'] == self::CONTRACT_NOT_FOUND) {
            throw new HcBankContractNotFoundException();
        }

        Log::error('HcBank Get Full Debt Error');
        throw new HcBankException();
    }

    public function repay($contract_number, $amount, $transaction_id, $pay_date, $repay_type = 0)
    {
        $auth_headers = $this->composeAuthHeaders('POST', $this->repayments_url);

        $date_to_sign = 'amount='.number_format($amount, 2, '.', '').
            "\r\ncode=$contract_number\r\ncomment=\r\ncurrency=AMD\r\npartnerid=$this->partner_id\r\npassport=\r\npaydate=$pay_date\r\nrepaytype=$repay_type\r\ntransactionid=$transaction_id";

        $xml_data = [
            'Amount' => $amount,
            'Code' => $contract_number,
            'PayDate' => $pay_date,
            'PartnerID' => $this->partner_id,
            'RepayType' => $repay_type,
            'Signature' => $this->createSignature($date_to_sign),
            'TransactionID' => $transaction_id,
        ];

        $xml = $this->prepareXML('xml/hcBank/repayment', $xml_data);
        Log::info('repay', ['xml' => $xml]);

        try {
            $client = $this->getConnection();
            $response = $this->measureExecutionTime(function () use ($client, $auth_headers, $xml) {
                return $client->post("Partners/$this->partner_id/ContractRepaymentsWithRepayType", [
                    'headers' => array_merge($auth_headers, [
                        'Content-Type' => 'text/xml',
                    ]),
                    'body' => $xml,
                ]);
            },
                __FUNCTION__,
                ['contract_number' => $contract_number]
            );

            $result = $this->xmlToArray($response->getBody());
            Log::info('repay Response', ['response' => $result]);

            if (!($result && $result['Status'] == self::SUCCESS && $result['State'] == self::SUCCESS)) {
                throw new HcBankException();
            }

            return $result['PaymentID'];
        } catch (Exception $e) {
            Log::error('HcBank repay Exception', ['error' => $e->getMessage()]);
            throw new HcBankException();
        }
    }

    public function confirmRepay($contract_number, $amount, $transaction_id, $pay_date, $payment_id, $repay_type = 0)
    {
        $auth_headers = $this->composeAuthHeaders('POST',
            $this->repayments_url.'/'.$payment_id
        );
        $date_to_sign = 'amount='.number_format($amount, 2, '.', '').
            "\r\ncode=$contract_number\r\ncomment=\r\ncurrency=AMD\r\npartnerid=$this->partner_id\r\npassport=\r\npaydate=$pay_date\r\npaymentid=$payment_id\r\nrepaytype=$repay_type\r\ntransactionid=$transaction_id";

        $xml_data = [
            'Amount' => $amount,
            'Code' => $contract_number,
            'PayDate' => $pay_date,
            'PartnerID' => $this->partner_id,
            'PaymentID' => $payment_id,
            'RepayType' => $repay_type,
            'Signature' => $this->createSignature($date_to_sign),
            'TransactionID' => $transaction_id,
        ];

        $xml = $this->prepareXML('xml/hcBank/confirm_repayment', $xml_data);
        Log::info('confirmRepay', ['xml' => $xml]);
        try {
            $client = $this->getConnection();
            $response = $this->measureExecutionTime(function () use ($client, $auth_headers, $xml, $payment_id) {
                return $client->post("Partners/$this->partner_id/ContractRepaymentsWithRepayType/$payment_id", [
                    'headers' => array_merge($auth_headers, [
                        'Content-Type' => 'text/xml',
                    ]),
                    'body' => $xml,
                ]);
            },
                __FUNCTION__,
                ['contract_number' => $contract_number]
            );

            $result = $this->xmlToArray($response->getBody());
            Log::info('confirmRepay Response', ['response' => $result]);
            if (!($result && $result['Status'] == self::SUCCESS && $result['State'] == self::APPROVE)) {
                throw new HcBankException();
            }
        } catch (Exception $e) {
            Log::error('HcBank confirmRepay Exception', ['error' => $e->getMessage()]);
            throw new HcBankException();
        }
    }

    public function fullRepay($contract_number, $amount, $transaction_id, $pay_date)
    {
        $auth_headers = $this->composeAuthHeaders('POST', $this->full_repayments_url);

        $date_to_sign = 'amount='.number_format($amount, 2, '.', '').
            "\r\ncode=$contract_number\r\ncurrency=AMD\r\npartnerid=$this->partner_id\r\npassport=\r\npaydate=$pay_date\r\ntransactionid=$transaction_id";

        $xml_data = [
            'Amount' => $amount,
            'Code' => $contract_number,
            'PayDate' => $pay_date,
            'PartnerID' => $this->partner_id,
            'Signature' => $this->createSignature($date_to_sign),
            'TransactionID' => $transaction_id,
        ];

        $xml = $this->prepareXML('xml/hcBank/full_repayment', $xml_data);
        Log::info('full repay', ['xml' => $xml]);

        try {
            $client = $this->getConnection();

            $response = $this->measureExecutionTime(function () use ($client, $auth_headers, $xml) {
                return $client->post("Partners/$this->partner_id/ContractRepayments", [
                    'headers' => array_merge($auth_headers, [
                        'Content-Type' => 'text/xml',
                    ]),
                    'body' => $xml,
                ]);
            },
                __FUNCTION__,
                ['contract_number' => $contract_number]
            );

            $result = $this->xmlToArray($response->getBody());
            Log::info('full repay Response', ['response' => $result]);

            if (!($result && $result['Status'] == self::SUCCESS && $result['State'] == self::SUCCESS)) {
                throw new HcBankException();
            }

            return $result['PaymentID'];
        } catch (Exception $e) {
            Log::error('HcBank full repay Exception', ['error' => $e->getMessage()]);
            throw new HcBankException();
        }
    }

    public function confirmFullRepay($contract_number, $amount, $transaction_id, $pay_date, $payment_id)
    {
        $auth_headers = $this->composeAuthHeaders('POST',
            $this->full_repayments_url.'/'.$payment_id
        );
        $date_to_sign = 'amount='.number_format($amount, 2, '.', '').
            "\r\ncode=$contract_number\r\ncurrency=AMD\r\npartnerid=$this->partner_id\r\npassport=\r\npaydate=$pay_date\r\npaymentid=$payment_id\r\ntransactionid=$transaction_id";

        $xml_data = [
            'Amount' => $amount,
            'Code' => $contract_number,
            'PayDate' => $pay_date,
            'PartnerID' => $this->partner_id,
            'PaymentID' => $payment_id,
            'Signature' => $this->createSignature($date_to_sign),
            'TransactionID' => $transaction_id,
        ];

        $xml = $this->prepareXML('xml/hcBank/confirm_full_repayment', $xml_data);
        Log::info('confirmFullRepay', ['xml' => $xml]);
        try {
            $client = $this->getConnection();
            $response = $this->measureExecutionTime(function () use ($client, $auth_headers, $xml, $payment_id) {
                return $client->post("Partners/$this->partner_id/ContractRepayments/$payment_id", [
                    'headers' => array_merge($auth_headers, [
                        'Content-Type' => 'text/xml',
                    ]),
                    'body' => $xml,
                ]);
            },
                __FUNCTION__,
                ['contract_number' => $contract_number]
            );

            $result = $this->xmlToArray($response->getBody());
            Log::info('confirmFullRepay Response', ['response' => $result]);
            if (!($result && $result['Status'] == self::SUCCESS && $result['State'] == self::APPROVE)) {
                throw new HcBankException();
            }
        } catch (Exception $e) {
            Log::error('HcBank confirmFullRepay Exception', ['error' => $e->getMessage()]);
            throw new HcBankException();
        }
    }

    public function generateTransactionId()
    {
        do {
            //to avoid duplication transaction id, need to make sure it's unique. The partner_id, transaction_id pair is unique
            $transaction_id = rand(0, **********);
        } while ($this->warehouse_service->isPaymentExists($this->partner_id, $transaction_id));

        return $transaction_id;
    }
}
