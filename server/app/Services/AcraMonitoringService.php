<?php

namespace App\Services;

use App\Abstracts\AbstractAcraService;
use App\Exceptions\CitizenNotFoundException;
use App\Exceptions\InternalErrorException;
use App\Models\AcraRequest;
use Exception;
use function Functional\reduce_left;
use Log;

class AcraMonitoringService extends AbstractAcraService
{
    protected $report_type;

    public function __construct($report_type)
    {
        parent::__construct($report_type ?? constants('ACRA_MONITORING.MONITORING_RETRO'));

        $this->ACRA_URL = env('ACRA_MONITORING_URL');
    }

    public function getCitizens($citizens)
    {
        Log::info('Requesting citizen data from ACRA monitoring', $citizens);

        try {
            $acra_login_payload = $this->composeAcraCitizensLoginPayload($citizens);
            Log::debug('Logging in to ACRA', ['payload' => $acra_login_payload]);

            $login_result = $this->login([
                'ReqID' => $acra_login_payload['ReqID'],
                'ACRA_USER' => $this->ACRA_USER,
                'ACRA_PASSWORD' => $this->ACRA_PASSWORD,
            ]);

            $this->storeThirdPartyResponse(new AcraRequest(), [
                'content' => $citizens,
                'is_monitoring' => true,
                'report_type' => $this->report_type,
                'date' => now(),
            ]);

            Log::debug('ACRA login response', ['response' => $login_result]);

            $acra_payload = $acra_login_payload;
            $acra_payload['SID'] = $login_result;

            Log::debug('Getting citizen data from ACRA', ['payload' => $acra_payload]);

            return $this->requestToAcra($acra_payload);
        } catch (CitizenNotFoundException $e) {
            Log::error('Requesting citizen data from ACRA monitoring, CitizenNotFoundException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (InternalErrorException $e) {
            Log::error('Requesting citizen data from ACRA monitoring, InternalErrorException', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Requesting citizen data from ACRA monitoring, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException('Something went wrong', [], constants('ALERT.ACTIONS.ACRA_MONITORING'), null, true, []);
        }
    }

    protected function composeAcraCitizensLoginPayload($citizens)
    {
        Log::debug('Composing ACRA monitoring login payload');

        $payload = $this->getRequestPayload();

        $citizen_payload['citizens'] = reduce_left($citizens, function ($citizen, $key, $collection, $reduction) {
            Log::debug('Getting ACRA monitoring login payload schema');

            $citizen['Participient'] = str_random(12);

            array_push($reduction, $citizen);

            return $reduction;
        }, []);

        $acra_login_payload = array_merge($payload, $citizen_payload);
        Log::debug('Compose Acra Login Payload', ['acra_login_payload' => $acra_login_payload]);

        return $acra_login_payload;
    }

    protected function getRequestPayload()
    {
        return array_merge(parent::getRequestPayload(),
            constants('ACRA_LOGIN.MONITORING_VALUES')
        );
    }
}
