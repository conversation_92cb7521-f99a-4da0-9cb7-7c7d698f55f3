<?php

namespace App\Services\TaxService\Dto\Entities;

use Illuminate\Contracts\Support\Arrayable;

class InvoiceDetailsDto implements Arrayable
{
    /**
     * @var string|null
     */
    private $id;

    /**
     * @var string|null
     */
    private $status;

    /**
     * @var string|null
     */
    private $createdAt;

    /**
     * @var string|null
     */
    private $deliveredAt;

    /**
     * @var string|null
     */
    private $additionalInfo;

    /**
     * @var string|null
     */
    private $userName;

    /**
     * @var string|null
     */
    private $serialNo;

    /**
     * @var int|null
     */
    private $version;

    /**
     * @var string|null
     */
    private $behalfOf;

    /**
     * @var bool|null
     */
    private $isCorrector;

    /**
     * @var string|null
     */
    private $supplierTin;

    /**
     * @var string|null
     */
    private $supplierVatTin;

    /**
     * @var string|null
     */
    private $supplierName;

    /**
     * @var string|null
     */
    private $supplierAddress;

    /**
     * @var string|null
     */
    private $supplierBank;

    /**
     * @var string|null
     */
    private $supplierAccNo;

    /**
     * @var bool|null
     */
    private $beneficiaryHasNoTin;

    /**
     * @var string|null
     */
    private $buyerTin;

    /**
     * @var string|null
     */
    private $buyerVatTin;

    /**
     * @var string|null
     */
    private $buyerName;

    /**
     * @var string|null
     */
    private $buyerAddress;

    /**
     * @var bool|null
     */
    private $buyerHasNoTin;

    /**
     * @var bool|null
     */
    private $buyerIsNatural;

    /**
     * @var bool|null
     */
    private $citizenArmenia;

    /**
     * @var bool|null
     */
    private $physicalPersonChecked;

    /**
     * @var bool|null
     */
    private $socialCostPersonChecked;

    /**
     * @var float|null
     */
    private $totalValue;

    /**
     * @var float|null
     */
    private $totalVatAmount;

    /**
     * @var float|null
     */
    private $total;

    /**
     * @var bool|null
     */
    private $isBatch;

    /**
     * @var string|null
     */
    private $supplierSignId;

    /**
     * @var string|null
     */
    private $buyerSignId;

    /**
     * @var string|null
     */
    private $issuedAt;

    /**
     * @var string|null
     */
    private $approvedAt;

    /**
     * @var string|null
     */
    private $supplierCn;

    /**
     * @var string|null
     */
    private $buyerCn;

    /**
     * @var string|null
     */
    private $supplierUser;

    /**
     * @var string|null
     */
    private $buyerUser;

    /**
     * @var string|null
     */
    private $source;

    public function __construct(array $data)
    {
        $this->setAllValues($data);
    }

    public function setAllValues(array $fields): void
    {
        foreach ($fields as $field => $value) {
            $this->{$field} = $value;
        }
    }

    public function getValue($name)
    {
        return $this->{$name};
    }

    /**
     * @param $value mixed
     */
    public function setValue(string $field, $value): void
    {
        $this->{$field} = $value;
    }

    public static function createFromArray(array $data): self
    {
        return new self($data);
    }

    public function toArray(): array
    {
        return [
            'supplier_acc_no' => $this->supplierAccNo,
        ];
    }
}
