<?php

namespace App\Services\TaxService\Dto\Requests;

use App\Dtos\AbstractRequestDto;

class InvoiceProductDetailsByIdRequestDto extends AbstractRequestDto
{
    /**
     * @var string|null
     */
    protected $clientTin = null;

    /**
     * @var int|null
     */
    protected $invoiceId = null;
    /**
     * @var int|null
     */
    protected $offSet = 1;

    /**
     * @var int|null
     */
    protected $limit = 20;

    /**
     * {@inheritdoc}
     */
    public function toArray(): array
    {
        return [
            'payload' => array_filter([
                'clientTin' => $this->clientTin,
                'invoiceId' => $this->invoiceId,
                'offSet' => $this->offSet,
                'limit' => $this->limit,
            ], function ($value) {
                return $value !== null;
            }),
        ];
    }
}
