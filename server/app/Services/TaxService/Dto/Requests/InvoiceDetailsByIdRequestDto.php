<?php

namespace App\Services\TaxService\Dto\Requests;

use App\Dtos\AbstractRequestDto;

class InvoiceDetailsByIdRequestDto extends AbstractRequestDto
{
    /**
     * @var string|null
     */
    protected $clientTin = null;

    /**
     * @var int|null
     */
    protected $id = null;

    /**
     * {@inheritdoc}
     */
    public function toArray(): array
    {
        return [
            'payload' => array_filter([
                'clientTin' => $this->clientTin,
                'id' => $this->id,
            ], function ($value) {
                return $value !== null;
            }),
        ];
    }
}
