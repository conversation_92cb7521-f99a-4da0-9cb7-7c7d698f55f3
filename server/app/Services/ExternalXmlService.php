<?php

namespace App\Services;

use Illuminate\Support\Facades\View;

abstract class ExternalXmlService extends ExternalService
{
    protected function xmlToArray($data)
    {
        $xml = simplexml_load_string((string) $data, 'SimpleXMLElement', LIBXML_NOCDATA);
        $result = json_decode(json_encode((array) $xml), 1);

        return $result;
    }

    protected function prepareXML($resource, $data)
    {
        return View::make($resource, $data)->render();
    }
}
