<?php

namespace App\Services;

use App\Interfaces\IRedisService;
use Cache;

class RedisService implements IRedisService
{
    /**
     *  The method will only add the item to the cache if it does not already exist in the cache store.
     */
    public function set($key, $value, $ttl)
    {
        return Cache::store('redis')->add($key, $value, $ttl);
    }

    /**
     *  The method will add the item to the cache or will override key if it already exists in the cache store.
     */
    public function update($key, $value, $ttl)
    {
        return Cache::store('redis')->put($key, $value, $ttl);
    }

    public function get($key)
    {
        return Cache::store('redis')->get($key);
    }

    /**
     * @param $key
     * @param int $ttl in seconds
     *
     * @return mixed
     */
    public function lock($key, int $ttl = 0)
    {
        return Cache::lock($key, $ttl);
    }

    public function exists($key)
    {
        return Cache::store('redis')->has($key);
    }

    public function remove($key)
    {
        return Cache::store('redis')->forget($key);
    }

    public function bulkRemove($keys)
    {
        foreach ($keys as $key) {
            $this->remove($key);
        }
    }
}
