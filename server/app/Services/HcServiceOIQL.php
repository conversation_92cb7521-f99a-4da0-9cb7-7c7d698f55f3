<?php

namespace App\Services;

use App\Abstracts\AbstractHcService;
use App\Exceptions\HcException;
use App\Models\HC\HcGCCRDTCODE;
use Log;
use Throwable;

class HcServiceOIQL extends AbstractHcService
{
    public function save($loan)
    {
        return $this->startTransaction(function () use ($loan) {
            try {
                parent::save($loan);
            } catch (Throwable $e) {
                Log::info('Store to HC error', ['err' => $e->getTraceAsString()]);
                throw new HcException($e->getMessage());
            }
        });
    }

    protected function getOuterCodeType()
    {
        return constants('HC.LOAN_OUTER_CODE_TYPE_OIQL');
    }

    public function hasCreditByLoan($loan)
    {
        $ssn = $loan->citizen->getSocCard()->passport_number;

        $has_credit = $this->hasCredit($ssn);
        if (!$has_credit) {
            return $this->hasNonSyncedCredits($ssn);
        }

        return $has_credit;
    }

    // For OIQL ignore all current loans
    protected function clientHasCredit($hc_gc_client, $config)
    {
        if (!$hc_gc_client) {
            return false;
        }

        return HcGCCRDTCODE::hasIqosCredit($hc_gc_client->fCODE);
    }
}
