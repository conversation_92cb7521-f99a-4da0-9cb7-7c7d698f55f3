<?php

namespace App\Services;

use App\Abstracts\AbstractEkengService;

class DevEkengService extends AbstractEkengService
{
    public function getCitizen($document_type, $document_number)
    {
        if (FakerService::isFakeSpouseSSN($document_number)) {
            $document_number = FakerService::replaceSSNToPassport($document_number);
        }

        if (FakerService::isFakePassport($document_number)) {
            $citizen = FakerService::getMockedCitizen($document_number);

            return $citizen['ekeng'];
        }

        if (FakerService::isFakeSSN($document_number)) {
            $citizen = FakerService::getMockedCitizenBySSN($document_number);

            return $citizen['ekeng'];
        }

        return parent::getCitizen($document_type, $document_number);
    }
}
