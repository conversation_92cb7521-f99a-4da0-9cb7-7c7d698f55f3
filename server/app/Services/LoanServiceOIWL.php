<?php

namespace App\Services;

use App\Abstracts\AbstractLoanService;
use App\Factory\HcServiceFactory;
use App\Strategies\Confirmation\ConfirmationStrategyContext;
use App\Traits\Transaction;

class LoanServiceOIWL extends AbstractLoanService
{
    use Transaction;

    public function __construct()
    {
        parent::__construct(constants('LOAN_TYPES.OIWL'));
    }

    /**
     * Override prepareCreditData to set interest_rate from configs for OIWL loans.
     *
     * @param mixed $credit_offer Credit offer object
     *
     * @return array Credit data with interest_rate from configs
     */
    protected function prepareCreditData($credit_offer): array
    {
        $citizen_credit = parent::prepareCreditData($credit_offer);

        // For OWL loans, the interest rate comes from configs, not from the credit offer
        $loan_config_service = resolve('App\Services\LoanConfigService');
        $configs = $loan_config_service->getConfigs($this->type);
        $citizen_credit['credit']['interest_rate'] = $configs['interest_rate'];

        return $citizen_credit;
    }

    public function processLoanConfirmation($loan)
    {
        $this->validateConfirmation($loan);

        $this->expireLoan($loan);

        $context = new ConfirmationStrategyContext($loan);

        $context->finalize();
    }

    public function validateConfirmation($loan)
    {
        parent::validateConfirmation($loan);

        $this->checkLoanAvailableAmount($loan);
    }

    public function saveLoanToHC($loan)
    {
        parent::saveLoanToHC($loan);
        if ($loan->withdrawal_fee) {
            $hc_service = HcServiceFactory::build($loan->loan_type_id);

            $hc_service->storeLoanDisbursementFee($loan);
        }
    }

    protected function getWithdrawalFee($amount, $withdrawal_fee_rate)
    {
        $configs = $this->getLoanConfigs();

        return ['withdrawal_fee' => $configs["withdrawal_fee_$amount"]];
    }

    public function shouldUpdateContractNumber($loan): bool
    {
        return true;
    }
}
