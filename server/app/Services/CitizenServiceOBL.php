<?php

namespace App\Services;

use App\Abstracts\BaseCitizenService;
use App\Helpers\ArrayHelper;
use App\Helpers\ObjectMerger;
use App\Interfaces\ICitizenService;
use App\Schemas\BusinessInfoSchema;
use Log;

class CitizenServiceOBL extends BaseCitizenService implements ICitizenService
{
    public function __construct()
    {
        parent::__construct();
    }

    protected function calculateCredit($citizen, $calculate_allowance = null)
    {
        $credit = [];
        $security_service = resolve('App\Interfaces\ISecurityService');
        $loan_security = $security_service->resolveLoanSecurity();

        $credit_offer_service = resolve('App\Interfaces\ICreditOfferService');
        $calculated_credit = $credit_offer_service->getCreditOffer($citizen, $loan_security->loan_type_id)['result']['credit'];

        if ($this->isCreditAvailable($calculated_credit)) {
            $credit = ArrayHelper::pick($calculated_credit, ['amount', 'duration']);
        }

        return $credit;
    }

    public function fetchCitizen($payload, $calculate_allowance)
    {
        $citizen = parent::getCitizen($payload, $calculate_allowance);
        $citizen['credit'] = ['obl' => $citizen['credit']];

        return $citizen;
    }

    protected function aggregateCitizenData($document_number)
    {
        $warehouse_service = resolve('App\Interfaces\IWarehouseService');

        $citizen = parent::aggregateCitizenData($document_number);
        $citizen_credits = $warehouse_service->getLoansBySsn($citizen['ekeng']['SSN'], true);

        if (count($citizen_credits) > 0) {
            $citizen['acra'] = $this->aggregator_service->getAcraBusinessMonitoringData($citizen['ekeng'], constants('ACRA_REPORT_TYPE.CREDIT_WITH_BUSINESS'));
        } else {
            $citizen['acra'] = $this->aggregator_service->getAcraData($citizen['ekeng'], constants('ACRA_REPORT_TYPE.CREDIT_WITH_BUSINESS'));
        }

        $citizen['loan_type_id'] = constants('LOAN_TYPES.OBL');

        return $citizen;
    }

    public function prepareBusinessInfo($citizen)
    {
        Log::debug('Preparing business info');
        $schema = new BusinessInfoSchema();

        $business_info_schema = $schema->get();

        Log::debug('Merge data by business info schema');
        $objectMerger = new ObjectMerger($business_info_schema);
        $prepared = $objectMerger->merge($citizen);
        $prepared = $this->setYerevanRegion($prepared);
        Log::debug('Merged data', ['business_context' => $prepared]);

        return $prepared;
    }

    public function prepareCitizen($citizen, $payload, $calculate_allowance)
    {
        $loan_config_service = resolve('App\Services\LoanConfigService');
        $configs = $loan_config_service->getConfigsByType(constants('LOAN_TYPES.OBL'));

        $citizen_info = parent::prepareCitizen($citizen, $payload, $calculate_allowance);
        $business_info = $this->prepareBusinessInfo($citizen);

        if (!empty($citizen_info['credit'])) {
            $citizen_info['credit']['interest_rate'] = $configs['interest_rate'];
            $apr = $this->calculateApr($citizen_info['credit']['amount']);
        }

        return array_merge($citizen_info, ['business' => $business_info, 'apr' => $apr ?? null]);
    }

    private function calculateApr($loan_amount)
    {
        return 27.32;
    }
}
