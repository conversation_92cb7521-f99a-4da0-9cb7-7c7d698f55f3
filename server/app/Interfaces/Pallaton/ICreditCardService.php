<?php

namespace App\Interfaces\Pallaton;

interface ICreditCardService
{
    public function registerPayment($payload);

    public function getUrls($payload);

    public function paymentOrderBinding($payload);

    public function processCreditCardSave($order_details, $user_id);

    public function getCreditCards();

    public function setPrimary($credit_card_id);

    public function delete($credit_card_id);
}
