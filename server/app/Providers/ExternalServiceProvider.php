<?php

namespace App\Providers;

use App;
use App\Factory\ArcaServiceFactory;
use App\Factory\HcServiceFactory;
use App\Models\SettingsStatus;

class ExternalServiceProvider extends ServiceResolveProvider
{
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('App\Interfaces\IHcService', function ($app) {
            $loan_type_id = $this->getLoanType($app->request);

            return HcServiceFactory::build($loan_type_id);
        });

        $this->app->bind('App\Interfaces\IArcaService', function ($app) {
            $loan_type_id = $this->getLoanType($app->request);

            $loan_config_service = resolve('App\Services\LoanConfigService');
            $configs = $loan_config_service->getConfigs($loan_type_id);
            $bank = $configs['primary_bank'];

            return ArcaServiceFactory::build($bank);
        });

        $this->app->bind('App\Interfaces\IStreamRecorderService', function () {
            $settings_service = resolve('App\Interfaces\ISettingsService');
            if (is_dev() || $settings_service->isGeneralSettingTypeDisabled(SettingsStatus::FACE_RECOGNITION)) {
                return new \App\Services\DevStreamRecorderService();
            }

            return new \App\Services\StreamRecorderService();
        });

        $this->app->bind('App\Interfaces\IHcBankService', 'App\Services\HcBankService');
        $this->app->bind('App\Interfaces\INorkService', is_dev() ? 'App\Services\DevNorkService' : 'App\Services\NorkService');
        $this->app->bind('App\Interfaces\IVehicleService', 'App\Services\VehicleService');
        $this->app->bind('App\Interfaces\IPoliceService', is_dev() ? 'App\Services\DevPoliceService' : 'App\Services\PoliceService');
        $this->app->bind('App\Interfaces\IEkengService', is_dev() ? 'App\Services\DevEkengService' : 'App\Services\EkengService');
        $this->app->bind('App\Interfaces\IIqosFtpService', is_dev() ? 'App\Services\DevIqosFtpService' : 'App\Services\IqosFtpService');
        $this->app->bind('App\Interfaces\IAWSService', is_dev() ? 'App\Services\DevAWSService' : 'App\Services\AWSService');
        $this->app->bind('App\Interfaces\IAggregatorService', 'App\Services\AggregatorService');
        $this->app->bind('App\Interfaces\IDataRobotService', is_dev() ? 'App\Services\DevDataRobotService' : 'App\Services\DWDataRobotService');
        $this->app->bind('App\Interfaces\IArmedService', is_dev() ? 'App\Services\CreditLine\DevArmedService' : 'App\Services\CreditLine\ArmedService');
        if (is_dev()) {
            $this->app->singleton('App\Interfaces\IWarehouseService', function ($app) {
                return new App\Services\DevWarehouseService();
            });
        } else {
            $this->app->bind('App\Interfaces\IWarehouseService', 'App\Services\WarehouseService');
        }
    }
}
