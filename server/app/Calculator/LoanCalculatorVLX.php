<?php

namespace App\Calculator;

use function Functional\map;
use function Functional\reduce_left;

class LoanCalculatorVLX extends LoanCalculator
{
    public function __construct($amount, $annual_rate, $service_fee_rate, $params)
    {
        $this->duration = $params['duration'];

        parent::__construct($amount, $annual_rate, $service_fee_rate, $params);
    }

    public function generateSchedule($monthly_payment = null, $start_date = null)
    {
        ['months' => $months, 'monthly_payment' => $monthly] = $this->table[$this->duration - 1];

        $r = range(1, $months);
        $today = $this->today($start_date);

        $sum = 0;

        $rows = map($r, function ($r, $i) use (&$sum, $monthly, $today) {
            [$next_payment_date, $days] = $this->getNextPaymentDate($today, $i);

            $fee = $this->percentage($this->amount - $sum, $this->annual_rate / self::DAYS_IN_YEAR * $days);
            $service_fee = $this->decimalPoints($fee);

            $columns = [
                'service_fee' => $service_fee,
                'st1' => $monthly,
                'st2' => $monthly - $service_fee,
                'date' => $next_payment_date,
                'days' => $days,
            ];
            $sum += $columns['st2'];

            return $columns;
        });

        $rows = map($rows, function ($r, $i) use ($rows) {
            $r['st4'] = reduce_left(array_slice($rows, $i), function ($rec, $index, $collection, $sum) {
                return $sum + $rec['st2'];
            }, 0);

            return $r;
        });

        $sum = 0;

        // Count base amount, balance and payment for that month
        foreach ($rows as $i => &$r) {
            // Last item is handled separately
            if ($i === count($rows) - 1) {
                $r['base'] = $this->decimalPoints($this->amount - $sum);
            } else {
                $r['base'] = $this->decimalPoints($r['st4'] - $rows[$i + 1]['st4']);
                $sum = $this->decimalPoints($sum + $r['base']);
            }

            $last_balance = $i == 0 ? $this->amount : $rows[$i - 1]['balance'];

            $r['balance'] = $last_balance - $r['base'] < 0.5
                        ? 0
                        : $this->decimalPoints($last_balance - $r['base']);

            $r['payment'] = $this->decimalPoints($r['service_fee'] + $r['base']);

            [, $days] = $this->getNextPaymentDate($today, $i);

            $service_fee_interest = $this->percentage($last_balance, $this->service_fee_rate) / self::DAYS_IN_YEAR * $days;
            $r['service_fee_interest'] = $this->decimalPoints($service_fee_interest);

            $r['service_fee_plain'] = $this->decimalPoints($r['service_fee'] - $r['service_fee_interest']);

            $previous = $i === 0 ? $this->amount : $rows[$i - 1]['st10'];
            $r['st10'] = $previous - $r['base'];
        }

        return $rows;
    }
}
