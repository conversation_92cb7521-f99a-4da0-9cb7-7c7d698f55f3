<?php

namespace App\Calculator;

use App\Helpers\ArrayHelper;
use function Functional\map;
use function Functional\reduce_left;

class LoanCalculator extends AbstractLoanCalculator
{
    public function __construct($amount, $annual_rate, $service_fee_rate, $params)
    {
        $max_duration = $params['max_duration'];

        parent::__construct($amount, $annual_rate, $service_fee_rate, $max_duration);

        $this->table = $this->generate();
    }

    protected function calculateRealRate($schedule)
    {
        $service_fee_sum = reduce_left($schedule, function ($v, $index, $collection, $sum) {
            return $sum + $v['service_fee'];
        }, 0);

        $st10_average = reduce_left($schedule, function ($m, $index, $collection, $sum) {
            return $sum + $m['st10'];
        }, $this->amount) / count($schedule);

        return $this->decimalPoints($service_fee_sum / $st10_average / count($schedule) * 12 * 100);
    }

    protected function monthsByMonthlyPayment($monthly_payment)
    {
        $first = $this->table[0];

        // Choose the amount of months, so that the amount of its monthly
        // payment has minimum difference from selected monthly payment
        return reduce_left($this->table, function ($row, $index, $collection, $min) use ($monthly_payment) {
            $diff = $monthly_payment - $row['monthly_payment'];
            [$cashedDiff] = $min;

            return $diff > 0 && $diff < $cashedDiff ? [$diff, $row] : $min;
        }, [INF, $first]);
    }

    public function generate()
    {
        $r = range(1, $this->max_duration);

        $full_table = map($r, function ($m) {
            [$months, $monthly_payment, $total] = $this->calculate($m);
            $rounded_monthly_payment = $this->roundUp($monthly_payment);

            return [
                'months' => $months,
                'monthly_payment' => $monthly_payment,
                'total' => $total,
                'rounded_monthly_payment' => $rounded_monthly_payment,
            ];
        });

        return $full_table;
    }

    /**
     * @param $min_months
     * Trying to calculate possible minimum and maximum payments for provided amount
     *
     * @return array
     */
    public function monthlyMinMax($min_months = 2)
    {
        // According to the algorithm the minimum monthly payment must be
        // at least the amount in 117th month (AG123 in excel). This happens
        // for vehicle loan only.
        // If the schedule is less than 117, default to last month payment
        $min_amount_idx = 117;
        $row = $this->table[$min_amount_idx] ?? $this->table[count($this->table) - 1];
        $min = $row['rounded_monthly_payment'];

        // Monthly payment for OCL($min_months = 18)
        // Monthly payment for OVL($min_months = 12)
        $max = $this->table[$min_months - 1]['rounded_monthly_payment'];

        return [$min, $max];
    }

    public function generateSchedule($monthly_payment, $start_date = null)
    {
        [$diff, $m] = $this->monthsByMonthlyPayment($monthly_payment);
        ['months' => $months, 'monthly_payment' => $monthly] = $m;
        $r = range(1, $months);
        $today = $this->today($start_date);

        $sum = 0;

        $rows = map($r, function ($r, $i) use (&$sum, $monthly, $today, $monthly_payment) {
            [$next_payment_date, $days] = $this->getNextPaymentDate($today, $i);

            $fee = $this->percentage($this->amount - $sum, $this->annual_rate / self::DAYS_IN_YEAR * $days);
            $service_fee = $this->decimalPoints(min($fee, $monthly_payment));

            $columns = [
                'service_fee' => $service_fee,
                'st1' => $monthly,
                'st2' => $monthly - $service_fee,
                'date' => $next_payment_date,
                'days' => $days,
            ];
            $sum += $columns['st2'];

            return $columns;
        });

        $rows = map($rows, function ($r, $i) use ($rows, $months, $diff) {
            $r['st4'] = reduce_left(array_slice($rows, $i), function ($rec, $index, $collection, $sum) {
                return $sum + $rec['st2'];
            }, 0) - $diff * $months;

            return $r;
        });

        $sum = 0;

        // Count base amount, balance and payment for that month
        foreach ($rows as $i => &$r) {
            // Last item is handled separately
            if ($i === count($rows) - 1) {
                $r['base'] = $this->decimalPoints($this->amount - $sum);
            } else {
                $r['base'] = $this->decimalPoints($r['st4'] - $rows[$i + 1]['st4'] + $diff);
                $sum = $this->decimalPoints($sum + $r['base']);
            }

            $last_balance = $i == 0 ? $this->amount : $rows[$i - 1]['balance'];

            $r['balance'] = $last_balance - $r['base'] < 0.5
                        ? 0
                        : $this->decimalPoints($last_balance - $r['base']);

            $r['payment'] = $this->decimalPoints($r['service_fee'] + $r['base']);

            [, $days] = $this->getNextPaymentDate($today, $i);

            $service_fee_interest = $this->percentage($last_balance, $this->service_fee_rate) / self::DAYS_IN_YEAR * $days;
            $r['service_fee_interest'] = $this->decimalPoints($service_fee_interest);

            $r['service_fee_plain'] = $this->decimalPoints($r['service_fee'] - $r['service_fee_interest']);

            $previous = $i === 0 ? $this->amount : $rows[$i - 1]['st10'];
            $r['st10'] = $previous - $r['base'];
        }

        // The algorithm has a bug, which in some cases
        // makes the balance of last month to be negative.
        // This adjusts the last entries to be correct.
        // NOTE: These are not specified in algorithm definition (EXCEL)
        $last_entry_idx = ArrayHelper::findIndex($rows, function ($r) {
            return $r['balance'] === 0;
        });

        // If last items balance is not 0, make the one with 0 balance
        // to be the last one and remove all next entries
        if ($last_entry_idx > 0 && $last_entry_idx < count($rows) - 1) {
            $r = &$rows[$last_entry_idx];
            // Set the base (principal) of last item to the last non-zero balance
            $r['base'] = $rows[$last_entry_idx - 1]['balance'];
            // Recalculate payment with new base (principal)
            $r['payment'] = $this->decimalPoints($r['service_fee'] + $r['base']);
            // Remove the rest
            array_splice($rows, $last_entry_idx + 1);
        }

        return $rows;
    }

    public function calculateAPR($monthly_payment, $start_date, $withdrawal_fee = 0, $months = null, $schedule = null)
    {
        // If months are 1 we get from Front not rounded monthly_payment and should replace with rounded one.
        if (isset($months) && $months === 1) {
            $monthly_payment = $this->table[$months - 1]['rounded_monthly_payment'];
        }

        return parent::calculateAPR($monthly_payment, $start_date, $withdrawal_fee, $months, $schedule);
    }
}
