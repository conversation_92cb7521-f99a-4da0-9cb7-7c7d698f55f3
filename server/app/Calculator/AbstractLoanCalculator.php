<?php

namespace App\Calculator;

use Carbon\Carbon;
use function Functional\reduce_left;
use Nyholm\EffectiveInterest\Calculator;

abstract class AbstractLoanCalculator
{
    const DAYS_IN_YEAR = 365;
    const NEWTON_RAPHSON_GUESS = 0.045;

    public function __construct($amount, $annual_rate, $service_fee_rate, $max_duration)
    {
        $this->amount = $amount;
        $this->annual_rate = $annual_rate;
        $this->max_duration = (int) $max_duration;
        $this->service_fee_rate = $service_fee_rate;
    }

    protected function percentage($amount, $percent)
    {
        return ($amount * $percent) / 100;
    }

    protected function decimalPoints($number, $decimals = null)
    {
        $decimals = $decimals ?? constants('DECIMALS');
        $rounded = sprintf('%0.'.$decimals.'f', $number);

        return +$rounded;
    }

    // Rounds up to nearest thousand
    protected function roundUp($amount)
    {
        return ceil($amount / 1000) * 1000;
    }

    public function calculate($months)
    {
        $monthly_payment = $this->pmt(
            $this->annual_rate / 100 / 12,
            $months,
            -$this->amount,
            0
        );

        $total = $months * $monthly_payment;

        return [$months, $monthly_payment, $total];
    }

    // Implementation of Excel PMT function
    protected function pmt($rate, $nperiod, $pv, $fv = 0, $type = 0)
    {
        if ($rate == 0) {
            return -($pv + $fv) / $nperiod;
        }

        $pvif = pow(1 + $rate, $nperiod);
        $pmt = ($rate / ($pvif - 1)) * -($pv * $pvif + $fv);

        if ($type == 1) {
            $pmt /= 1 + $rate;
        }

        return $pmt;
    }

    protected function getNextPaymentDate($today, $i)
    {
        $current_date = $today->copy()->addMonthsNoOverflow($i);
        $next_payment_date = $today->copy()->addMonthsNoOverflow($i + 1);

        return [$next_payment_date, $next_payment_date->diffInDays($current_date)];
    }

    public function scheduleSummary($monthly_payment, $start_date = null)
    {
        $schedule = $this->generateSchedule($monthly_payment, $start_date);

        return $this->summarizeSchedule($schedule);
    }

    public function summarizeSchedule($schedule)
    {
        return [
            'total' => $this->getTotal($schedule),
            'monthly_payment' => $schedule[0]['payment'],
            'second_month_payment' => $schedule[1]['payment'],
            'last_month_payment' => $schedule[count($schedule) - 1]['payment'],
            'months' => count($schedule),
            'next_payment_date' => $schedule[0]['date'],
            'real_interest_rate' => $this->calculateRealRate($schedule),
        ];
    }

    protected function getTotal($schedule)
    {
        return reduce_left($schedule, function ($m, $index, $collection, $sum) {
            return $sum + $m['payment'];
        }, 0);
    }

    public function calculateAPR($monthly_payment, $start_date, $withdrawal_fee = 0, $months = null, $schedule = null)
    {
        if (is_null($schedule)) {
            $schedule = $this->generateSchedule($monthly_payment, $start_date);
        }

        $payments = reduce_left($schedule, function ($value, $index, $collection, $reduction) {
            return array_merge($reduction, [$value['date']->format('Y-m-d') => $value['payment']]);
        }, []);

        $calculator = new Calculator();

        $apr = $calculator->withSpecifiedPayments(
            $this->amount - $withdrawal_fee,
            $this->today($start_date)->format('Y-m-d'),
            $payments,
            $this->getGuess()
        );

        return $this->decimalPoints($apr * 100);
    }

    public function today($d = null)
    {
        $today = $d ?? Carbon::now();
        $today = $today->copy()->setTimezone(constants('ARM_TIMEZONE'));

        return $today;
    }

    protected function getGuess(): float
    {
        return self::NEWTON_RAPHSON_GUESS;
    }

    abstract public function generateSchedule($monthly_payment, $start_date = null);

    abstract protected function calculateRealRate($schedule);
}
