<?php

namespace App\Jobs\Pallaton;

use App\Interfaces\ISmsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendSmsVerificationCode implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $code;
    private $phone_number;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($phone_number, $code)
    {
        $this->code = $code;
        $this->phone_number = $phone_number;

        $this->queue = config('queue.types.sms');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(ISmsService $smsService)
    {
        $smsService->send($this->phone_number, __('sms.forgot_password_verification_message', ['code' => $this->code]));
    }
}
