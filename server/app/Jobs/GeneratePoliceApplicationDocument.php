<?php

namespace App\Jobs;

use App\Factory\DocumentServiceFactory;
use App\Models\LoanDocument;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use PDF;

class GeneratePoliceApplicationDocument implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $loan;
    private $loan_document_service;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($loan)
    {
        $this->loan = $loan;
        $this->loan_document_service = DocumentServiceFactory::build($loan->loan_type_id);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $directory = $this->loan_document_service->extractDirectoryPath($this->loan);

        $type = LoanDocument::POLICE_APPLICATION;

        $this->loan->documents()
            ->where('document_type', $type['name'])
            ->delete();

        $this->generatePdf($this->loan, $type['name'], $directory, $type['public']);
    }

    protected function generatePdf($loan, $type, $directory, $public)
    {
        $path = $this->loan_document_service->getCitizenPath($loan, $type, $directory);

        $pdf = PDF::loadView(
            $this->loan_document_service->getPdfTemplatePath($type),
            $this->loan_document_service->composePdfData($loan)
        );
        $content = $pdf->output();

        $this->loan_document_service->persistDocument($loan, $path, $type, $content, $public);
    }
}
