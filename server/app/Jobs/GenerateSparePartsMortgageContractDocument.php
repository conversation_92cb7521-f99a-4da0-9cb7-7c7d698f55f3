<?php

namespace App\Jobs;

use App\Factory\DocumentServiceFactory;
use App\Models\LoanDocument;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use PDF;

class GenerateSparePartsMortgageContractDocument implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $loan;
    private $loan_document_service;
    private $loan_config_service;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($loan)
    {
        $this->loan = $loan;
        $this->loan_document_service = DocumentServiceFactory::build($loan->loan_type_id);
        $this->loan_config_service = resolve('App\Services\LoanConfigService');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // We have to generate lawyer documents if loan documents already exist.
        if (!$this->loan->documents->first()) {
            return;
        }

        $type = LoanDocument::SPARE_PARTS_MORTGAGE_CONTRACT_OVIL['name'];

        $this->loan->documents()
            ->where('document_type', $type)
            ->delete();

        $directory = $this->loan_document_service->extractDirectoryPath($this->loan);
        $this->generatePdf($type, $directory);
    }

    protected function generatePdf($type, $directory)
    {
        $citizen = $this->loan->citizen;
        $path = $this->loan_document_service->getDocumentPath($citizen->first_name, $citizen->last_name, $type, $directory, constants('EXTENSION.PDF'));

        $pdf = PDF::loadView(
            $this->loan_document_service->getPdfTemplatePath($type),
            $this->loan_document_service->composePdfData($this->loan)
        );
        $content = $pdf->output();

        $this->loan_document_service->persistDocument($this->loan, $path, $type, $content, LoanDocument::LAWYER['public']);
    }

    public function failed(Exception $exception)
    {
        Log::error('Generate Spare Parts Mortgage Contract Document failed', [
            'loan_id' => $this->loan->id,
            'message' => $exception->getMessage(),
        ]);
    }
}
