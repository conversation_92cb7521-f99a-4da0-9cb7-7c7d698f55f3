<?php

namespace App\Jobs;

use App\Factory\DocumentServiceFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use PDF;

class GenerateTransactionDocuments implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $transaction = null;
    private $loanDocumentService = null;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($transaction)
    {
        $this->transaction = $transaction;
        $this->loanDocumentService = DocumentServiceFactory::build($transaction->loan->loan_type_id);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Deleting documents, because user can go back and change loan details,
        // so we need to generate them again
        $this->removeDocuments();

        $directory = $this->loanDocumentService->prepareDirectory();

        $types = $this->loanDocumentService->getTransactionDocumentTypes();

        foreach ($types as $type) {
            $this->generatePdf($this->transaction, $type['name'], $directory, $type['public'], $type['path'] ?? null);
        }
    }

    private function removeDocuments()
    {
        $document_names = $this->loanDocumentService->getTransactionDocumentsForRemove();

        $this->transaction->documents()
            ->whereIn('document_type', $document_names)
            ->delete();
    }

    protected function generatePdf($transaction, $type, $directory, $public, $file_path)
    {
        $path = $this->loanDocumentService->getCitizenPath($transaction->loan, $type, $directory);

        if (!empty($file_path)) {
            $content = file_get_contents(resource_path($file_path));
        } else {
            $pdf = PDF::loadView(
                $this->loanDocumentService->getPdfTemplatePath($type),
                $this->loanDocumentService->composeTransactionPdfData($transaction)
            );

            $content = $pdf->output();
        }

        $this->loanDocumentService->persistTransactionDocument($transaction, $path, $type, $content, $public);
    }
}
