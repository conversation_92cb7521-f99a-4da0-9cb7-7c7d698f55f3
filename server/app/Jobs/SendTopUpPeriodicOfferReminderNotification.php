<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendTopUpPeriodicOfferReminderNotification implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $profile;
    private $credit_offer;

    public $tries = 4; // Number of times the job should be attempted
    public $retry_after = 1200; // Retry delay in seconds

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($profile, $credit_offer)
    {
        $this->profile = $profile;
        $this->credit_offer = $credit_offer;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $notification_service = resolve('App\Interfaces\Notifier\IPushNotificationService');

            $notification_service->sendTopUpPeriodicOffer($this->profile, $this->credit_offer, true);

            Log::info('Send top-up periodic offer reminder push notification', ['ssn' => $this->profile->ssn, 'credit_offer' => $this->credit_offer]);
        } catch (Exception $e) {
            Log::warning('SendTopUpPeriodicOfferReminderNotification job failed', ['attempts' => $this->attempts(), 'message' => $e->getMessage()]);

            $this->release($this->retry_after);
        }
    }

    public function failed(Exception $exception)
    {
        Log::warning('Remind about top-up periodic credit offer via Push, Exception', [
            'message' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
