<?php

namespace App\Jobs;

use App\Models\Loan;
use App\Models\Paymentable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class UpdateLoanPaymentStatus implements ShouldQueue
{
    use Queueable;
    const CHUNK_SIZE = 1000;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $current_date = now();

        $loan_config_service = resolve('App\Services\LoanConfigService');
        $top_up_expiration_days = $loan_config_service->getConfigs(constants('LOAN_TYPES.OCL'))['top_up_withdraw_exp_days'];

        $loan_types = [
            constants('LOAN_TYPES.OCL') => $loan_config_service->getConfigs(constants('LOAN_TYPES.OCL'))['withdraw_exp_days'],
            constants('LOAN_TYPES.OVL') => $loan_config_service->getConfigs(constants('LOAN_TYPES.OVL'))['withdraw_exp_days'],
            constants('LOAN_TYPES.OIWL') => $loan_config_service->getConfigs(constants('LOAN_TYPES.OIWL'))['withdraw_exp_days'],
        ];

        Loan::where('payment_status', Paymentable::PAYMENT_PROCESSING)
            ->whereIn('loan_type_id', array_keys($loan_types))
            ->where('status', '<>', Loan::PENDING)
            ->where(function ($query) use ($current_date, $loan_types, $top_up_expiration_days) {
                foreach ($loan_types as $loan_type_id => $expiration_days) {
                    $query->orWhere(function ($query) use ($current_date, $loan_type_id, $expiration_days, $top_up_expiration_days) {
                        $query->where(function ($q) use ($current_date, $loan_type_id, $top_up_expiration_days) {
                            $q->where('loan_type_id', $loan_type_id)
                                ->whereNotNull('top_up_amount')
                                ->where('sign_date', '<', $current_date->copy()->subDays($top_up_expiration_days));
                        })->orWhere(function ($q) use ($current_date, $loan_type_id, $expiration_days) {
                            $q->where('loan_type_id', $loan_type_id)
                                ->whereNull('top_up_amount')
                                ->where('sign_date', '<', $current_date->copy()->subDays($expiration_days));
                        });
                    });
                }
            })
            ->orderBy('id')
            ->chunk(self::CHUNK_SIZE, function ($loans) {
                foreach ($loans as $loan) {
                    Log::info('Updated loan id - ', [$loan->id]);
                    $this->updateStatus($loan, Paymentable::PAYMENT_EXPIRED);
                }
            });
    }

    private function updateStatus($loan, $status)
    {
        if ($loan->payment->withdrawn === null) {
            $loan->update([
                'payment_status' => $status,
            ]);

            $loan->payment
                ->whereNull('withdrawn')
                ->where('id', $loan->payment_id)
                ->update([
                    'payment_status' => $status,
                ]);
        }
    }
}
