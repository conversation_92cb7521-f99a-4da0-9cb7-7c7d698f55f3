<?php

namespace App\Abstracts;

use App\Dtos\InternalClient\ClientDataDto;
use App\Exceptions\TransferTypeNotFoundException;
use App\Helpers\PassportHelper;
use App\Interfaces\IHcService;
use App\Models\CardToCardPayment;
use App\Models\CashPayment;
use App\Models\CitizenPassport;
use App\Models\ContractNumberId;
use App\Models\EasypayWalletPayment;
use App\Models\HC\HcClient;
use App\Models\HC\HcGCCLIENTS;
use App\Models\HC\HcGCCRDTCODE;
use App\Models\HC\HcLoansCred;
use App\Models\HC\HcLoansCredDisbursements;
use App\Models\HC\HcLoansCredDisbursmentFees;
use App\Models\HC\HcLoansCredInterests;
use App\Models\HC\HcLoansCredLineBreaks;
use App\Models\HC\HcLoansCredRepSchedules;
use App\Models\HC\HcLoansCredSchedules;
use App\Models\HC\HcLoansCredServiceFeeSchedules;
use App\Models\HC\HcLoansCredUpdates;
use App\Models\HcClientDefault;
use App\Models\IdramWalletPayment;
use App\Models\InternalClient;
use App\Models\Loan;
use App\Models\LoanSecurity;
use App\Models\LoanTypeHcNote;
use App\Models\ManualTransfer;
use App\Models\Pallaton\Profile;
use App\Models\Paymentable;
use App\Models\ProductProvision;
use App\Models\VeloxPayment;
use App\Models\WalletLoanPayment;
use App\Models\WirePayment;
use App\Observers\HcLoansCredDisbursementsObserver;
use App\Traits\StoreLoanDisbursement;
use App\Traits\Transaction;
use App\Utils\HCPassportManager;
use Carbon\Carbon;
use Config;
use function Functional\filter;
use function Functional\reduce_left;
use Illuminate\Support\Facades\Log;

abstract class AbstractHcService implements IHcService
{
    use Transaction;
    use StoreLoanDisbursement;

    const SCHEDULE_TYPES = [
        'CREDIT_LINE' => 'CREDIT_LINE',
        'LOAN' => 'LOAN',
    ];

    abstract protected function getOuterCodeType();

    abstract public function hasCreditByLoan($loan);

    abstract protected function clientHasCredit($hc_gc_client, $config);

    public function save($loan)
    {
        $ssn = $loan->loan_security->ssn;
        $client_outer_id = $this->upsertClient($ssn, $loan);

        $loan_outer_id = $this->storeLoan($loan, $client_outer_id);

        $this->storeLoanDisbursement($loan_outer_id, $loan);

        $this->storeLoanSchedule($loan_outer_id, $loan);

        return [
            'client_outer_id' => $client_outer_id,
            'loan_outer_id' => $loan_outer_id,
        ];
    }

    public function saveTopUp($loan)
    {
        $loan_outer_id = $loan->contract_number;
        $ssn = $loan->loan_security->ssn;

        $this->upsertClient($ssn, $loan);

        $this->storeLoanDisbursement($loan_outer_id, $loan);

        $this->storeLoansCredRepSchedule($loan_outer_id, $this->resolveLastPayment($loan));

        $this->storeLoansCredServiceFeeSchedule($loan_outer_id, $loan);

        $this->storeLoansCredScheduleRowsUpdates($loan_outer_id, $loan->loan_schedule, self::SCHEDULE_TYPES['LOAN']);
    }

    protected function clientHasAnyOldCredit($hc_gc_client = null)
    {
        if (!$hc_gc_client) {
            return false;
        }

        return HcGCCRDTCODE::hasAnyOldCredit($hc_gc_client->fCODE);
    }

    protected function getClientContractNumbers($hc_gc_client = null)
    {
        if (!$hc_gc_client) {
            return [];
        }

        return HcGCCRDTCODE::getAllContractNumbers($hc_gc_client->fCODE);
    }

    public function hasCredit($soc_card, $config = null)
    {
        $hc_gc_client = $this->getHcGCClientBySocCard($soc_card);

        return $this->clientHasCredit($hc_gc_client, $config);
    }

    public function hasNonSyncedCredits($soc_card): bool
    {
        // We need to make sure there
        // are no credits that are not synced to HC yet.
        // We do this by checking if a citizen has a loan with a contract number
        //  that doesn't exist in HC.
        $hc_gc_client = $this->getHcGCClientBySocCard($soc_card);

        $citizen_contract_numbers = Loan::getContractNumbersBySocCard($soc_card);
        $client_contract_numbers = $this->getClientContractNumbers($hc_gc_client);

        return $citizen_contract_numbers->diff($client_contract_numbers)->count() > 0;
    }

    public function hasActiveOclOrWallet($soc_card): bool
    {
        $hc_gc_client = $this->getHcGCClientBySocCard($soc_card);

        if (!$hc_gc_client) {
            return false;
        }

        $has_active_ocl = HcGCCRDTCODE::hasActiveCreditOcl($hc_gc_client->fCODE);
        $has_active_wallet = HcGCCRDTCODE::hasActiveWalletCredit($hc_gc_client->fCODE);

        return $has_active_ocl || $has_active_wallet;
    }

    public function hasNonSyncedDisbursement($contract_number): bool
    {
        $warehouse_service = resolve('App\Interfaces\IWarehouseService');

        $pending_payment_amount = $warehouse_service->getPendingDisbursementAmount($contract_number);

        return $pending_payment_amount > 0;
    }

    public function generateContractNumber($loan_type_id, $date)
    {
        $code = $this->loanCodeByType($loan_type_id);

        return $this->getOuterCodeType().$date->format('y').'-'.$code;
    }

    public function generateMortgageOuterCode($loan_contract_number, $date)
    {
        // Use last 5 characters of loan contract number
        $num = substr($loan_contract_number, -5);
        // Use loan type letter
        $type = substr($loan_contract_number, 0, 1);

        return constants('HC.MORTGAGE_OUTER_CODE_PREFIX').$date->format('y').'-'.$type.$num;
    }

    public function getCreditCodeAndOuterId($ssn, $loan_type_id)
    {
        $outer_id = $this->getOrCreateClientId($ssn, $loan_type_id);

        $hc_gc_client = $this->getHcGCClientBySocCard($ssn);
        $has_credit = $this->clientHasAnyOldCredit($hc_gc_client);

        // Existing client with credit
        if ($has_credit) {
            [
                'credit_identifier' => $credit_identifier,
                'current_credit_number' => $current_credit_number
            ] = $this->extractLastCreditCode($hc_gc_client->fCODE, $outer_id);
        }
        // Client doesn't exist or doesn't have credit
        else {
            $credit_identifier = constants('HC.CREDIT_IDENTIFIERS')[0];
            $current_credit_number = -1;
        }

        return [
            'credit_code' => $this->composeCreditCode($outer_id, $credit_identifier, $current_credit_number, $ssn),
            'outer_id' => $outer_id,
        ];
    }

    protected function loanCodeByType($loan_type_id)
    {
        $loan_code = $this->startTransaction(function () use ($loan_type_id) {
            $loan_code = ContractNumberId::getContractNumberId($loan_type_id);

            $loan_code->increment('contract_number_id', 1);
            $loan_code->save();

            return $loan_code;
        });

        // Pad with 0s to have 6 digits
        return sprintf('%06d', $loan_code->contract_number_id);
    }

    protected function getHcGCClient($citizen)
    {
        $soc_card = $citizen->getSocCard();

        return $this->getHcGCClientBySocCard($soc_card->passport_number);
    }

    protected function getPreviousCitizenBySsn($ssn)
    {
        $previous_citizen = 1;

        return CitizenPassport::getCitizenLatestPassportsBySsn($ssn)[$previous_citizen]->citizen ?? null;
    }

    public function getHcGCClientBySocCard($soc_card)
    {
        return HcGCCLIENTS::getBySocCard($soc_card);
    }

    public function updateHCEmail($ssn, $email)
    {
        $hc_gc_client = $this->getHcGCClientBySocCard($ssn);

        if ($hc_gc_client) {
            $this->upsertClient($ssn, null, null, null, $email);
        }
    }

    public function upsertClient($ssn, $loan = null, $citizen = null, $phone_number = null, $email = null)
    {
        $client_data_dto = $this->buildClientDataDto($ssn, $loan, $citizen, $phone_number, $email);

        // Perform hash comparison to determine if updates are needed
        if ($this->isHashMatching($client_data_dto)) {
            return $client_data_dto->getValue('outer_id');
        }

        // Upsert the client data
        return $this->upsertClientData($client_data_dto);
    }

    /**
     * Prepares a ClientDataDto object by determining the source of client data
     * (loan, Ekeng based citizen, or direct input (phone_number, email)), enhancing it with extra data,
     * and generating a hashable data DTO for comparison.
     *
     * @return ClientDataDto prepared client data
     */
    protected function buildClientDataDto($ssn, $loan, $citizen, $new_phone_number, $new_email): ClientDataDto
    {
        Log::debug('Building internal client data', ['ssn' => $ssn]);
        // Extract data from the most relevant source
        if ($loan) {
            return $this->buildFromLoan($loan, $ssn);
        }

        if ($citizen) {
            return $this->buildFromCitizen($citizen, $ssn);
        }

        return $this->buildFromDirectInput($ssn, $new_phone_number, $new_email);
    }

    /**
     * Prepare client data from loan details.
     */
    private function buildFromLoan($loan, $ssn): ClientDataDto
    {
        $citizen = $loan->citizen;
        $loan_type_id = $loan->loan_type_id;
        $phone_number = $citizen->phone_number;
        $email = $citizen->email;

        return $this->finalizeClientDataBuild($ssn, $loan_type_id, $citizen, $loan, $phone_number, $email);
    }

    /**
     * Prepare client data from Ekeng based citizen details and extra sources.
     */
    private function buildFromCitizen($citizen, $ssn): ClientDataDto
    {
        $security_service = resolve('App\Interfaces\ISecurityService');
        // Retrieve extra data from LoanSecurity and Profile
        $loan_security = $security_service->tryResolveLoanSecurity();
        $loan_type_id = $loan_security->loan_type_id ?? null;

        return $this->finalizeClientDataBuild($ssn, $loan_type_id, $citizen, null, null, null);
    }

    /**
     * Prepare client data from direct inputs like phone or email updates.
     */
    private function buildFromDirectInput($ssn, $new_phone_number, $new_email): ClientDataDto
    {
        // The cases when provided only phone number and/or email for update
        $phone_number = $new_phone_number ?? null;
        $email = $new_email ?? null;

        return $this->finalizeClientDataBuild($ssn, null, null, null, $phone_number, $email);
    }

    /**
     * Finalize and create the ClientDataDto with hashable data.
     */
    private function finalizeClientDataBuild($ssn, $loan_type_id, $citizen, $loan, $phone_number, $email): ClientDataDto
    {
        $outer_id = $this->getOrCreateClientId($ssn, $loan_type_id);

        $internal_client_service = resolve('App\Interfaces\IInternalClientService');
        $hashable_data_dto = $internal_client_service->prepareHashableData($citizen, $phone_number, $email);

        return new ClientDataDto($ssn, $loan_type_id, $hashable_data_dto, $citizen, $outer_id, $loan);
    }

    protected function isHashMatching(ClientDataDto $client_data_dto): bool
    {
        $internal_client_service = resolve('App\Interfaces\IInternalClientService');

        return $internal_client_service->internalClientHashComparison($client_data_dto);
    }

    /**
     * Upsert client details into the HC system, updating existing clients or inserting new ones.
     *
     * @return string external ID (outer_id) of the client
     */
    protected function upsertClientData(ClientDataDto $client_data_dto): string
    {
        $client_data_array = $client_data_dto->toArray();

        $citizen = $client_data_array['citizen'];
        $outer_id = $client_data_array['outer_id'];
        $loan = $client_data_array['loan'];
        $ssn = $client_data_array['ssn'];
        $hashable_data = $client_data_array['hashable_data_dto']->toArray();
        $hc_gc_client = $this->getHcGCClientBySocCard($ssn);

        $client_type = InternalClient::resolveHcClientType($client_data_array['loan_type_id']);

        Log::debug('Preparing to upsert HC Client details', ['ssn' => $ssn, 'client_type' => $client_type]);

        $client = $this->resolveClientUpsertData($citizen, $hashable_data, $client_type, $hc_gc_client, $ssn);

        // Existing client
        if ($hc_gc_client) {
            return $this->updateExistingClient($hc_gc_client, $client, $outer_id);
        }
        // New client
        return $this->handleInsertNewClient($loan, $hc_gc_client, $client, $outer_id, $ssn, $client_type);
    }

    /**
     * Handle the creation of a new client.
     */
    private function handleInsertNewClient($loan, $hc_gc_client, $client, $outer_id, $ssn, $client_type): string
    {
        $redis_service = resolve('App\Interfaces\IRedisService');
        $redis_key = "$outer_id:outer_id";
        $redis_outer_id = $redis_service->get($redis_key);
        //if the client is not synced, but we have a record in client table (hc buffer)
        if (isset($redis_outer_id)) {
            //we need this case because when a client does not reach to sync, we don't need to update one more time
            return $this->updateExistingClient($hc_gc_client, $client, $outer_id);
        }

        $meta = $loan ? $this->composeMeta($loan) : [];
        $redis_service->set($redis_key, $outer_id, constants('TWO_DAYS_IN_MINUTES'));

        $this->insertNewClient($client, $outer_id, $meta);

        $internal_client_service = resolve('App\Interfaces\IInternalClientService');
        $internal_client_service->markAsHcInserted($ssn, $client_type);

        return $outer_id;
    }

    public function updateExistingClient($hc_gc_client, $client, $outer_id)
    {
        $ids = ['CLICOD' => $hc_gc_client->fCODE ?? null];

        // If existing client fKEY is different like internal client_id need to insert NEW_OUTERID
        if ($hc_gc_client && trim($hc_gc_client->fKEY) !== $outer_id) {
            $ids = array_merge($ids, ['NEW_OUTERID' => $outer_id]);
        } else {
            $ids = array_merge($ids, ['OUTERID' => $outer_id]);
        }

        HcClient::updateExisting($ids, $client);

        return $outer_id;
    }

    protected function insertNewClient($client, $outer_id, $meta)
    {
        HcClient::insert(array_merge($client, $meta, ['OUTERID' => $outer_id]));
    }

    protected function composeMeta($loan)
    {
        return [];
    }

    private function setClientOuterId($hc_gc_client, $ssn)
    {
        $loan_security = LoanSecurity::where('ssn', $ssn)->latest()->first();
        $loan_type_id = $loan_security->loan_type_id;

        $outer_id = $this->getOrCreateClientId($ssn, $loan_type_id);

        if ($hc_gc_client && trim($hc_gc_client->fKEY) !== $outer_id) {
            return ['NEW_OUTERID' => $outer_id];
        }

        return ['OUTERID' => $outer_id];
    }

    /**
     * Resolve client data based on citizen information or hashable data.
     */
    private function resolveClientUpsertData($citizen, $hashable_data, $client_type, $hc_gc_client, $ssn): array
    {
        // If $citizen exists,
        // its mean the update source has enough info to compose a whole client
        // (it may be Ekeng or loan confirmation)
        if ($citizen) {
            return $this->composeClient(
                (object) $citizen,
                HcClientDefault::getClientDefaults($client_type),
                $hc_gc_client
            );
        }

        // If $citizen does not exist,
        //its mean the update source has only phone number and/or email for client update
        return $this->composeClientPhoneNumberAndEmail($ssn, $hashable_data['phone_number'], $hashable_data['email']);
    }

    protected function composeClient($citizen, $defaults, $hc_gc_client = null)
    {
        $ssn = $citizen->ssn ?? $citizen->getSocCard()->passport_number;
        $hc_code = $this->resolveLocation($citizen->location_code, $citizen->region, $citizen->district);
        $region_codes = $this->resolveRegion($citizen->location_code, $citizen->region, $citizen->district);

        return array_merge(
            $defaults,
            $this->resolveSocCardType($ssn),
            $this->composeClientPassport($citizen, $hc_gc_client),
            [
                'REGNUM' => $ssn,
                'GENDER' => $citizen->gender,
                'DATEBIRTH' => Carbon::parse($citizen->birth_date),
                'NAME' => $citizen->last_name.' '.$citizen->first_name.' '.$citizen->middle_name,
                'ENAME' => $citizen->last_name_en.' '.$citizen->first_name_en.' '.$citizen->middle_name_en,
                'ADDRESS' => $citizen->street.$citizen->building,
                'TEL1' => isset($citizen->additional_phone_number) ? substr($citizen->additional_phone_number, 1) : null, // Trim the + sign
                'EMAIL' => $citizen->email ?? null,
                'CITY' => $citizen->city,
                'MOBCODE' => isset($citizen->phone_number) ? substr($citizen->phone_number, 1, 5) : null,
                'MOBILE' => isset($citizen->phone_number) ? substr($citizen->phone_number, 6) : null,
                'DISTRICT' => $region_codes['DISTRICT'],
                'COMMUNITY' => $hc_code,
                'APARTMENT' => $citizen->apartment,
            ]
        );
    }

    protected function composeClientPhoneNumberAndEmail($ssn, $phone_number, $email): array
    {
        return [
            'REGNUM' => $ssn,
            'EMAIL' => $email,
            'MOBCODE' => isset($phone_number) ? substr($phone_number, 1, 5) : null,
            'MOBILE' => isset($phone_number) ? substr($phone_number, 6) : null,
        ];
    }

    public function composeClientPassport($citizen, $hc_gc_client): array
    {
        $citizen_passports = PassportHelper::getOrderedPassports($citizen->passports, [constants('SOC_CARD')]);

        $hc_initial_passports = is_null($hc_gc_client) ? [] : [
            'PASSCODE' => HCPassportManager::composeHCPassportData($hc_gc_client->fPASCODE, $hc_gc_client->fPASTYPE),
            'PASSCODE2' => HCPassportManager::composeHCPassportData($hc_gc_client->fPASCODE2, $hc_gc_client->fPASTYPE2, HCPassportManager::PASSCODE2_SUFFIX),
        ];
        $hc_passport_manager = new HCPassportManager($citizen_passports, $hc_initial_passports);

        return $hc_passport_manager->composeHCPassports();
    }

    // A person might not have a social card, in that case
    // it's a social card reference, which is not numeric
    protected function resolveSocCardType($soc_card)
    {
        if (is_numeric($soc_card)) {
            return [];
        }

        return ['REGNTYPE' => constants('HC.SOC_CARD_REFERENCE')];
    }

    protected function resolveLocation($location_code, $region_name, $district_name)
    {
        if ($location_code == null) {
            //In case of invalid region default to ԵՐԵՎԱՆ: ԿԵՆՏՐՈՆ code
            return Config::get('hc_ekeng_community.ԿԵՆՏՐՈՆ.01.hc_code');
        }

        $region_code = $this->extractRegionCode($location_code);

        // in case of NKR, we compare region & district names
        // if there are different, we need to combine them to getting Ekeng community name form
        // ex: $region_name == 'ՔԱՇԱԹԱՂ', $district_name == 'ՇԱԼՈՒԱ'. Ekeng name 'ՇԱԼՈՒԱ(ՔԱՇԱԹԱՂ)'
        if ($region_code == constants('EKENG_REGION_NKR') && $region_name != $district_name) {
            $district_name = $district_name."($region_name)";
        }

        // for supporting names with `.` ($district name == 'Գ.ԱՐԱՐԱՏ'), we replace `.` to space
        $district = str_replace('.', ' ', $district_name);

        // if code dosen't exist in hc_ekeng_community we return null
        return Config::get("hc_ekeng_community.$district.$region_code.hc_code");
    }

    protected function resolveRegion($location_code, $region_name, $district_name)
    {
        if ($location_code == null) {
            //In case of invalid region default to ԵՐԵՎԱՆ: code
            return constants('HC.HC_REGIONS.01.ԵՐԵՎԱՆ');
        }

        $region_code_ekeng = $this->extractRegionCode($location_code);

        // In general we detect REGION and DISTRICT code by ekeng region code(01-12) and region name( Շիրակ, Հադրութ ․․․)
        // But we have some exceptions with NKG
        // for example ԲԵՐՁՈՐ: We get from ekeng as region name ԲԵՐՁՈՐ but it must be ՔԱՇԱԹԱՂ
        // to handle this case we call resolveRegionByCode method and trying detect REGION and DISTRICT code from ԲԵՐՁՈՐ hc_code
        $region = constants("HC.HC_REGIONS.$region_code_ekeng.$region_name");
        if ($region !== null) {
            return $region;
        } else {
            $hc_code = $this->resolveLocation($location_code, $region_name, $district_name);
            $region_code_hc = $this->extractRegionCode($hc_code);

            return $this->resolveRegionByCode($region_code_ekeng, $region_code_hc);
        }
    }

    public function resolveRegionByCode($region_code_ekeng, $region_code_hc)
    {
        $region_consts = constants("HC.HC_REGIONS.$region_code_ekeng");

        // trying find out REGION and DISTRICT code from HC.HC_REGIONS by ekeng and hc codes
        $region = filter($region_consts, function ($region_const) use ($region_code_hc) {
            $region_code = $this->extractRegionCode($region_const['REGION']);

            return $region_code == $region_code_hc;
        });

        if ($region !== null) {
            return array_shift($region);
        }

        // Finally if we couldn't detect REGION and DISTRICT code from hc_code too,
        // we compose this array for correct filling REGION and DISTRICT fields in hc-DB by null
        return [
            'REGION' => null,
            'DISTRICT' => null,
        ];
    }

    protected function extractRegionCode($code)
    {
        if ($code == null) {
            return null;
        }

        // cut Region code
        return substr($code, 0, 2);
    }

    protected function storeLoan($loan, $client_outer_id)
    {
        $hc_loan = $this->composeLoan($loan, $client_outer_id);

        $this->insertLoansCred($hc_loan);

        return $hc_loan['OUTER_CODE'];
    }

    protected function composeLoan($loan, $client_outer_id)
    {
        $outer_code = $loan->contract_number;
        $region_codes = $this->resolveRegion($loan->citizen->location_code, $loan->citizen->region, $loan->citizen->district);

        return array_merge(
            $this->setDefaultLoansCredValues($loan),
            $this->resolveDeliveryAddress($loan),
            $this->getPenaltyPercentage($loan),
            [
                'OUTER_CLIENT' => $client_outer_id,
                'CRDTCODE' => $loan->credit_code,
                'OUTER_CODE' => $outer_code,
                'CODE' => $outer_code,
                'SUMMA' => $loan->amount,
                'DATE' => $loan->sign_date->setTimezone(constants('ARM_TIMEZONE')),
                'DATEGIVE' => $loan->sign_date->setTimezone(constants('ARM_TIMEZONE')), // The date the payment is made
                'STMSDATE' => $loan->sign_date->setTimezone(constants('ARM_TIMEZONE')),
                'NOTE2' => $this->getNote2($loan),
                'DLVSTMVIEW' => $loan->notification_method,
                'LRDISTR' => $region_codes['DISTRICT'],
                'AGRMARBEG' => $loan->next_payment_date,
                'DATEAGR' => $this->resolveLastPayment($loan),
                'REGION' => $region_codes['REGION'],
                'UDRFAYCO' => $loan->fico_score,
                'UDRSALARY' => $loan->citizen->salary,
                'UDRDISBURS' => $this->resolveTransferType($loan),
                'PCAGR' => $loan->service_fee_rate,
                'PCLOSS' => $loan->service_fee_rate,
        ]);
    }

    protected function storeLoanUpdate($loan, $updatable_attrs = [])
    {
        $hc_loan = $this->composeLoanUpdate($loan, $updatable_attrs);

        HcLoansCredUpdates::create($hc_loan);
    }

    protected function composeLoanUpdate($loan, $updatable_attrs)
    {
        $outer_code = $loan->contract_number;

        return array_merge([
            'NEW_OUTER_CODE' => $outer_code,
            'CODE' => $outer_code,
        ], $updatable_attrs);
    }

    // For post, set registration address
    protected function resolveDeliveryAddress($loan)
    {
        if ($loan->notification_method == 'post') {
            return ['SENDSTMADRS' => constants('HC.DELIVERY_REGISTRATION_ADDRESS')];
        }

        return [];
    }

    protected function getPenaltyPercentage($loan)
    {
        $loanConfigService = resolve('App\Services\LoanConfigService');
        $configs = $loanConfigService->adjustConfigs($loan->loan_type->loan_configs);

        return [
            'PCPENAGR' => $configs['penalty_base_amount'],
            'PCPENPER' => $configs['penalty_percentage_amount'],
        ];
    }

    protected function storeLoanDisbursement($loan_outer_id, $loan)
    {
        // For cash payment do nothing
        if ($loan->payment instanceof CashPayment) {
            return;
        }

        $payment_date = $loan->payment->paid ? $loan->payment->paid->setTimezone(constants('ARM_TIMEZONE')) : null;

        if ($loan->payment instanceof ProductProvision) {
            if ($loan->loan_type_id === constants('LOAN_TYPES.OASL')) {
                $payload = [
                    'DATE' => Carbon::now()->setTimezone(constants('ARM_TIMEZONE')),
                    'ACCCORR' => env('ARPI_SOLAR_BANK_ACCOUNT'),
                ];
            } else {
                $payload = [
                    'DATE' => Carbon::now()->setTimezone(constants('ARM_TIMEZONE')),
                    'ACCCORR' => env('PRODUCT_PROVISION_BANK_ACCOUNT'),
                ];
            }
        } elseif ($loan->payment instanceof WalletLoanPayment) {
            $acccorr = $this->getWalletAcccorr($loan);

            $payload = [
                'DATE' => $payment_date,
                'ACCCORR' => $acccorr,
                'COMMENT' => '',
            ];
        } elseif ($loan->payment instanceof IdramWalletPayment) {
            $payload = [
                //if payment fail paid == null
                'DATE' => $payment_date,
                'ACCCORR' => env('IDRAM_BANK_ACCOUNT'),
                'COMMENT' => "Idram {$loan->payment->wallet_id}",
            ];
        } elseif ($loan->payment instanceof CardToCardPayment) {
            $bank_account = [
                constants('TRANSFER_BANKS.EVOCABANK') => env('EVOCA_CARD_BANK_ACCOUNT'),
                constants('TRANSFER_BANKS.ARMECONOMBANK') => env('ARMECONOM_CARD_BANK_ACCOUNT'),
            ];

            $payload = [
                //if payment fail paid == null
                'DATE' => $payment_date,
                'ACCCORR' => $bank_account[$loan->payment->bank],
                'COMMENT' => "Card {$loan->payment->card_number}",
            ];
        } elseif ($loan->payment instanceof EasypayWalletPayment) {
            $payload = [
                //if payment fail paid == null
                'DATE' => $payment_date,
                'ACCCORR' => env('EASYPAY_BANK_ACCOUNT'),
                'COMMENT' => "EasyPay {$loan->payment->wallet_id}",
            ];
        } elseif ($loan->payment instanceof WirePayment) {
            $payload = [
                // ArmSoft doesn't accept null. so we just set current time
                'DATE' => Carbon::now()->setTimezone(constants('ARM_TIMEZONE')),
                'ACCCORR' => env('WIRE_BANK_ACCOUNT'),
                'COMMENT' => "Wire {$loan->payment->bank_account}",
            ];
        } elseif ($loan->payment instanceof VeloxPayment) {
            $payload = [
                // ArmSoft doesn't accept null. so we just set current time
                'DATE' => $loan->payment->withdrawn->setTimezone(constants('ARM_TIMEZONE')),
                'ACCCORR' => env('VELOX_BANK_ACCOUNT'),
            ];
        } elseif ($loan->payment instanceof ManualTransfer) {
            $payload = [
                'DATE' => $payment_date,
                'ACCCORR' => $this->getMerchantBankAccount($loan),
                'COMMENT' => '',
            ];
        } else {
            throw new TransferTypeNotFoundException();
        }

        $this->createLoanDisbursement($loan_outer_id, $loan, $payload);
    }

    public function updateLoanDisbursement($loan)
    {
        // TODO: Different bank accounts for different cash payment providers
        if ($loan->payment instanceof CashPayment) {
            $payload = [
                //if payment fail paid == null
                'DATE' => $loan->payment->withdrawn ? $loan->payment->withdrawn->setTimezone(constants('ARM_TIMEZONE')) : null,
                'ACCCORR' => $this->getBankAccount($loan),
            ];

            $this->createLoanDisbursement($loan->contract_number, $loan, $payload);
        }
    }

    public function storeLoanDisbursementFee($loan)
    {
        Log::debug('Storing loanDisbursementFee into armsoft');

        // For cash payment do nothing
        if ($loan->payment instanceof CashPayment) {
            return;
        }

        $payload = [
            'ACCCORR' => $this->getBankWithdrawnAccount($loan),
        ];

        if ($loan->payment instanceof ManualTransfer) {
            $payload = [
                'ACCCORR' => $this->getMerchantBankAccount($loan),
            ];
        }

        $this->createLoanDisbursementFee($loan->contract_number, $loan, $payload);

        Log::debug('Stored loanDisbursementFee into armsoft successfully');
    }

    public function updateLoanDisbursementFee($loan)
    {
        if ($loan->payment instanceof CashPayment) {
            $payload = [
                'ACCCORR' => $this->getBankWithdrawnAccount($loan),
            ];

            $this->createLoanDisbursementFee($loan->contract_number, $loan, $payload);
        }
    }

    protected function getWalletAcccorr($loan)
    {
        if ($loan->isIdramWalletLoan()) {
            return env('IDRAM_LOAN_BANK_ACCOUNT');
        } elseif ($loan->isTelcellWalletLoan()) {
            return env('TELCELL_LOAN_BANK_ACCOUNT');
        } elseif ($loan->isEasypayWalletLoan()) {
            return env('EASYPAY_LOAN_BANK_ACCOUNT');
        } elseif ($loan->isUpayWalletLoan()) {
            return env('UPAY_LOAN_BANK_ACCOUNT');
        } elseif ($loan->isFastshiftWalletLoan()) {
            return env('FASTSHIFT_LOAN_BANK_ACCOUNT');
        }

        return null;
    }

    protected function getBankAccount($loan)
    {
        if ($loan->payment->type === CashPayment::GLOBAL_CREDIT) {
            return env('GC_CASH_BANK_ACCOUNT');
        }

        if ($loan->payment->type === CashPayment::EASYPAY) {
            return env('EASYPAY_BANK_ACCOUNT');
        }

        if ($loan->payment->type === CashPayment::UPAY) {
            return env('UPAY_CASH_BANK_ACCOUNT');
        }

        if ($loan->payment->type === CashPayment::TELCELL) {
            return env('TELCELL_BANK_CASH_ACCOUNT');
        }

        return null;
    }

    protected function getMerchantBankAccount($loan)
    {
        $loan_security = $loan->loan_security;

        $merchant = $loan_security->loan_application_order->merchant ?? null;

        return $merchant ? $merchant->account_number : null;
    }

    protected function getBankWithdrawnAccount($loan)
    {
        if ($loan->payment instanceof CardToCardPayment) {
            if ($loan->loan_type_id == constants('LOAN_TYPES.OIWL')) {
                $bank_account = [
                    constants('TRANSFER_BANKS.EVOCABANK') => env('EVOCA_CARD_BANK_WITHDRAWN_ACCOUNT'),
                    constants('TRANSFER_BANKS.ARMECONOMBANK') => env('ARMECONOM_CARD_BANK_WITHDRAWN_ACCOUNT'),
                ];

                return $bank_account[$loan->payment->bank];
            }
        }

        if ($loan->payment instanceof WalletLoanPayment) {
            return $this->getWalletAcccorr($loan);
        }

        if ($loan->payment->type === CashPayment::TELCELL) {
            if ($loan->loan_type_id == constants('LOAN_TYPES.OIWL')) {
                return env('TELCELL_BANK_WITHDRAWN_SECOND_ACCOUNT');
            }

            return env('TELCELL_BANK_WITHDRAWN_ACCOUNT');
        }

        return env('GC_BANK_WITHDRAWN_ACCOUNT');
    }

    protected function createLoanDisbursement($loan_outer_id, $loan, $payload)
    {
        $summa = $loan->isTopUp() ? $loan->top_up_amount : $loan->amount;

        $disbursement_date = $loan->payment->withdrawn ?? now();
        $loan_payment_disbursement = $this->storeLoanDisbursementRef(
            constants('LOAN_DISBURSEMENT_TYPES.PAYMENT'),
            $loan->payments()->latest()->first()->id,
            $loan->loan_security->id,
            $disbursement_date
        );

        HcLoansCredDisbursementsObserver::setDisbursementRef($loan_payment_disbursement);

        HcLoansCredDisbursements::create(array_merge($loan->loans_cred_disbursement_defaults, [
            'CODE' => $loan_outer_id,
            'OUTER_CODE' => $loan_outer_id,
            'SUMMA' => $summa,
        ], $payload));
    }

    protected function createLoanDisbursementFee($loan_outer_id, $loan, $payload)
    {
        HcLoansCredDisbursmentFees::create(array_merge($loan->loans_cred_disbursement_defaults, [
            'DATE' => $loan->payment->withdrawn ? $loan->payment->withdrawn->setTimezone(constants('ARM_TIMEZONE')) : null,
            'CODE' => $loan_outer_id,
            'OUTER_CODE' => $loan_outer_id,
            'SUMMA' => $loan->withdrawal_fee,
        ], $payload));
    }

    protected function storeLoanSchedule($loan_outer_id, $loan)
    {
        $payload = $this->composeLoanSchedule($loan_outer_id, $loan->loan_schedule);

        $chunks = array_chunk($payload, HcLoansCredSchedules::CHUNK_SIZE);

        foreach ($chunks as $chunk) {
            HcLoansCredSchedules::insert($chunk);
        }
    }

    protected function composeLoanSchedule($loan_outer_id, $loan_schedule = [])
    {
        return reduce_left($loan_schedule, function ($s, $i, $schedule, $acc) use ($loan_outer_id) {
            // Bulk insert doesn't use Eloquent features,
            // hence we need to format the date here
            $p = [
                'OUTER_CODE' => $loan_outer_id,
                'DATE' => $s->date->format('Y-m-d'),
                'PAYEDAMOUNT' => 0.0,
            ];

            array_push($acc, array_merge(
                [
                    'AMOUNT' => $s->base,
                    'TYPE' => 1,
                ], $p
            ), array_merge(
                [
                    'AMOUNT' => $s->service_fee_interest,
                    'TYPE' => 2,
                ], $p
            ), array_merge(
                [
                    'AMOUNT' => $s->service_fee_plain,
                    'TYPE' => 7,
                ], $p
            ));

            return $acc;
        }, []);
    }

    protected function storeLoansCredServiceFeeSchedule($loan_outer_id, $loan)
    {
        HcLoansCredServiceFeeSchedules::create([
            'OUTER_CODE' => $loan_outer_id,
            'CODE' => $loan_outer_id,
            'DATE' => now()->setTimezone(constants('ARM_TIMEZONE')),
            'DATEAGR' => $this->resolveLastPayment($loan),
            'COMMENT' => '',
        ]);
    }

    protected function storeLoansCredRepSchedule($loan_outer_id, $last_payment_date)
    {
        HcLoansCredRepSchedules::create([
            'OUTER_CODE' => $loan_outer_id,
            'CODE' => $loan_outer_id,
            'DATE' => now()->setTimezone(constants('ARM_TIMEZONE')),
            'DATEAGR' => $last_payment_date,
            'ISPROLONG' => 0,
            'COMMENT' => '',
        ]);
    }

    protected function storeLoansCredScheduleRowsUpdates($loan_outer_id, $schedule, $schedule_type)
    {
        $composed_schedule = $this->composeLoanScheduleUpdatesSchedule($loan_outer_id, $schedule, $schedule_type);

        Log::info('Composed LoansCredScheduleRows for Armsoft', ['schedule' => $composed_schedule]);

        $warehouse_service = resolve('App\Interfaces\IWarehouseService');
        $warehouse_service->storeLoansCredScheduleRowsUpdatesToHC(
            $composed_schedule,
            $loan_outer_id
        );
    }

    public function composeLoanScheduleUpdatesSchedule($loan_outer_id, $schedule, $schedule_type)
    {
        $filtered_schedule = $this->filterLoanScheduleUpdatesSchedule($schedule, $schedule_type);

        return reduce_left($filtered_schedule, function ($curr, $i, $schedule, $acc) use ($loan_outer_id, $schedule_type) {
            $static_values = [
                'OUTER_CODE' => $loan_outer_id,
                'CODE' => $loan_outer_id,
                // Bulk insert doesn't use Eloquent features, hence we need to format the date here
                'DATEAGR' => $curr->date->format(constants('SCHEDULE_DATE_FORMAT')),
            ];

            // Because of CREDIT_LINE schedule has different columns compare with loan_schedule,
            // that's why we need to check before usage
            array_push($acc, array_merge(
                [
                    'AMOUNT' => $this->getScheduleAmount($curr, $schedule_type),
                    'TYPE' => 1,
                ], $static_values
            ), array_merge(
                [
                    'AMOUNT' => $this->getInterestFeeAmount($curr, $schedule_type),
                    'TYPE' => 2,
                ], $static_values
            ), array_merge(
                [
                    'AMOUNT' => $this->getServiceFeeAmount($curr, $schedule_type),
                    'TYPE' => 3,
                ], $static_values
            ));

            return $acc;
        }, []);
    }

    protected function getScheduleAmount($schedule, $schedule_type)
    {
        if ($schedule_type == self::SCHEDULE_TYPES['CREDIT_LINE']) {
            return $schedule->total;
        }

        return $schedule->base;
    }

    protected function getInterestFeeAmount($schedule, $schedule_type)
    {
        if ($schedule_type == self::SCHEDULE_TYPES['CREDIT_LINE']) {
            return $schedule->service_fee;
        }

        return $schedule->service_fee_interest;
    }

    protected function getServiceFeeAmount($schedule, $schedule_type)
    {
        if ($schedule_type == self::SCHEDULE_TYPES['CREDIT_LINE']) {
            return 0;
        }

        return $schedule->service_fee_plain;
    }

    protected function filterLoanScheduleUpdatesSchedule($schedule, $schedule_type)
    {
        return $schedule;
    }

    protected function storeLoansCredInterest($loan_outer_id, $loan)
    {
        HcLoansCredInterests::create(array_merge($loan->loans_cred_interest_defaults, [
            'OUTER_CODE' => $loan_outer_id,
            'CODE' => $loan_outer_id,
            'DATE' => now()->setTimezone(constants('ARM_TIMEZONE')),
            'COMMENT' => '',
        ]));
    }

    protected function storeLoansCredLineBreak($loan_outer_id, $loan)
    {
        HcLoansCredLineBreaks::create(array_merge($loan->loans_cred_line_break_defaults, [
            'OUTER_CODE' => $loan_outer_id,
            'CODE' => $loan_outer_id,
            'DATE' => now()->setTimezone(constants('ARM_TIMEZONE')),
            'COMMENT' => '',
        ]));
    }

    /**
     * Credit code has the following format
     * {global credit code(5 digits)}{client outer code (6 digits)}{check digit(1 digit)}
     * {credit identifier(1 char)}{client credit number(3 digits)}.
     */
    protected function composeCreditCode($client_code, $credit_identifier, $current_credit_number, $ssn)
    {
        $credit_identifiers = constants('HC.CREDIT_IDENTIFIERS');
        $i = array_search($credit_identifier, $credit_identifiers);

        // Add 2 to get next credit number and pad with 0s to have 3 digits
        $current_credit_number = sprintf('%03d', $current_credit_number + 2);

        // If we exceed 3 digits, pick next credit identifier and start over from 001
        if ($current_credit_number > 999) {
            $credit_identifier = $credit_identifiers[$i + 1];
            $current_credit_number = '001';
        }

        $company_and_client_code = constants('HC.GLOBAL_CREDIT_CODE').$client_code;

        $check_digit = $this->generateCheckDigit($company_and_client_code);

        $credit_code = "$company_and_client_code$check_digit$credit_identifier$current_credit_number";

        $is_exist = $this->isCreditCodeExist($credit_code, $ssn);

        if (!$is_exist) {
            $is_stored = $this->storeCreditCodeToRedis($ssn, $credit_code);

            if ($is_stored) {
                return $credit_code;
            }
        }

        return $this->composeCreditCode($client_code, $credit_identifier, $current_credit_number, $ssn);
    }

    protected function storeCreditCodeToRedis($ssn, $credit_code)
    {
        $is_stored = false;

        $redis_service = resolve('App\Interfaces\IRedisService');
        Log::info('Locking credit code: ', ['credit_code' => $credit_code]);
        $lock = $redis_service->lock("credit_code_lock_$credit_code", constants('CREDIT_CODE_TIMEOUT'));

        $is_stored = $lock->get(function () use ($ssn, $credit_code, $redis_service) {
            Log::info('Storing credit code in redis: ', ['credit_code' => $credit_code]);
            $redis_service->set("$ssn:credit_code:$credit_code", $credit_code, constants('CREDIT_CODE_TIMEOUT'));
            Log::info('Credit code successfully stored in redis: ', ['credit_code' => $credit_code]);

            return true;
        });

        if (!$is_stored) {
            Log::warning('Store credit code to redis, ResourceLocked: ', ['credit_code' => $credit_code]);
        }

        return $is_stored;
    }

    protected function isCreditCodeExist($credit_code, $ssn)
    {
        $hc_credit_code_exists = HcGCCRDTCODE::where('fDGCRDTCODE', $credit_code)->exists();
        $db_credit_code_exists = false;

        if (!$hc_credit_code_exists) {
            $db_credit_code_exists = Loan::whereHas('loan_security', function ($query) use ($credit_code, $ssn) {
                $query->where('ssn', $ssn);
                $query->where('loans.credit_code', $credit_code);

                $query->where(function ($q) {
                    //This loan is withdrawable, so we need to examine this case as well.
                    $q->where('payment_status', '<>', Paymentable::PAYMENT_EXPIRED);
                    //Reviewing the loan that may be considered a potential loan.
                    $q->orWhere(function ($sub_q) {
                        $sub_q->whereIn('status', [Loan::PENDING, Loan::VERIFIED]);
                        $sub_q->where('suuid_exp', '>', now());
                    });
                    //Checking the OVL, which is unconfirmed, but the loan is in progress.
                    $q->orWhere(function ($sub_q) {
                        $sub_q->where('loan_type_id', constants('LOAN_TYPES.OVL'));
                        $sub_q->whereIn('status', [Loan::PROCESSING, Loan::PROCESSED, Loan::REVIEW, Loan::PLEDGED]);
                    });
                    //Checking to see if we have a confirmed status,
                    //but the loan is not inserted into Armsoft in any case.
                    $q->orWhere('status', Loan::CONFIRMED);
                });
            })->exists();

            // If the credit code doesn't exist in Loan table,
            //check in LoanSecurity table for Velox loan offer cases
            if (!$db_credit_code_exists) {
                $db_credit_code_exists = LoanSecurity::whereHas('velox_loan_offer_detail', function ($q) use ($credit_code, $ssn) {
                    $q->where('ssn', $ssn)
                        ->where('loan_type_id', constants('LOAN_TYPES.VLX'))
                        ->where('suuid_exp', '>', now())
                        ->where('credit_code', $credit_code);
                })->exists();
            }
        }

        $redis_service = resolve('App\Interfaces\IRedisService');
        $redis_credit_code_exists = $redis_service->exists("$ssn:credit_code:$credit_code");

        return $hc_credit_code_exists || $db_credit_code_exists || $redis_credit_code_exists;
    }

    protected function generateCheckDigit($digits)
    {
        $digits = str_split($digits);
        $sum = $this->digitSumWithWeights($digits);
        $reminder = $sum % 10;

        return $reminder > 0 ? 10 - $reminder : $reminder;
    }

    protected function verifyCheckDigit($credit_code)
    {
        $digits = substr($credit_code, 0, 12);
        $sum = $this->digitSumWithWeights($digits);

        return $sum % 10 == 0;
    }

    protected function digitSumWithWeights($digits)
    {
        return reduce_left($digits, function ($digit, $index, $collection, $reduction) {
            $split_number = str_split($digit * constants('HC.CHECK_DIGIT_WEIGHTS')[$index]);

            return $reduction + array_sum($split_number);
        }, 0);
    }

    /**
     * Find credit code with the highest credit number(last 3 digits).
     */
    protected function extractLastCreditCode($cli_cod, $outer_id)
    {
        $credit_codes = HcGCCRDTCODE::getCreditCodes($cli_cod, $outer_id);

        $crdt_code = $credit_codes->reduce(function ($max, $c) {
            if (!$max) {
                return $c;
            }

            $credit_number = substr($c->fDGCRDTCODE, -3);
            $max_credit_number = substr($max->fDGCRDTCODE, -3);

            return $credit_number > $max_credit_number ? $c : $max;
        });

        //if the crdt_code is not found in HC need to start L001 (because we have changed the outer_id, so credit code changed too.)
        if (empty($crdt_code)) {
            return [
                'credit_identifier' => constants('HC.CREDIT_IDENTIFIERS')[0],
                'current_credit_number' => -1,
            ];
        }

        return [
            'credit_identifier' => substr($crdt_code->fDGCRDTCODE, -4, 1),
            'current_credit_number' => substr($crdt_code->fDGCRDTCODE, -3),
        ];
    }

    protected function getNote2($loan)
    {
        $identifier = $this->resolveNote2($loan);

        return LoanTypeHcNote::getExactNote2($loan->loan_type_id, $identifier);
    }

    protected function resolveNote2($loan)
    {
        return $loan->interest_rate;
    }

    protected function resolveLastPayment($loan)
    {
        return $loan->lastPayment->date;
    }

    protected function resolveTransferType($loan)
    {
        $payment_service = resolve('App\Interfaces\IPaymentService');

        return $payment_service->getTransferType($loan);
    }

    /**
     * Function to get loan amount left from HC.
     *
     * @param $contract_number string
     *
     * @return float|int
     */
    public function getBalance($contract_number)
    {
        return HcGCCRDTCODE::getBalance($contract_number);
    }

    /**
     * Function to get loan credit register code from HC.
     *
     * @param $contract_number string
     *
     * @return string
     */
    public function getCreditRegisterCodeNew($contract_number)
    {
        return HcGCCRDTCODE::getCreditRegisterCodeNew($contract_number);
    }

    protected function insertLoansCred($hc_loan)
    {
        HcLoansCred::create($hc_loan);
    }

    protected function setDefaultLoansCredValues($loan)
    {
        return $loan->loans_cred_defaults;
    }

    public function getOrCreateClientId($ssn, $loan_type_id = null)
    {
        $internal_client_service = resolve('App\Interfaces\IInternalClientService');

        $client_type = InternalClient::resolveHcClientType($loan_type_id);

        return $internal_client_service->getOrCreateClient($ssn, $client_type)->client_id;
    }
}
