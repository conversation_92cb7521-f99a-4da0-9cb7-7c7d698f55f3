<?php

namespace App\Abstracts;

use App\Exceptions\BlacklistedException;
use App\Exceptions\CreditLine\ZeroBalanceException;
use App\Factory\HcServiceFactory;
use App\Interfaces\CreditLine\ICreditLineService;
use App\Models\Loan;
use App\Models\PreapprovedCreditOfferType;
use App\Services\SecurityUtilityService;
use Log;

abstract class AbstractCreditLineService extends BaseCreditLineService implements ICreditLineService
{
    const INITIAL_LIMIT = 0;

    abstract public function resolveCredit();

    public function checkIsCitizenBlocked($loan_security)
    {
        $phone_number = $loan_security->phone_number;
        $ssn = $loan_security->ssn;
        $loan_type_id = $loan_security->loan_type_id;

        $is_blocked = SecurityUtilityService::isCitizenBlocked($ssn, $phone_number, null, null, $loan_type_id);
        if ($is_blocked) {
            Log::warning('Credit Line checkIsCitizenBlocked, Citizen Blocked',
                ['ssn' => $ssn, 'phone_number' => $phone_number]
            );

            throw new BlacklistedException();
        }
    }

    public function processLoanLimitUpdate($loan_security)
    {
        $current_credit_limit_calculation =
            $loan_security->loan
                ->credit_limit_calculations()
                ->current()
                ->first();

        // Calculate credit limit if loan does not have none or current(active) credit limit calculation
        if (!$current_credit_limit_calculation) {
            Log::info('Credit limit calculation started during the transaction', ['contract_number' => $loan_security->loan->contract_number]);

            $credit_limit_calculation_service = resolve('App\Services\CreditLine\CreditLimitCalculationService');
            [
                'credit' => $calculated_credit,
                'credit_offer' => $credit_offer
            ] = $credit_limit_calculation_service->prepareCreditOffer($loan_security, PreapprovedCreditOfferType::TRANSACTION_CREDIT_LIMIT_CALCULATION);

            $credit_limit_calculation_service->storeCalculation($loan_security, $credit_offer);

            $this->updateCreditLine($loan_security->loan, $calculated_credit);

            return;
        }

        Log::info('Credit limit calculation skipped during transaction because of the existence', ['current_credit_limit_calculation' => $current_credit_limit_calculation]);
    }

    protected function updateCreditLine($loan, $calculated_credit)
    {
        $current_loan_amount = $loan->amount;
        $calculated_limit = $calculated_credit['amount'];

        if ($current_loan_amount != $calculated_limit) {
            $loan->update(['amount' => $calculated_limit]);

            Log::info('Credit limit updated during the transaction', ['contract_number' => $loan->contract_number, 'existing_limit' => $loan->amount, 'calculated_limit' => $calculated_limit]);
        }

        if ($calculated_limit == 0) {
            throw new ZeroBalanceException();
        }
    }

    public function getFirstTransactionBySsn($ssn, $loan_type_id)
    {
        $loan = Loan::getLoanBySsnAndLoanType($ssn, $loan_type_id);

        if (!$loan) {
            return null;
        }

        return $loan->transactions()->orderBy('paid', 'asc')->first();
    }

    public function getTransactionDetails($payment_id): array
    {
        $credit_service = resolve('App\Interfaces\CreditLine\ICreditLineService');
        $loan = $credit_service->resolveCredit();

        $loan_config_service = resolve('App\Services\LoanConfigService');
        $loan_configs = $loan_config_service->getConfigs(constants('LOAN_TYPES.BNPL'));

        $purchase_service = resolve('App\Interfaces\CreditLine\IPurchaseService');
        $transaction = $purchase_service->resolveTransaction($payment_id);

        $transaction_schedule = $transaction->schedule()->orderBy('date', 'asc')->get()->toArray();

        $document_service_bnpl = resolve('App\Services\CreditLine\DocumentServiceBNPL');
        $documents = $document_service_bnpl->getTransactionDocuments($transaction);

        $hc_service = HcServiceFactory::build(constants('LOAN_TYPES.BNPL'));
        $balance = $hc_service->getCreditBalance($loan);

        return [
            'limit' => $loan->amount,
            'balance' => $balance,
            'amount' => $transaction->amount,
            'duration' => $loan_configs['duration'],
            'monthly_payment' => $transaction_schedule[0]['total'],
            'last_monthly_payment' => end($transaction_schedule)['total'],
            'payment_day' => carbon_parse($transaction_schedule[0]['date'])->format(constants('CREDIT_LINE.SCHEDULE_DATE_FORMAT')),
            'documents' => $documents,
        ];
    }

    public function composeFailureResponse($purchase_session)
    {
        return [
            'balance' => 0,
            'purchase' => $purchase_session->amount,
            'payment_id_exp' => carbon_parse($purchase_session->payment_id_exp)->setTimezone(constants('ARM_TIMEZONE'))->toString(),
        ];
    }

    protected function handleInternalRules($ssn, $document_number = null): void
    {
        $redis_service = resolve('App\Interfaces\IRedisService');
        $citizen_service = resolve('App\Interfaces\CreditLine\ICitizenServiceBNPL');

        Log::info('Getting affected internal rule(s) from cache', ['ssn' => $ssn]);
        $cached_affected_internal_rules = $redis_service->get("$ssn:affected_internal_rules");
        if (is_null($cached_affected_internal_rules)) {
            Log::info('There are no affected internal rule(s) in cache', ['ssn' => $ssn]);

            $citizen_service->checkInternalCredits($ssn, constants('LOAN_TYPES.BNPL'), $document_number);
            $redis_service->set("$ssn:affected_internal_rules", [], constants('ONE_HOUR_IN_MINUTES'));

            Log::info('Affected internal rule(s) empty result stored in cache with key', ['ssn' => $ssn]);
        }

        // If the array is not empty, it means there is some suspension/rejection internal rule in the cache
        if (!empty($cached_affected_internal_rules)) {
            Log::warning('Throw cached affected internal rule(s)', ['ssn' => $ssn, 'cached' => $cached_affected_internal_rules]);

            throw new $cached_affected_internal_rules();
        }
    }
}
