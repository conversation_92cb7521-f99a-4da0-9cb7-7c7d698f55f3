<?php

namespace App\Mail;

use App\Factory\DocumentServiceFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TransactionDocumentsMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    private $loan;
    private $transaction;
    private $loan_document_service;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($loan)
    {
        $this->loan = $loan;
        $this->transaction = $loan->transactions()->orderBy('id', 'desc')->first();
        $this->loan_document_service = DocumentServiceFactory::build($loan->loan_type_id);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $contract_number = $this->loan->contract_number;
        $msg = $this->view('transactionDocumentsMail')
            ->subject(__('mail.subject_loan_docs').$contract_number);

        $aws_service = resolve('App\Interfaces\IAWSService');

        $documents = $this->loan_document_service->getTransactionDocuments($this->transaction);

        foreach ($documents as $document) {
            $name = __('mail.'.$document['document_type']).'.pdf';
            $msg->attachFromStorageDisk(
                $aws_service->getPdfStorageName(),
                $document['path'],
                $name,
                [
                    'mime' => 'application/pdf',
                ]);
        }

        return $msg;
    }
}
