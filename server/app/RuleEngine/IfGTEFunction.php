<?php

namespace App\RuleEngine;

use <PERSON><PERSON><PERSON><PERSON>tz\StringCalc\Exceptions\NumberOfArgumentsException;
use <PERSON><PERSON><PERSON><PERSON>tz\StringCalc\Symbols\AbstractFunction;

class IfGTEFunction extends AbstractFunction
{
    /**
     * {@inheritdoc}
     */
    protected $identifiers = ['ifgte'];

    /**
     * {@inheritdoc}
     */
    public function execute(array $arguments)
    {
        if (sizeof($arguments) != 4) {
            throw new NumberOfArgumentsException('Error: Expected three arguments, got '.sizeof($arguments));
        }

        $value = $arguments[0];
        $min = $arguments[1];

        if ($value >= $min) {
            return $arguments[2];
        }

        return $arguments[3];
    }
}
