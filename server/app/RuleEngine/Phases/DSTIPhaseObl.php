<?php

namespace App\RuleEngine\Phases;

use App\Calculator\LoanCalculator;
use Log;

class DSTIPhaseObl extends DSTIPhase
{
    public function __construct()
    {
        parent::__construct(RulePhases::OBL['DSTI']);
    }

    public function applyPhase($payload)
    {
        $result = $payload->getResult();
        $configs = $this->getConfigs();

        if ($result['credit']['amount'] == $configs['min_amount']) {
            return true;
        }

        Log::info('DSTI Phase OBL skipped', ['credit' => $result['credit']]);

        return false;
    }

    protected function getValidCredit($credit, $dsti_repayment, $income)
    {
        $configs = $this->getConfigs();
        $calculator = new LoanCalculator(
            $credit['amount'],
            $configs['interest_rate'],
            $configs['service_fee_rate'],
            ['max_duration' => $configs['duration']]
        );

        $monthly_payment = $calculator->calculate($configs['duration'])[1];

        Log::info('DSTI Phase obl', ['amount' => $credit['amount'], 'payment' => $monthly_payment]);

        if (!$this->validateDsti($dsti_repayment, $monthly_payment, $income)) {
            return [];
        }

        Log::info('DSTI Phase get valid credit', ['phase' => $this->phase, 'credit' => $credit]);

        return $credit;
    }

    protected function getConfigs()
    {
        $loanConfigService = resolve('App\Services\LoanConfigService');

        return $loanConfigService->getConfigsByType(constants('LOAN_TYPES.OBL'));
    }
}
