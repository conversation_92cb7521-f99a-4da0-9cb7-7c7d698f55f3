<?php

namespace App\RuleEngine\Phases;

use App\Calculator\LoanCalculatorREML;
use Illuminate\Support\Facades\Log;

class MortgageSolvencyPhaseReml extends AbstractPhase
{
    const MIN_AMOUNT_PERCENTAGE = 0.1;

    public function __construct($phase)
    {
        $this->phase = $phase;
    }

    public function __invoke(PhasePayload $payload)
    {
        Log::info('Mortgage Solvency Phase Reml', ['phase' => $this->phase]);

        $context = $payload->getContext();
        $rules = $payload->getRules();
        $result = $payload->getResult();

        $mortgage_solvency = 0;

        if ($this->allowCalculateSolvency($context)) {
            $real_estate_price = $context['realEstatePrice'];
            $duration = $context['REMLDuration'];
            $credit = $result['credit'];
            $rate = $credit['rate'];
            $service_fee_rate = $credit['service_fee_rate'];

            $solvency = $this->calculateSolvency($credit['amount'] * self::MIN_AMOUNT_PERCENTAGE, $context);

            Log::info('Allow calculate solvency', ['solvency' => $solvency, 'credit' => $result['credit']]);

            $calculator = new LoanCalculatorREML($real_estate_price * self::MIN_AMOUNT_PERCENTAGE, $rate, $service_fee_rate, ['max_duration' => $duration]);
            $current_loan_monthly_payment = $calculator->generateSchedule()[0]['payment'];

            $mortgage_solvency = $solvency - $current_loan_monthly_payment;
        }

        $prepared_context = $this->prepareContext($context, ['mortgageSolvency' => $mortgage_solvency]);

        $phase_result = $this->execute($rules[$this->phase], $prepared_context);

        $prepared_result = $this->prepareResult($phase_result);
        $payload->addResult($prepared_result);

        return $payload;
    }

    public function allowCalculateSolvency($context): bool
    {
        $real_estate_price = $context['realEstatePrice'] ?? null;
        $real_estate_duration = $context['REMLDuration'] ?? null;

        Log::info('Mortgage Solvency Apply Phase', [
            'price' => $real_estate_price, 'duration' => $real_estate_duration,
        ]);

        return !is_null($real_estate_price) && !is_null($real_estate_duration);
    }

    public function calculateSolvency($credit_amount, $context)
    {
        $salary_free_part_percentage = $this->getSalaryFreePartPercentage($credit_amount, $context['realEstatePrice']);

        $calculation = ($context['netSalary'] * $salary_free_part_percentage) - $context['acraLoansMonthlyRepayments'];
        if (is_null($calculation) || $calculation < 0) {
            return 0;
        }

        return $calculation;
    }

    protected function getSalaryFreePartPercentage($credit_amount, $real_estate_price)
    {
        if ($real_estate_price > constants('REML_SOLVENCY_BOUNDARY_VALUES.REAL_ESTATE_PRICE') ||
            $credit_amount > constants('REML_SOLVENCY_BOUNDARY_VALUES.MORTGAGE_AMOUNT')
        ) {
            return constants('REML_SOLVENCY_BOUNDARY_VALUES.NET_SALARY_PERCENTAGE_60');
        }

        return constants('REML_SOLVENCY_BOUNDARY_VALUES.NET_SALARY_PERCENTAGE_45');
    }
}
