<?php

namespace App\RuleEngine\Phases;

use Log;

abstract class DSTIPhase extends AbstractPhase
{
    protected const DSTI_LIMIT = 0.5;

    abstract protected function getValidCredit($credit, $dsti_repayment, $salary);

    public function __construct($phase)
    {
        $this->phase = $phase;
    }

    public function __invoke(PhasePayload $payload)
    {
        if (!$this->applyPhase($payload)) {
            return $payload;
        }

        Log::info('DSTI Phase', ['phase' => $this->phase]);

        $context = $payload->getContext();
        $max_income = max($context['salary'], $context['dstiIncome']);

        $rules = $payload->getRules();
        $result = $payload->getResult();

        // do not continue phase execution if already rejected by another phase
        if ($result['credit']['amount'] == 0) {
            Log::debug('Skipped DSTI phase because of another phase rejection');

            return $payload;
        }

        $dsti_credit = $result['credit'];

        $dsti_credit['amount'] = 0;

        if ($context['dstiRepayment'] != 0 && $max_income != 0) {
            Log::info('DSTI Phase ', ['Repayment' => $context['dstiRepayment'], 'max_income' => $max_income]);

            $dsti_credit = $this->getValidCredit($result['credit'], $context['dstiRepayment'], $max_income);
        }

        $prepared_context = $this->prepareContext($context, ['dstiAmount' => $dsti_credit['amount'] ?? 0]);

        $phase_result = $this->execute($rules[$this->phase], $prepared_context);

        $prepared_result = $this->prepareResult($phase_result);
        $payload->addResult($prepared_result);

        return $payload;
    }

    protected function validateDsti($dsti_repayment, $monthly, $income)
    {
        $total_dsti_repayment = $dsti_repayment + $monthly;
        $dsti = $total_dsti_repayment / $income;

        Log::info('DSTI Phase validate', ['dsti' => $dsti]);

        return $dsti <= self::DSTI_LIMIT;
    }
}
