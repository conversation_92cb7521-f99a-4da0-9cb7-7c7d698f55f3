<?php

namespace App\RuleEngine\Phases;

use Log;

class OSMPhaseBNPL extends OSMPhase
{
    public function __construct()
    {
        parent::__construct(RulePhases::BNPL['OSM']);
        $this->MONTHLY_REPAYMENT_LIMIT = 0;
    }

    protected function getConfigs()
    {
        $loanConfigService = resolve('App\Services\LoanConfigService');

        return $loanConfigService->getConfigs(constants('LOAN_TYPES.BNPL'));
    }

    public function applyPhase($payload)
    {
        $result = $payload->getResult();

        if ($result['credit']['amount'] >= constants('AMOUNT_LIMITS.BNPL')) {
            return true;
        }

        Log::info('OSM Phase BNPL skipped', ['credit' => $result['credit']]);

        return false;
    }

    protected function getValidCredit($credit, $osm)
    {
        $configs = $this->getConfigs();

        $credit['duration'] = $configs['duration'];

        return parent::getValidCredit($credit, $osm);
    }
}
