<?php

namespace App\RuleEngine\Phases;

class RulePhases
{
    const OCL = [
        'AMOUNT_CALCULATION' => 1,
        'DSTI' => 2,
        'OSM' => 3,
        'ACRA_ACTIVE_LOANS' => 4,
        'TOP_UP_OFFER' => 5,
    ];

    const OVL = [
        'ALLOW_CREDIT' => 1,
        'AMOUNT_CALCULATION' => 2,
        'OSM' => 3,
    ];

    const COMMON = [
        'AMOUNT_CALCULATION' => 1,
        'DSTI' => 2,
    ];

    const OIQL = [
        'AMOUNT_CALCULATION' => 1,
        'DSTI' => 2,
    ];

    const OASL = [
        'AMOUNT_CALCULATION' => 1,
        'DSTI' => 2,
    ];

    const OIDL = [
        'AMOUNT_CALCULATION' => 1,
    ];

    const OTCL = [
        'AMOUNT_CALCULATION' => 1,
    ];

    const OFSL = [
        'AMOUNT_CALCULATION' => 1,
    ];

    const PL = [
        'AMOUNT_CALCULATION' => 1,
        'DSTI' => 2,
    ];

    const OEPL = [
        'AMOUNT_CALCULATION' => 1,
    ];

    const VLX = [
        'AMOUNT_CALCULATION' => 1,
        'DSTI' => 2,
        'OSM' => 3,
        'ACRA_ACTIVE_LOANS' => 4,
        'PRODUCT_CATEGORY_RATE' => 5,
        'FINAL_AMOUNT_CALCULATION' => 6,
    ];

    const REML = [
        'ALLOW_CREDIT' => 1,
        'AMOUNT_CALCULATION' => 2,
        'MORTGAGE_SOLVENCY' => 3,
    ];

    const BNPL = [
        'PRIMARY' => 1,
        'AMOUNT_CALCULATION' => 2,
        'DSTI' => 3,
        'OSM' => 4,
        'CALCULATE_CONVERTED_BALANCE' => 5,
    ];

    const OBL = [
        'AMOUNT_CALCULATION' => 1,
        'DSTI' => 2,
    ];
}
