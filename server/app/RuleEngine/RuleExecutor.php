<?php

namespace App\RuleEngine;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\StringCalc\StringCalc;
use Exception;
use function Functional\map;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\JsonLogic;

class RuleExecutor implements IRuleExecutor
{
    public function __construct($context, $rules)
    {
        $this->context['input'] = $context;

        // Assign default values to non mandatory outputs
        $this->rules = map($rules, function ($r) {
            $r->id = $r->id ?? null;
            $r->rate = $r->rate ?? null;
            $r->amount = $r->amount ?? null;
            $r->duration = $r->duration ?? null;
            $r->service_fee_rate = $r->service_fee_rate ?? null;
            $r->label = $r->label ?? null;

            return $r;
        });

        $this->string_calc = $this->calculator();
    }

    private function calculator()
    {
        $string_calc = new StringCalc();

        $container = $string_calc->getContainer();
        $stringHelper = $container->get('stringcalc_stringhelper');

        $symbol = new IfGTEFunction($stringHelper);
        $string_calc->addSymbol($symbol);

        $symbol = new IfLTFunction($stringHelper);
        $string_calc->addSymbol($symbol);

        $symbol = new IfGTFunction($stringHelper);
        $string_calc->addSymbol($symbol);

        return $string_calc;
    }

    /**
     * Run rule engine.
     *
     * We follow 2 step process
     * In first step applies rules on the context yielding an output with non resolved values
     * In second step actually resolve the values
     *
     * @return $context
     */
    public function execute()
    {
        $context = $this->context;
        // In this step getting 'amount' and 'duration' pairs which satisfies the rule condition
        $context['output'] = $this->createOutput($context['input']);
        // Then count the value of 'amount' attributes if it contains variables and return minimum
        $context['resolved'] = $this->resolveOutput($context['output']);

        return $context['resolved'];
    }

    /**
     * Resolve output.
     *
     * Returns resolved values
     *
     * @param $output - object which contains non resolved values
     *
     * @return associative array
     */
    public function resolveOutput($output)
    {
        $resolved = [];
        foreach ($output as $name => $item) {
            // Resolvable attribute defines a visibility of rule in final data
            if ($item['resolvable']) {
                // Recursively resolve all nested variables
                $resolved = array_merge_recursive($resolved, $this->resolveRecursive($output, $name));
                /* TODO: Handle circular references if such may occure */
            }
        }

        return $resolved;
    }

    /**
     * Recursively resolve output.
     *
     * @param $output - object which contains non resolved values
     * @param $name - key of output current value
     *
     * @return associative array
     */
    protected function resolveRecursive($output, $name)
    {
        foreach (['amount', 'duration', 'rate', 'service_fee_rate'] as $key) {
            $item = $this->resolveVariableRecursive($output, $name, $key);

            if (!$item) {
                return [];
            }
        }

        return [$name => $item];
    }

    /**
     * Resolve each key.
     *
     * @param $output - object which contains non resolved values
     * @param $name - key of output current value
     * @param $key - what should be resolved
     *
     * @return associative array
     */
    protected function resolveVariableRecursive(&$output, $name, $key)
    {
        $item = $output[$name];

        $value = $item[$key];
        // Checks if given string starts with '$' sign and contains only alphanumeric characters
        $alphanumeric = '/\$[A-Za-z0-9]+/';
        // Checks if 'amount' attribute contains variables, to replace them with values
        $is_variable = preg_match_all($alphanumeric, $value, $matches);

        if (!$is_variable) {
            return $item;
        }

        // Replace all variables with according values
        foreach ($matches[0] as $match) {
            // Removing '$' sign from variable to check if 'output' contains attribute with that name
            $variable_name = substr($match, 1);
            if (isset($output[$variable_name])) {
                $pair = $this->resolveRecursive($output, $variable_name);
                $item[$key] = str_replace($match, $pair[$variable_name][$key], $item[$key]);
            }
        }

        // Evaluate the expression
        try {
            $item[$key] = $this->evaluateExpression($item[$key]);
            $output[$name] = $item;

            return $item;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Evaluate given expression.
     *
     * @param $exp - expression to be evaluated
     *
     * @return $amount
     */
    protected function evaluateExpression($exp)
    {
        $amount = $this->string_calc->calculate($exp);

        return $amount;
    }

    /**
     * Create output.
     *
     * Applies rules on the context yielding an output with non resolved values
     *
     * @param $input - citizen data from ACRA, EKENG and NORK
     *
     * @return $output
     */
    public function createOutput($input)
    {
        $output = [];
        // Here we make an assumption that rules with same name are mutually exclusive, meaning that only one of them can be applied on particular context
        foreach ($this->rules as $rule) {
            if (!$rule->disabled) {
                $amount_duration_and_rates = $this->getAmountDurationAndRates($rule, $input);
                if ($amount_duration_and_rates) {
                    $output[$rule->name] = $amount_duration_and_rates;
                }
            }
        }

        return $output;
    }

    /**
     * Get amount and duration.
     *
     * Applies rules on citizen data and returns pair of available 'amount' and 'duration'
     *
     * @param $rule - object which contains 'rule logic', 'amount', 'duration' and 'resolvable' attributes
     * @param $input - citizen data from ACRA, EKENG and NORK
     *
     * @return associative array which contains 'amount', 'duration', 'resolvable'
     */
    protected function getAmountDurationAndRates($rule, $input)
    {
        $result = JsonLogic::apply(json_decode($rule->formula, true), $input);
        if ($result) {
            return
            [
                'amount' => $this->resolveValue($rule->amount, $input),
                'duration' => $this->resolveValue($rule->duration, $input),
                'rate' => $this->resolveValue($rule->rate, $input),
                'service_fee_rate' => $this->resolveValue($rule->service_fee_rate, $input),
                'resolvable' => $rule->resolvable,
                'label' => $rule->label,
                'id' => $rule->id,
            ];
        }

        return null;
    }

    protected function resolveValue($value, $input)
    {
        // Rule amount can be represented as formula, so we need to execute it to get value
        if (gettype(json_decode($value)) == 'object') {
            return JsonLogic::apply(json_decode($value, true), $input);
        }

        return $value;
    }
}
