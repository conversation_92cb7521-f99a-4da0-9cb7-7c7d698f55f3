<?php

namespace App\Models\COVID19;

use App\Models\Loan as CashMeLoan;

// Tmp For-Support-Credit
class Loan extends CashMeLoan
{
    protected $connection = 'pgsql_covid';

    /**
     * Get the citizen that belong to the loan.
     */
    public function citizen()
    {
        return $this->hasOne('App\Models\COVID19\Citizen');
    }

    /**
     * Get loan documents.
     */
    public function documents()
    {
        return $this->hasMany('App\Models\COVID19\LoanDocument');
    }
}
