<?php

namespace App\Models;

use App\Helpers\EnvironmentHelper;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class ReferralCode extends Model
{
    const AGREEMENT = ['name' => 'referral_agreement', 'public' => true];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'hash',
        'ssn',
        'code',
        'path',
        'verified',
        'start_date',
        'end_date',
        'verified_at',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'start_date',
        'end_date',
    ];

    protected $appends = ['expired', 'full_path'];

    public function repayments()
    {
        return $this->hasMany('App\Models\ReferralCodeRepayment');
    }

    public static function getReferralCode($hash)
    {
        return self::where('hash', $hash)->first();
    }

    public function getReferralLinkAttribute()
    {
        return EnvironmentHelper::generateUrl("/home?h=$this->hash");
    }

    public function getExpiredAttribute()
    {
        return $this->end_date !== null && $this->end_date > Carbon::now();
    }

    public function getPathAttribute($value)
    {
        return $value ? env('REFERRAL_CODE_PDFS_SUBDIR', '').'/'.$value : null;
    }

    public function getFullPathAttribute()
    {
        $path = str_replace_array(' ', ['%20'], $this->path);

        return env('AWS_REFERRAL_CODE_PDFS_URL').$path;
    }
}
