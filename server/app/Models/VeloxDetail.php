<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VeloxDetail extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'lender_token',
        'product_name',
        'product_price',
        'product_ratio',
        'product_category_id',
        'product_category_name',
        'product_merchant_name',
        'product_merchant_address',
        'loan_security_id',
    ];

    /**
     * Get the payment's loan.
     */
    public function loan_security()
    {
        return $this->belongsTo('App\Models\LoanSecurity');
    }
}
