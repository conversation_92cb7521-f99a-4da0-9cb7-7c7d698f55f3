<?php

namespace App\Models;

use App\Helpers\NumberHelper;
use Illuminate\Database\Eloquent\Model;

class ArmedDetail extends Model
{
    // armed transaction status
    const APPROVE = 'approve';
    const PENDING = 'pending';
    const DENY = 'deny';
    const EXPIRED = 'expired';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_security_id',
        'hash_ssn',
        'armed_id',
        'amount',
        'phone',
        'expiration',
        'order_id',
        'request_type',
        'request_status',
        'status',
        'mi_tax_payer_id',
        'purchase_order_id',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = ['expiration'];

    protected $attributes = [
        'status' => self::PENDING,
    ];

    public function loan_security()
    {
        return $this->belongsTo('App\Models\LoanSecurity');
    }

    public function purchase_order()
    {
        return $this->belongsTo('App\Models\CreditLine\PurchaseOrder');
    }

    public function scopeDetailsByOrderId($query, $order_id)
    {
        return self::where('order_id', $order_id)->where('expiration', '>', now());
    }

    public function setPhoneAttribute($value)
    {
        $this->attributes['phone'] = NumberHelper::phoneMask($value);
    }
}
