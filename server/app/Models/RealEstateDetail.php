<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RealEstateDetail extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'address',
        'price',
        'region_id',
        'loan_security_id',
        'sreml_mortgage_contract_quantity',
        'sreml_mortgage_contract_notes',
    ];

    protected $appends = ['developer_company'];

    public function loan_security()
    {
        return $this->belongsTo('App\Models\LoanSecurity');
    }

    public function region()
    {
        return $this->belongsTo('App\Models\Region');
    }

    public function real_estate_media()
    {
        return $this->hasMany('App\Models\RealEstateMedia');
    }

    public static function withRegion()
    {
        return RealEstateDetail::with('region');
    }

    public function getDeveloperCompanyAttribute()
    {
        $real_estate = PredefinedRealEstate::whereToken($this->loan_security->re_token)->first();

        return $real_estate ? DeveloperCompany::find($real_estate->developer_company_id) : null;
    }

    public static function findByLoan($loan)
    {
        return $loan->loan_security->real_estate_details;
    }
}
