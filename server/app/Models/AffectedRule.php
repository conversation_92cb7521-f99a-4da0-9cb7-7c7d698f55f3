<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AffectedRule extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'credit_offer_id',
        'rejected',
        'rule_id',
        'amount',
        'duration',
        'rate',
        'service_fee_rate',
        'label',
        'score',
    ];

    public function credit_offer()
    {
        return $this->belongsTo('App\Models\CreditOffer');
    }
}
