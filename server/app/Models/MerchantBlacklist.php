<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class MerchantBlacklist extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_name',
        'expiration',
        'merchant_type'
    ];

    protected $dates = [
        'expiration',
    ];

    public static function isBlacklisted($merchant_name, $merchant_type)
    {
        return self::where('merchant_name', $merchant_name)
            ->where('merchant_type', $merchant_type)
            ->where(function ($q) {
                $q->whereNull('expiration')->orWhere('expiration', '>', Carbon::now());
            })->exists();
    }
}
