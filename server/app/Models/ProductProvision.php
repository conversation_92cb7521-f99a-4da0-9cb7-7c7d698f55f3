<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductProvision extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'paid',
        'withdrawn',
        'withdraw_operator_id',
        'payment_status',
    ];

    protected $dates = [
       'paid',
       'withdrawn',
    ];

    /**
     * Get the payment's loan.
     */
    public function loan()
    {
        return $this->morphToMany(Loan::class, 'paymentable')->withTimestamps();
    }
}
