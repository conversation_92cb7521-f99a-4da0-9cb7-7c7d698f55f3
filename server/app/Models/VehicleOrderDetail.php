<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VehicleOrderDetail extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_application_order_id',
        'police_code',
        'vin',
        'released',
        'import_date',
    ];

    protected $casts = [
        'import_date' => 'date',
    ];

    public function loan_application_order()
    {
        return $this->belongsTo('App\Models\LoanApplicationOrder');
    }

    public function setVinAttribute($value)
    {
        $this->attributes['vin'] = strtoupper($value);
    }
}
