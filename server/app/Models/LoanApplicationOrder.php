<?php

namespace App\Models;

use App\Helpers\NumberHelper;
use Illuminate\Database\Eloquent\Model;

class LoanApplicationOrder extends Model
{
    const APPROVED = 'APPROVED';
    const PENDING = 'PENDING';
    const EXPIRED = 'EXPIRED';
    const REJECTED = 'REJECTED';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_security_id',
        'merchant_id',
        'vendor_id',
        'agent_id',
        'ssn',
        'document_number',
        'amount',
        'phone_number',
        'expiration',
        'order_id',
        'status',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = ['expiration'];

    protected $attributes = [
        'status' => self::PENDING,
    ];

    public function loan_security()
    {
        return $this->belongsTo('App\Models\LoanSecurity');
    }

    public function merchant()
    {
        return $this->belongsTo('App\Models\Merchant');
    }

    public function agent()
    {
        return $this->belongsTo('App\Models\User');
    }

    public function vendor()
    {
        return $this->belongsTo('App\Models\CreditLine\Vendor');
    }

    public function vehicle_order_detail()
    {
        return $this->hasOne('App\Models\VehicleOrderDetail');
    }

    public function setPhoneNumberAttribute($value)
    {
        $this->attributes['phone_number'] = NumberHelper::phoneMask($value);
    }
}
