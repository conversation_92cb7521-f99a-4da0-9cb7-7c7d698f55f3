<?php

namespace App\Models\HC;

use App\Helpers\UnicodeToAnsi;
use Illuminate\Database\Eloquent\Model;

class HcModel extends Model
{
    protected $connection = 'hc_sqlsrv';
    public $timestamps = false;
    protected $primaryKey = null;
    public $incrementing = false;
    protected $dateFormat = 'Y-m-d';

    protected $convertable = [];

    public function setAttribute($key, $value)
    {
        if (in_array($key, $this->convertable)) {
            $value = UnicodeToAnsi::convert($value);
        }

        return parent::setAttribute($key, $value);
    }
}
