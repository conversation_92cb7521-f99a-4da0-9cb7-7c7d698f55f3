<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserActivity extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'device_token',
        'ip',
        'url',
        'meta',
    ];

    public function user()
    {
        return $this->hasOne('App\Models\User');
    }

}
