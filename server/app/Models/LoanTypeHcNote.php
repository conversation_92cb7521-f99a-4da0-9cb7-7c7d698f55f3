<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LoanTypeHcNote extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_type_id',
        'identifier',
        'note2',
        'resolvable',
    ];

    public static function getExactNote2($loan_type_id, $identifier)
    {
        return self::where('loan_type_id', $loan_type_id)
            ->where('identifier', $identifier)
            ->where('resolvable', true)
            ->first()
            ->note2 ?? null;
    }

    public static function getAllNote2AsArray($loan_type_ids)
    {
        // If $loan_type_ids is not an array, make it an array
        if (!is_array($loan_type_ids)) {
            $loan_type_ids = [$loan_type_ids];
        }

        return self::whereIn('loan_type_id', $loan_type_ids)
            ->pluck('note2')
            ->toArray();
    }
}
