<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Model;

class Package extends Model
{
    const PACKAGE_NUMBER_DEVIATION = 16;

    protected $fillable = [
        'name',
        'state'
    ];

    protected $attributes = [
        'state' => SolarPanel::NOT_PRESENTED
    ];

    protected $appends = [
        'package_number',
    ];

    /**
     * Function to get resource url.
     *
     * @return string
     */
    public function getUrl()
    {
        return config('nova.url').'/resources/packages/'.$this->id;
    }

    /**
     * This relation name is written in snake case to be similar as in Arpi Solar Nova Resource uriKey name.
     * This is made because of Nova validation rules generation logic.
     * Otherwise needed validation rules won't be set in request.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function arpi_solar_loans()
    {
        return $this->belongsToMany(Loan::class, 'package_loan');
    }

    public function getPackageNumberAttribute()
    {
        return $this->package_number = $this->id + self::PACKAGE_NUMBER_DEVIATION;
    }

    /**
     * Overwriting delete function to empty model relations, but keep data in table.
     *
     * @return bool|void|null
     */
    public function delete()
    {
        $this->name = 'Empty Package';

        DB::transaction(function () {
            $this->save();
            $this->arpi_solar_loans()->detach();
        });
    }
}
