<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LoanHistory extends Model
{
    protected $appends = ['payment'];

    protected $casts = ['osm' => 'float'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'loan_id',
        'user_id',
        'public_id',
        'loan_type_id',
        'payment_id',
        'payment_type',
        'amount',
        'months',
        'monthly_payment',
        'last_month_payment',
        'total',
        'interest_rate',
        'real_interest_rate',
        'apr',
        'next_payment_date',
        'fico_score',
        'existing_fico_score',
        'status',
        'verified_at',
        'confirmed_at',
        'sign_date',
        'contract_number',
        'credit_code',
        'notification_method',
        'failed_payment',
        'dispute_solution_method',
        'withdrawal_fee',
        'is_offline',
        'service_fee_rate',
        'nominal_rate',
        'payment_status',
        'osm',
        'dsti',
        'top_up_amount',
        'loan_security_id',
        'prev_loan_type_id',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'sign_date',
        'confirmed_at',
        'next_payment_date',
    ];

    public function loan()
    {
        return $this->belongsTo('App\Models\Loan');
    }

    public function loan_security()
    {
        return $this->belongsTo('App\Models\LoanSecurity');
    }

    public function documents_histories()
    {
        return $this->hasMany('App\Models\LoanDocumentHistory');
    }

    // Return current payment
    public function getPaymentAttribute()
    {
        $paymentable_method = $this->paymentableMethod();

        if ($paymentable_method === null) {
            return null;
        }

        return $this->$paymentable_method()->first();
    }

    // Return paymentable method
    // E.g. cardToCardPayment, cashPayment ...
    public function paymentableMethod()
    {
        $paymentable_type = $this->payment_type;

        return $paymentable_type !== null ? Paymentable::PAYMENTABLE_TYPES[$paymentable_type] : null;
    }

    public function cardToCardPayment()
    {
        return CardToCardPayment::whereId($this->payment_id);
    }

    public function idramWalletPayment()
    {
        return IdramWalletPayment::whereId($this->payment_id);
    }

    public function walletLoanPayment()
    {
        return WalletLoanPayment::whereId($this->payment_id);
    }

    public function cashPayment()
    {
        return CashPayment::whereId($this->payment_id);
    }

    public function wirePayment()
    {
        return WirePayment::whereId($this->payment_id);
    }

    public function easypayWalletPayment()
    {
        return EasypayWalletPayment::whereId($this->payment_id);
    }

    public function productProvision()
    {
        return ProductProvision::whereId($this->payment_id);
    }

    public function veloxPayment()
    {
        return VeloxPayment::whereId($this->payment_id);
    }
}
