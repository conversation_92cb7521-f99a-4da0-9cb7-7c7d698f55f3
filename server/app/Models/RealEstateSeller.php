<?php

namespace App\Models;

use App\Helpers\NumberHelper;
use Illuminate\Database\Eloquent\Model;

class RealEstateSeller extends Model
{
    /**
     * @var string[]
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'middle_name',
        'phone_number',
        'birthday',
        'passport_number',
        'given_date',
        'from',
        'registration_address',
        'bank',
        'bank_account',
    ];

    public function real_estate_mortgage()
    {
        return $this->belongsTo('App\Models\RealEstateMortgage');
    }

    public function setPhoneNumberAttribute($value)
    {
        $this->attributes['phone_number'] = NumberHelper::phoneMask($value);
    }
}
