<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RealEstateEvaluationCompany extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'en_name',
        'address',
        'email',
        'phone_number',
        'additional_phone_number',
        'disabled',
    ];

    public static function getEvaluationCompanies()
    {
        return RealEstateEvaluationCompany::where('disabled', false)->pluck('name', 'id');
    }
}
