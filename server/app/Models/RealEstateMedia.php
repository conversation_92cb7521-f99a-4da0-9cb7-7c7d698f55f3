<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RealEstateMedia extends Model
{
    const OWNERSHIP_CERTIFICATE = 'ownership_certificate';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'real_estate_detail_id',
        'path',
        'type',
        'name',
        'document_type',
    ];

    protected $appends = ['full_path'];

    public function real_estate_detail()
    {
        return $this->belongsTo('App\Models\RealEstateDetail');
    }

    public function getPathAttribute($value)
    {
        return env('REAL_ESTATE_MEDIA_SUBDIR', '').$value;
    }

    public function getFullPathAttribute()
    {
        return env('AWS_MORTGAGE_MEDIA_URL').$this->path;
    }

    public static function getMediaByDetailId($real_estate_detail_id, $collection = true)
    {
        $real_estate_media = self::where('real_estate_detail_id', $real_estate_detail_id);

        return $collection ? $real_estate_media->get() : $real_estate_media->first();
    }
}
