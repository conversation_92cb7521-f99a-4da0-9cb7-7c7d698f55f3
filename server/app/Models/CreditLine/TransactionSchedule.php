<?php

namespace App\Models\CreditLine;

use Illuminate\Database\Eloquent\Model;

class TransactionSchedule extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'service_fee',
        'base',
        'payment',
        'total',
        'date',
        'disabled'
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'date',
    ];

    public function transaction()
    {
        return $this->belongsTo('App\Models\CreditLine\Transaction');
    }
}
