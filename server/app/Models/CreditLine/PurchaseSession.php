<?php

namespace App\Models\CreditLine;

use Illuminate\Database\Eloquent\Model;

class PurchaseSession extends Model
{
    const CONFIRMED = 'CONFIRMED';

    const PENDING = 'PENDING';

    const REJECTED = 'REJECTED';

    const INITIAL_CURRENCY = 'AMD';

    protected $fillable = [
        'order_id',
        'payment_id',
        'payment_id_exp',
        'vendor_id',
        'amount',
        'currency',
        'description',
        'callback_url',
        'status',
        'loan_id',
    ];

    protected $attributes = [
        'currency' => self::INITIAL_CURRENCY,
        'status' => self::PENDING,
    ];

    public function vendor()
    {
        return $this->belongsTo('App\Models\CreditLine\Vendor', 'vendor_id');
    }

    public static function getByOrderId($order_id)
    {
        return self::whereOrderId($order_id)->latest()->first();
    }

    public static function getByPaymentId($payment_id)
    {
        return self::wherePaymentId($payment_id)->first();
    }

    public function bnpl_detail()
    {
        return $this->hasOne('App\Models\BnplDetail');
    }

    /**
     * Get the payment's loan.
     */
    public function loan()
    {
        return $this->belongsTo('App\Models\Loan');
    }
}
