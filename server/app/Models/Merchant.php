<?php

namespace App\Models;

use App\Helpers\NumberHelper;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Merchant extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'vendor_id',
        'name',
        'title',
        'address',
        'mi_tax_payer_id',
        'account_number',
        'icon',
        'link',
        'type',
        'state_reg_num',
        'owner',
        'phone_number',
        'additional_phone_number',
    ];

    public function vendor()
    {
        return $this->belongsTo('App\Models\CreditLine\Vendor');
    }

    public function purchase_orders()
    {
        return $this->hasMany('App\Models\CreditLine\PurchaseOrder');
    }

    public function agents()
    {
        return $this->belongsToMany(User::class, 'merchant_agent')->using(MerchantAgent::class)->withTimestamps();
    }

    public function getIconAttribute($value)
    {
        return env('APP_URL', '').env('MERCHANT_MEDIA_DIR', '').$value;
    }

    public function setPhoneNumberAttribute($value)
    {
        $this->attributes['phone_number'] = NumberHelper::phoneMask($value);
    }

    public function setAdditionalPhoneNumberAttribute($value)
    {
        $this->attributes['additional_phone_number'] = $value ? NumberHelper::phoneMask($value) : null;
    }
}
