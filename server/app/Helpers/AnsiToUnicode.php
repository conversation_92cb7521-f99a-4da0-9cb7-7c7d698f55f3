<?php

namespace App\Helpers;

class AnsiToUnicode
{
    protected static $mapping = [
        '²' => 'Ա',
        '³' => 'ա',
        '´' => 'Բ',
        'µ' => 'բ',
        '¶' => 'Գ',
        '·' => 'գ',
        '¸' => 'Դ',
        '¹' => 'դ',
        'º' => 'Ե',
        '»' => 'ե',
        '¼' => 'Զ',
        '½' => 'զ',
        '¾' => 'Է',
        '¿' => 'է',
        'À' => 'Ը',
        'Á' => 'ը',
        'Â' => 'Թ',
        'Ã' => 'թ',
        'Ä' => 'Ժ',
        'Å' => 'ժ',
        'Æ' => 'Ի',
        'Ç' => 'ի',
        'È' => 'Լ',
        'É' => 'լ',
        'Ê' => 'Խ',
        'Ë' => 'խ',
        'Ì' => 'Ծ',
        'Í' => 'ծ',
        'Î' => 'Կ',
        'Ï' => 'կ',
        'Ð' => 'Հ',
        'Ñ' => 'հ',
        'Ò' => 'Ձ',
        'Ó' => 'ձ',
        'Ô' => 'Ղ',
        'Õ' => 'ղ',
        'Ö' => 'Ճ',
        '×' => 'ճ',
        'Ø' => 'Մ',
        'Ù' => 'մ',
        'Ú' => 'Յ',
        'Û' => 'յ',
        'Ü' => 'Ն',
        'Ý' => 'ն',
        'Þ' => 'Շ',
        'ß' => 'շ',
        'à' => 'Ո',
        'á' => 'ո',
        'â' => 'Չ',
        'ã' => 'չ',
        'ä' => 'Պ',
        'å' => 'պ',
        'æ' => 'Ջ',
        'ç' => 'ջ',
        'è' => 'Ռ',
        'é' => 'ռ',
        'ê' => 'Ս',
        'ë' => 'ս',
        'ì' => 'Վ',
        'í' => 'վ',
        'î' => 'Տ',
        'ï' => 'տ',
        'ð' => 'Ր',
        'ñ' => 'ր',
        'ò' => 'Ց',
        'ó' => 'ց',
        'ô' => 'Ւ',
        'õ' => 'ւ',
        'ö' => 'Փ',
        '÷' => 'փ',
        'ø' => 'Ք',
        'ù' => 'ք',
        'ú' => 'Օ',
        'û' => 'օ',
        'ü' => 'Ֆ',
        'ý' => 'ֆ',
        '¨' => 'և',
        '•' => '.',
        '\'' => '՚',
        '°' => '՛',
        '¯' => '՜',
        'ª' => '՝',
        '±' => '՞',
        '£' => '։',
        '' => '֊',
        '§' => '«',
        '¦' => '»',
        '«' => ',',
        '©' => '.',
        '®' => '…',
    ];

    public static function convert($str)
    {
        $res = '';

        for ($i = 0; $i < mb_strlen($str); ++$i) {
            $unicode_letter = mb_substr($str, $i, 1);
            $ansi_letter = self::$mapping[$unicode_letter] ?? null;

            if ($ansi_letter != null) {
                $res .= $ansi_letter;
            } else {
                $res .= $unicode_letter;
            }
        }

        return $res;
    }
}
