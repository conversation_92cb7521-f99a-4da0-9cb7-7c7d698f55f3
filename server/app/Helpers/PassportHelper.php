<?php

namespace App\Helpers;

use App\Exceptions\InvalidDocumentException;
use Carbon\Carbon;
use function Functional\contains;
use function Functional\filter;
use function Functional\first;

class PassportHelper
{
    public static function isIdCard($document_number)
    {
        return preg_match(constants('REGEXES.ID_CARD'), $document_number) && strlen($document_number) == 9;
    }

    public static function isSocialCard($document_number)
    {
        return preg_match(constants('REGEXES.SOC_CARD'), $document_number) && strlen($document_number) == 10;
    }

    public static function isPassport($document_number)
    {
        return preg_match(constants('REGEXES.PASSPORT'), $document_number) && mb_strlen($document_number) == 9;
    }

    public static function determineDocumentType($document_number)
    {
        if (self::isSocialCard($document_number)) {
            return constants('SOC_CARD');
        } elseif (self::isIdCard($document_number)) {
            return constants('ID_CARD');
        } elseif (self::isPassport($document_number)) {
            return constants('PASSPORT');
        } else {
            return constants('UNKNOWN');
        }
    }

    public static function getPassportNumbers($docs)
    {
        $docs = gettype($docs) == 'array' ? $docs : [$docs];

        // Filter out necessary doc types and order them by passports type priority
        return collect($docs)
            ->filter(function ($d) {
                $document_type = $d->DocumentIdentifier->DocumentType ?? '';

                return contains(constants('PASSPORTS.TYPES_BY_ORDER_PRIORITY'), $document_type) &&
                    isset($d->ValidityDate) &&
                    Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $d->ValidityDate)->gt(now());
            })
            ->sortBy(function ($d) {
                return array_search($d->DocumentIdentifier->DocumentType ?? '', constants('PASSPORTS.TYPES_BY_ORDER_PRIORITY'));
            })
            ->values();
    }

    public static function getPassportFromList($docs, $document_number)
    {
        $docs = gettype($docs) == 'array' ? $docs : [$docs];

        // Filter out unecessary docs
        return filter($docs, function ($d) use ($document_number) {
            $document = $d->DocumentIdentifier->DocumentNumber ?? '';

            return $document === $document_number && self::isValidPassport($d);
        });
    }

    public static function getPassports($citizen): array
    {
        $ekeng_passports = $citizen['passport_data']->AVVDocuments->AVVDocument ?? [];

        return gettype($ekeng_passports) == 'array' ? $ekeng_passports : [$ekeng_passports];
    }

    public static function isValidPassport($passport): bool
    {
        return isset($passport->ValidityDate) &&
            Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $passport->ValidityDate)->gt(Carbon::now());
    }

    public static function getEkengPassportNumbersAsArray($passport_data)
    {
        $docs = json_decode(json_encode($passport_data))->AVVDocuments->AVVDocument ?? null;

        $valid_documents = self::getPassportNumbers($docs);

        return $valid_documents->reduce(function ($reduction, $item) {
            $reduction[] = $item->DocumentIdentifier->DocumentNumber;

            return $reduction;
        }, []);
    }

    public static function composeAddress($citizen)
    {
        return "ՀՀ, {$citizen['region']}, {$citizen['city']}, {$citizen['street']}, {$citizen['building']} {$citizen['apartment']}";
    }

    /**
     * @usage compare whether the passport(s) are matched in both, then checking if matched passport(s) is valid(not expired) or not
     *
     * @return mixed
     */
    public static function passportsComparison(string $passport, array $ekeng_passports)
    {
        $found_passport = first($ekeng_passports, function ($ekeng_passport) use ($passport) {
            $ekeng_passport_num = $ekeng_passport->DocumentIdentifier->DocumentNumber ?? '';

            return $ekeng_passport_num === $passport;
        });

        $result = [];
        if (!is_null($found_passport)) {
            $result['matched'][] = $found_passport;

            if (self::isValidPassport($found_passport)) {
                $result['valid'][] = $found_passport;
            }
        }

        return $result;
    }

    public static function extractCitizenFullName($citizen): array
    {
        $ekeng_passports = self::getPassports($citizen);
        $first_passport = reset($ekeng_passports);

        return [
            'first_name' => $first_passport->FirstName ?? '',
            'last_name' => $first_passport->LastName ?? '',
            'middle_name' => $first_passport->MiddleName ?? '',
        ];
    }

    public static function getOrderedPassports($passports, $exclude_types = [])
    {
        $filtered_passports = filter($passports, function ($passport) use ($exclude_types) {
            return !in_array($passport['type'], $exclude_types);
        });

        return collect($filtered_passports)->sortBy(function ($passport) {
            $found_priority = array_search($passport['type'], constants('PASSPORTS.TYPES_BY_ORDER_PRIORITY'));
            // Place unexpected types at the end of order list
            return $found_priority !== false ? $found_priority : PHP_INT_MAX;
        })->values();
    }

    public static function getDocumentNumberBasedOnSSN($ssn)
    {
        $ekeng_service = resolve('App\Interfaces\IEkengService');
        $citizen = $ekeng_service->getCitizen(constants('SOC_CARD'), $ssn);
        $valid_documents = PassportHelper::getPassportNumbers($citizen['passport_data']->AVVDocuments->AVVDocument ?? []);

        if ($valid_documents->isEmpty()) {
            throw new InvalidDocumentException();
        }

        return $valid_documents->first()->DocumentIdentifier->DocumentNumber;
    }

    public static function getBirthDate($ekeng_passport_data)
    {
        $passport_data = json_decode(json_encode($ekeng_passport_data), true);

        if (isset($passport_data['AVVDocuments']['AVVDocument']['BirthDate'])) {
            $birth_date = $passport_data['AVVDocuments']['AVVDocument']['BirthDate'];

            return Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $birth_date)->toDateTimeString();
        }

        if (isset($passport_data['AVVDocuments']['AVVDocument'][0])) {
            $birth_date = $passport_data['AVVDocuments']['AVVDocument'][0]['BirthDate'];

            return Carbon::createFromFormat(constants('EKENG_DATE_FORMAT'), $birth_date)->toDateTimeString();
        }

        return null;
    }
}
