<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\SmsRequest;
use App\Api\V2\Transformers\LoanTransformer;
use App\Exceptions\BlacklistedException;
use App\Exceptions\DoubleLoanException;
use App\Exceptions\DoublePaymentException;
use App\Exceptions\ExpiredLoanApplicationOrderException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidCodeException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\LoanConfirmationInterruptedException;
use App\Exceptions\LoanConfirmationLockException;
use App\Exceptions\Pallaton\InvalidVehicleCheckUpDateException;
use App\Exceptions\PaymentLockedException;
use App\Exceptions\WalletLoansInLastPeriodException;
use App\Http\Controllers\Controller;
use App\Interfaces\ILoanService;
use App\Interfaces\ISecurityService;
use App\Interfaces\ISmsService;
use App\Jobs\SendVerificationSMS;
use App\Traits\LoanConfirmationLock;
use Exception;
use Log;
use Throwable;

class SmsValidationController extends Controller
{
    use LoanConfirmationLock;

    /**
     * Create a new SmsValidationController instance.
     *
     * @return void
     */
    public function __construct(ISmsService $sms_service, ILoanService $loan_service, ISecurityService $security_service)
    {
        $this->sms_service = $sms_service;
        $this->loan_service = $loan_service;
        $this->security_service = $security_service;
    }

    public function getCode()
    {
        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            $has_attempts = $this->security_service->canGetVerificationCode($loan_security->get_verification_code_attempts);

            if (!$has_attempts) {
                return response()->json(false);
            }

            Log::info('Getting verification code', ['suuid' => $loan_security->suuid]);
            $this->dispatch(new SendVerificationSMS($loan_security->suuid));
            Log::info('Verification code response', ['response' => ['sent' => true]]);

            return response()->json(['sent' => $has_attempts]);
        } catch (Exception $e) {
            Log::error('Get verification code, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function expireCode()
    {
        try {
            $loan_security = $this->security_service->resolveLoanSecurity();

            Log::info('Expire verification code', ['suuid' => $loan_security->suuid]);
            $expired = $this->sms_service->expireCode();
            Log::info('Verification code expired', ['response' => ['expired' => $expired]]);

            return response()->json(['expired' => $expired]);
        } catch (Exception $e) {
            Log::error('Expire verification code, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function validateCode(SmsRequest $request)
    {
        $code = $request->get('code');

        try {
            $loan_security = $this->security_service->resolveLoanSecurity();
            $loan = $loan_security->loan;

            // Lock the resource and process loan confirmation
            return $this->withLoanConfirmationLock($loan, function () use ($loan, $code) {
                $this->sms_service->validateCode($loan, $code);

                $this->loan_service->verifyLoan($loan);
                Log::info('Loan verified', ['loan' => $loan]);

                $this->loan_service->processLoanConfirmation($loan);

                return response()->json([
                    'loan' => fractal($loan, new LoanTransformer()),
                ]);
            });
        } catch (InvalidCodeException | InvalidVehicleCheckUpDateException | LoanConfirmationLockException $e) {
            Log::warning('Validate code, '.get_class($e), ['message' => $e->getMessage()]);
            throw $e;
        } catch (DoublePaymentException | DoubleLoanException | PaymentLockedException |
        InvalidSuuidException | BlacklistedException | WalletLoansInLastPeriodException | ExpiredLoanApplicationOrderException $e) {
            Log::warning('Validate code, '.get_class($e), ['message' => $e->getMessage()]);
            // In such exceptional cases, Mobile App should redirect the user, because cannot process the flow anyway
            throw new LoanConfirmationInterruptedException();
        } catch (Throwable $e) {
            Log::error('Validate code, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }
}
