<?php

namespace App\Api\V2\Controllers;

use App\Exceptions\InternalErrorException;
use App\Http\Controllers\Controller;
use Exception;
use Log;

class MarketingController extends Controller
{
    public function getCarouselItems()
    {
        try {
            $marketing_service = resolve('App\Interfaces\Pallaton\IMarketingService');
            $items = $marketing_service->getCarouselItems();
    
            return response()->json($items, 200);
        } catch (Exception $e) {
            Log::error('Get carousel items, Exception', ['message' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getStories()
    {
        try {
            $marketing_service = resolve('App\Interfaces\Pallaton\IMarketingService');
            $items = $marketing_service->getStories();
    
            return response()->json($items, 200);
        } catch (Exception $e) {
            Log::error('Get stories, Exception', ['message' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }
}
