<?php

namespace App\Api\V2\Controllers;

use App\Api\V2\Requests\CallRequest;
use App\Api\V2\Requests\SupportRequest;
use App\Exceptions\InternalErrorException;
use App\Exceptions\Pallaton\CallBackInvalidWorkingHoursException;
use App\Exceptions\Pallaton\SupportInvalidWorkingHoursException;
use App\Http\Controllers\Controller;
use Exception;
use Log;

class SupportController extends Controller
{
    public function requestSupport(SupportRequest $request)
    {
        try {
            $payload = $request->only([
                'name',
                'phone',
                'email',
                'comment',
                'type',
                'ssn',
            ]);
            $support_service = resolve('App\Services\Pallaton\SupportService');
            $support_service->requestSupport($payload);

            return response()->json([
                'success' => true,
            ], 200);
        } catch (SupportInvalidWorkingHoursException $e) {
            Log::warning('Request Support, SupportInvalidWorkingHoursException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Request Support, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function requestCall(CallRequest $request)
    {
        try {
            $payload = $request->only([
                'name',
                'phone',
                'type',
                'ssn',
            ]);
            $support_service = resolve('App\Services\Pallaton\SupportService');
            $support_service->requestCall($payload);

            return response()->json([
                'success' => true,
            ], 200);
        } catch (CallBackInvalidWorkingHoursException $e) {
            Log::warning('Request Call, CallBackInvalidWorkingHoursException', ['message' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Request Call, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }
}
