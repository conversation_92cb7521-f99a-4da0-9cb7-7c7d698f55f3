<?php

namespace App\Api\V2\Transformers;

use App\Models\Loan;
use League\Fractal\TransformerAbstract;

class LoanTransformer extends TransformerAbstract
{
    protected $defaultIncludes = [
        'citizen',
    ];

    public function transform(Loan $loan)
    {
        // For showing a proper msg in case of vehicle trade loan
        $trade_vehicle = [];

        if ($loan->isOVTL()) {
            $trade_vehicle['seller'] = (bool) $loan->vehicle->seller;
            $trade_vehicle['seller_spouse'] = (bool) $loan->vehicle->seller->spouse;
        }

        $arr_loan = $loan->toArray();
        $arr_loan['next_payment_date'] = $loan->next_payment_date ? $loan->next_payment_date->format(constants('SERVER_DATE_TIME_FORMAT')) : null;

        $arr_loan = array_diff_key(
            $arr_loan,
            array_flip([
                'fico_score',
                'credit_code',
                'payment_id',
                'sign_date',
                'user_id',
                'verified_at',
                'vehicle',
            ])
        );

        unset($arr_loan['loan_security']);

        return array_merge($arr_loan, $trade_vehicle);
    }

    public function includeCitizen(Loan $loan)
    {
        $citizen = $loan->citizen->toArray();

        return $this->item($citizen, new CitizenTransformer());
    }
}
