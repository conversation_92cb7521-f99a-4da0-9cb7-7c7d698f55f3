<?php

namespace App\Api\V2\Transformers;

use App\Models\CreditLine\PurchaseOrder;
use League\Fractal\TransformerAbstract;

class PurchaseOrderBNPLTransformer extends TransformerAbstract
{
    public function transform(PurchaseOrder $purchase_order)
    {
        $arr_purchase_order = $purchase_order->toArray();

        return array_diff_key(
            $arr_purchase_order,
            array_flip([
                'id',
                'loan_security_id',
                'merchant_id',
                'agent_id',
                'updated_at',
            ])
        );
    }
}
