<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Requests\ResetPasswordPhoneRequest;
use App\Api\V1\Requests\ResetPasswordEmailRequest;
use App\Api\V1\Requests\TryResetPasswordRequest;
use App\Api\V1\Requests\ResetPasswordAttemptRequest;
use App\Api\V1\Requests\ResetPasswordVerifyRequest;
use App\Exceptions\InvalidCodeException;
use App\Exceptions\ResetPasswordFailedException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\UserNotFoundException;
use App\Helpers\NumberHelper;
use App\Http\Controllers\Controller;
use App\Interfaces\IResetPasswordService;
use App\Models\User;
use Tymon\JWTAuth\JWTAuth;
use Log;

class ResetPasswordController extends Controller
{
    private $reset_password_service;

    public function __construct(IResetPasswordService $reset_password_service)
    {
        $this->reset_password_service = $reset_password_service;
    }

    public function verify(ResetPasswordVerifyRequest $request)
    {
        $uuid = $request->get('uuid');
        $code = $request->get('code');

        try {
            Log::info("Reset password verifying request", ['code' => $code, 'uuid' => $uuid]);

            return $this->reset_password_service->verify($uuid, $code);

        } catch (InvalidCodeException | UserNotFoundException | InternalErrorException $e) {
            Log::info("Reset password verifying exception", ['code' => $code, 'uuid' => $uuid, 'message' => $e->getMessage()]);

            throw $e;
        }
    }

    public function tryResetPassword(TryResetPasswordRequest $request)
    {
        $phone = NumberHelper::phoneMask($request->get('phone_number'));

        try {

            Log::info("Trying Reset password", ['phone_number' => $phone]);

            return $this->reset_password_service->tryResetPassword($phone);
        } catch (UserNotFoundException $e) {
            Log::info("Reset password trying request exception", ['phone' => $phone, 'message' => $e->getMessage()]);

            throw $e;
        }
    }

    public function hasResetPasswordAttempt(ResetPasswordAttemptRequest $request)
    {
        $uuid = $request->get('uuid');

        try {
            Log::info("Has Reset password attempts", ['uuid' => $uuid]);

            $password_reset_attempt = $this->reset_password_service->getPasswordResetAttempt($uuid);

            return array_merge([
                'has_attempt' => $this->reset_password_service->hasAttempt($password_reset_attempt),
                'code_sent' => $this->reset_password_service->codeSent($password_reset_attempt),
                'phone_number' => $password_reset_attempt->phone_number
            ]);

        } catch (InternalErrorException $e) {
            Log::info("Has Reset password exception", ['uuid' => $uuid, 'message' => $e->getMessage()]);

            throw  $e;
        }
    }

    public function getCode(ResetPasswordAttemptRequest $request)
    {
        $uuid = $request->get('uuid');

        try {
            Log::info("Reset password getting code", ['uuid' => $uuid]);

            return $this->reset_password_service->getCode($uuid);
        } catch (InternalErrorException $e) {
            Log::info("Reset password get code exception", ['uuid' => $uuid, 'message' => $e->getMessage()]);

            throw $e;
        }
    }

    public function resetPasswordPhone(ResetPasswordPhoneRequest $request, JWTAuth $JWTAuth)
    {
        $uuid = $request->get('uuid');

        try {
            $password_reset_attempt = $this->reset_password_service->getPasswordResetAttempt($request->get('uuid'));
            $user = $this->reset_password_service->getUser($password_reset_attempt->phone_number);

            $credentials = array_merge($request->only(['password', 'password_confirmation', 'token']), [
                'phone_number' => $user->phone_number
            ]);

            $token = $this->reset_password_service->resetPassword($credentials, $JWTAuth, $user);
            Log::info("Password reset with phone succeed", ['uuid' => $uuid]);

            return response()->json([
                'status' => 'ok',
                'token' => $token,
            ]);
        } catch (ResetPasswordFailedException | InternalErrorException $e) {
            Log::info("Password reset failed", ['uuid' => $uuid, 'message' => $e->getMessage()]);

            throw $e;
        }
    }


    public function resetPasswordEmail(ResetPasswordEmailRequest $request, JWTAuth $JWTAuth)
    {
        $credentials = $request->only(['email', 'password', 'password_confirmation', 'token']);

        try {
            $user = User::where('email', '=', $credentials['email'])->first();

            $token = $this->reset_password_service->resetPassword($credentials, $JWTAuth, $user);

            Log::info("Password reset with email succeed", ['email' => $credentials['email']]);

            return response()->json([
                'status' => 'ok',
                'token' => $token,
            ]);
        } catch (ResetPasswordFailedException $e) {
            Log::info("Password reset failed", ['email' => $credentials['email'], 'message' => $e->getMessage()]);

            throw $e;
        }
    }
}
