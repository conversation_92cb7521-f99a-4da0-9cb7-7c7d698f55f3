<?php

namespace App\Api\V1\Controllers;

use App\Abstracts\AbstractCreditLineController;
use App\Api\V1\Requests\Pay4MeRequest;
use App\Api\V1\Requests\Pay4MeSessionRequest;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidLoanException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\InvalidTokenException;
use App\Models\LoanSecurity;
use App\Services\CreditLine\OBLService;
use App\Services\SecurityUtilityService;
use Exception;
use Log;

class Pay4MeController extends AbstractCreditLineController
{
    protected $pay4me_service;

    public function __construct(OBLService $obl_service)
    {
        parent::__construct($obl_service);

        $this->pay4me_service = resolve('App\Interfaces\IPay4MeService');
    }

    public function createSession(Pay4MeSessionRequest $request)
    {
        try {
            $token = $request->get('token');

            $session = $this->pay4me_service->createSession($token);

            return response()->json($session);
        } catch (InvalidTokenException $e) {
            Log::error('Create session, InvalidTokenException', ['message' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Create session, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function getSession(Pay4MeSessionRequest $request)
    {
        try {
            $token = $request->get('token');

            $loan_security = LoanSecurity::getLoanSecurityByToken($token);

            $is_pay4me = isset($loan_security) && SecurityUtilityService::isOBL($loan_security->loan_type_id);

            return response()->json(['is_pay4me' => $is_pay4me]);
        } catch (InvalidSuuidException $e) {
            Log::error('Get Loan Security, InvalidSuuidException', ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Get Loan Security, Exception', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function checkStatus(Pay4MeRequest $request)
    {
        try {
            $suuid = $request->get('suuid');

            $result = $this->pay4me_service->checkStatus($suuid);

            return response()->json($result);
        } catch (InvalidSuuidException $e) {
            Log::error('Check status, InvalidSuuidException', ['error' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Check status, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }

    public function getDetails(Pay4MeRequest $request)
    {
        try {
            $suuid = $request->get('suuid');

            $details = $this->pay4me_service->getDetails($suuid);

            return response()->json($details);
        } catch (InvalidSuuidException $e) {
            Log::error('Get Detail, InvalidSuuidException', ['message' => $e->getMessage()]);

            throw $e;
        } catch (InvalidLoanException $e) {
            Log::error('Get Detail, InvalidLoanException', ['message' => $e->getMessage()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Get Detail, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);

            throw new InternalErrorException();
        }
    }
}
