<?php

namespace App\Api\V1\Controllers;

use App\Exceptions\ExpiredReferralCodeException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidReferralCodeUrlException;
use App\Exceptions\InvalidReferralCodeException;
use App\Exceptions\ReferralCodeOwnerIsCitizenException;
use App\Http\Controllers\Controller;
use App\Interfaces\IReferralCodeService;
use Dingo\Api\Http\Request;
use Log;
use Exception;

class ReferralCodeController extends Controller
{
    protected $referral_code_service;

    public function __construct(IReferralCodeService $referral_code_service)
    {
        $this->referral_code_service = $referral_code_service;
    }

    public function generateAgreement(Request $request)
    {
        try {
            $hash = $request->get('hash');

            $this->referral_code_service->generateAgreement($hash);
        } catch (InvalidReferralCodeUrlException $e) {
            Log::error('Generate Referral Code Agreement, InvalidReferralCodeUrlException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Generate Referral Code Agreement, Exception', ['message' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function getReferralCode(Request $request)
    {
        try {
            $hash = $request->get('hash');

            $referral_code = $this->referral_code_service->getReferralCode($hash);

            return response()->json($referral_code);
        } catch (Exception $e) {
            Log::error('Get Referral Code, Exception', ['message' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function verifyReferralCode(Request $request)
    {
        try {
            $hash = $request->get('hash');

            $referral_code = $this->referral_code_service->verifyReferralCode($hash);

            return response()->json($referral_code);
        } catch (InvalidReferralCodeUrlException $e) {
            Log::error('Verify Referral Code, InvalidReferralCodeUrlException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (ExpiredReferralCodeException $e) {
            Log::error('Verify Referral Code, ExpiredReferralCodeException', ['error' => $e->getMessage()]);
            throw new InvalidReferralCodeUrlException();
        } catch (Exception $e) {
            Log::error('Verify Referral Code, Exception', ['message' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function setReferralCode(Request $request)
    {
        try {
            $code = $request->get('code');

            $this->referral_code_service->setReferralCode($code);

            return response()->json(['code' => $code]);
        } catch (InvalidReferralCodeException $e) {
            Log::error('Set Referral Code, InvalidReferralCodeException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (ReferralCodeOwnerIsCitizenException $e) {
            Log::error('Set Referral Code, ReferralCodeOwnerIsCitizenException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Set Referral Code, Exception', ['message' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }
}
