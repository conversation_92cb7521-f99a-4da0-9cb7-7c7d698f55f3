<?php

namespace App\Api\V1\Controllers;

use App\Api\V1\Requests\AllowOpenVideoRequest;
use App\Api\V1\Requests\CheckLivenessRequest;
use App\Api\V1\Requests\SmsRequest;
use App\Api\V1\Requests\UnblockCitizenBySsnRequest;
use App\Exceptions\AwsConnectionException;
use App\Exceptions\AwsStoreException;
use App\Exceptions\BlacklistedException;
use App\Exceptions\CheckLivenessException;
use App\Exceptions\CitizenLockedException;
use App\Exceptions\CompareFaceException;
use App\Exceptions\CRM\CitizenNotFoundException;
use App\Exceptions\CRM\CitizenUnblockedException;
use App\Exceptions\IdentificationException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidCodeException;
use App\Exceptions\InvalidSuuidException;
use App\Exceptions\LivenessEyeDistanceException;
use App\Exceptions\LivenessImageQualityException;
use App\Exceptions\LivenessManyFacesException;
use App\Exceptions\LivenessMinRateException;
use App\Exceptions\OpenTokException;
use App\Exceptions\UnmatchedFaceException;
use App\Helpers\NumberHelper;
use App\Http\Controllers\Controller;
use App\Interfaces\ISecurityService;
use App\Jobs\SendIdentityVerificationSMS;
use App\Models\SettingsStatus;
use App\Services\SecurityUtilityService;
use Dingo\Api\Http\Request as DingoRequest;
use Exception;
use Log;

class SecurityController extends Controller
{
    public function sendVerificationSms()
    {
        try {
            $securityService = resolve('App\Interfaces\ISecurityService');

            $loan_security = $securityService->resolveLoanSecurity();

            $has_attempts = $securityService->canGetVerificationCode($loan_security->get_identity_verification_code_attempts);

            $phone_last_2_digits = NumberHelper::getLastDigits($loan_security->phone_number, 2);

            if ($has_attempts) {
                Log::info('Sending identity verification sms', ['suuid' => $loan_security->suuid]);
                $this->dispatch(new SendIdentityVerificationSMS($loan_security->suuid));
                Log::info('Identity verification sms response', ['response' => ['sent' => true]]);
            }

            return response()->json(['sent' => $has_attempts, 'phone_last_digits' => $phone_last_2_digits]);
        } catch (Exception $e) {
            Log::error('Send identity verification sms, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function verifySms(SmsRequest $request)
    {
        $code = $request->get('code');

        try {
            /** @var ISecurityService $securityService */
            $securityService = resolve('App\Interfaces\ISecurityService');

            $verified = $securityService->verifySms($code);

            // Locking user if sms verified
            $securityService->lockCitizen();

            $securityService->recordMetaOnce([], constants('META_STEPS.CREDENTIALS_VERIFIED'), true);

            return response()->json(['verified' => $verified]);
        } catch (InvalidSuuidException $e) {
            Log::error('Verify sms, InvalidSuuidException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (InvalidCodeException $e) {
            Log::error('Verify sms, InvalidCodeException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (IdentificationException $e) {
            Log::error('Verify sms, IdentificationException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (CitizenLockedException $e) {
            Log::error('Verify sms, CitizenLockedException', ['error' => $e->getTraceAsString()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Verify sms, Exception', ['error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }
    }

    public function allowOpenVideo(AllowOpenVideoRequest $request)
    {
        $fingerprint = $request->get('fingerprint');

        $securityService = resolve('App\Interfaces\ISecurityService');
        $loan_security = $securityService->resolveLoanSecurity();

        $settings_service = resolve('App\Interfaces\ISettingsService');

        // turn on offline in case of OASL and staging environment
        $is_offline = $loan_security->is_offline || $settings_service->isGeneralSettingTypeDisabled(SettingsStatus::FACE_RECOGNITION);

        if (!$is_offline) {
            $securityService->storeFingerprint($fingerprint);
        }

        return response()->json(['is_offline' => $is_offline]);
    }

    public function detectFace(CheckLivenessRequest $request)
    {
        try {
            $security_service = resolve('App\Interfaces\ISecurityService');
            $loan_security = $security_service->resolveLoanSecurity();

            $settings_service = resolve('App\Interfaces\ISettingsService');

            // turn on offline in case of OASL and staging environment
            $is_offline = $loan_security->is_offline || $settings_service->isGeneralSettingTypeDisabled(SettingsStatus::FACE_RECOGNITION);

            if ($is_offline) {
                return response()->json(['is_detected' => true]);
            }

            /** @var IRecognitionService $liveness_service */
            $liveness_service = resolve('App\Interfaces\IRecognitionService');

            $base64_image = $request->input('citizen_image');
            $binary_image_data = base64_decode($base64_image);

            $is_detected = $liveness_service->detectCitizen($binary_image_data);

            return response()->json(['is_detected' => $is_detected]);
        } catch (InvalidSuuidException $e) {
            Log::error('Check liveness, InvalidSuuidException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (CheckLivenessException $e) {
            Log::error('Check liveness, CheckLivenessException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (LivenessManyFacesException $e) {
            Log::error('Check liveness, LivenessManyFacesException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (LivenessEyeDistanceException $e) {
            Log::error('Check liveness, LivenessEyeDistanceException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (LivenessImageQualityException $e) {
            Log::error('Check liveness, LivenessImageQualityException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (LivenessMinRateException $e) {
            Log::error('Check liveness, LivenessMinRateException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Citizen Face comparison, BlacklistedException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (CompareFaceException $e) {
            Log::error('Citizen Face comparison, CompareFaceException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (UnmatchedFaceException $e) {
            Log::error('Citizen Face comparison, UnmatchedFaceException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (AwsStoreException $e) {
            Log::error('Citizen Face comparison, AwsStoreException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (AwsConnectionException $e) {
            Log::error('Citizen Face comparison, AwsConnectionException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('Check liveness, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function startSession()
    {
        try {
            $stream_recorder_service = resolve('App\Interfaces\IStreamRecorderService');

            $session = $stream_recorder_service->startSession();

            return response()->json($session);
        } catch (OpenTokException $e) {
            Log::error('Start OpenTok session, OpenTokException', ['error' => $e->getMessage()]);
            throw new OpenTokException();
        }
    }

    public function startRecording(DingoRequest $request)
    {
        try {
            $stream_recorder_service = resolve('App\Interfaces\IStreamRecorderService');

            $stream_recorder_service->startRecording($request->get('session_id'));

            return response()->json([
                'start_recording' => true,
            ]);
        } catch (OpenTokException $e) {
            Log::error('Start OpenTok archiving, OpenTokException', ['error' => $e->getMessage()]);
            throw new OpenTokException();
        }
    }

    public function stopRecording(DingoRequest $request, $archive_id)
    {
        try {
            $stream_recorder_service = resolve('App\Interfaces\IStreamRecorderService');

            $stream_recorder_service->stopRecording($archive_id);

            return response()->json([
                'stop_recording' => true,
            ]);
        } catch (OpenTokException $e) {
            Log::error('Stop OpenTok recording, OpenTokException', ['error' => $e->getMessage()]);
            throw new OpenTokException();
        }
    }

    public function unblockCitizenBySsn(UnblockCitizenBySsnRequest $request, $origin)
    {
        try {
            $ssn = $request->input('soc_card_number');
            $loan_type = $request->input('loan_type');

            SecurityUtilityService::unblockCitizenBySsn($ssn, $loan_type, constants('UNBLOCKING_ORIGIN.'."$origin"));

            return response()->json(['success' => true]);
        } catch (CitizenNotFoundException $e) {
            Log::error('UnblockCitizenBySsn, CitizenNotFoundException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (CitizenUnblockedException $e) {
            Log::error('UnblockCitizenBySsn, CitizenUnblockedException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (Exception $e) {
            Log::error('UnblockCitizenBySsn, Exception', ['error' => $e->getMessage()]);
            throw new InternalErrorException();
        }
    }

    public function storeEkengPhoto()
    {
        try {
            $recognition_service = resolve('App\Interfaces\IRecognitionService');

            if (!$recognition_service->hasEkengPhoto()) {
                $recognition_service->handleEkengPhoto();
            }
        } catch (InvalidSuuidException $e) {
            throw $e;
        } catch (BlacklistedException $e) {
            Log::error('Citizen Face comparison, BlacklistedException', ['error' => $e->getMessage()]);

            throw new BlacklistedException();
        } catch (AwsStoreException $e) {
            Log::error('Ekeng photo storage Exception ', ['error' => $e->getTraceAsString()]);

            throw $e;
        } catch (Exception $e) {
            Log::error('storeEkengPhoto, Exception', ['error' => $e->getMessage()]);

            throw new InternalErrorException();
        }

        return response()->json(['success' => true]);
    }
}
