<?php

namespace App\Api\V1\Requests;

use Dingo\Api\Http\FormRequest;

class RealEstateStoreDetailsRequest extends FormRequest
{
    public function rules()
    {
        return [
            'region_id' => 'required|numeric|exists:regions,id',
            'address' => 'required|string',
            'price' => 'required|numeric',
        ];
    }

    public function authorize()
    {
        return true;
    }
}
