<?php

namespace App\Api\V1\Requests;

use Dingo\Api\Http\FormRequest;

class StoreLoanApplicationOrderDetailsRequest extends FormRequest
{
    public function rules()
    {
        return [
            'amount' => 'required|numeric',
            'phone_number' => 'required|arm_phone',
            'document_number' => 'required|document_number',
            'order_id' => 'required',
            'police_code' => 'required|exists:vehicle_models,police_code',
            'vin' => 'required',
            'released' => 'required|numeric',
        ];
    }

    public function authorize()
    {
        return true;
    }
}
