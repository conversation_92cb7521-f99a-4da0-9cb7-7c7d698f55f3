<?php

namespace App\Api\V1\Transformers;

use League\Fractal\TransformerAbstract;

class CitizenConfigTransformer extends TransformerAbstract
{
    public function transform($config)
    {
        $config = array_diff_key(
            $config,
            array_flip([
                'fallback_transfer',
                'night_end',
                'night_start',
                'penalty_base_amount',
            ])
        );

        return $config;
    }
}
