<?php

namespace App\Api\V1\Transformers;

use App\Models\RealEstateDetail;
use League\Fractal\TransformerAbstract;

class RealEstateDetailsTransformer extends TransformerAbstract
{
    public function transform(RealEstateDetail $real_estate_details)
    {
        $real_estate_details->region_name = $real_estate_details->region->name ?? null;

        $arr_details = array_diff_key(
            $real_estate_details->toArray(),
            array_flip([
                'id',
                'evaluation_company',
                'region',
                'created_at',
                'updated_at',
                'loan_security_id',
            ])
        );

        unset($arr_details['loan_security']);

        return $arr_details;
    }
}
