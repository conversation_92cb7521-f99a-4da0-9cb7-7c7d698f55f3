<?php

namespace App\Exceptions\CreditLine;

use Config;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class TransactionCancelationAmountException extends BadRequestHttpException
{
    public function __construct($message = 'Canceled transaction amount exceeds the transaction amount')
    {
        parent::__construct($message, null, config('error_codes.CREDIT_LINE.PUBLIC.CANCEL_TRANSACTION_AMOUNT'));
    }
}
