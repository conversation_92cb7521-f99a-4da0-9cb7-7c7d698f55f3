<?php

namespace App\Exceptions\CreditLine;

use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;

class DuplicateCredentialException extends NotAcceptableHttpException
{
    public function __construct($message = 'The user with the provided credentials already exists')
    {
        parent::__construct($message, null, config('error_codes.CREDIT_LINE.DUPLICATE_CREDENTIAL'));
    }
}
