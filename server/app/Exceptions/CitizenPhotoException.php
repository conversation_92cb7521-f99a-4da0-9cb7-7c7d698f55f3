<?php

namespace App\Exceptions;

use Config;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CitizenPhotoException extends BadRequestHttpException
{
    public function __construct($verifications = [], $message = 'Face recognition failure.')
    {
        $this->verifications = $verifications;
        parent::__construct($message, null, Config::get('error_codes.NO_EKENG_PHOTO'));
    }

    public function getVerifications()
    {
        return $this->verifications;
    }
}
