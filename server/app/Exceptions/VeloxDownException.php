<?php

namespace App\Exceptions;

use Illuminate\Support\Facades\Config;
use Symfony\Component\HttpKernel\Exception\HttpException;

class VeloxDownException extends HttpException
{
    public function __construct($message = 'The Velox service is currently unavailable, please try again later.', array $headers = [])
    {
        parent::__construct(500, $message, null, $headers, Config::get('error_codes.VLX.DOWN'));
    }
}
