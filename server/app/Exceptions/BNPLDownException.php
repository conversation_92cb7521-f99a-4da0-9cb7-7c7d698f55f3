<?php

namespace App\Exceptions;

use Illuminate\Support\Facades\Config;
use Symfony\Component\HttpKernel\Exception\HttpException;

class BNPLDownException extends HttpException
{
    public function __construct($message = 'The BNPL service is currently unavailable, please try again later.', array $headers = [])
    {
        parent::__construct(500, $message, null, $headers, Config::get('error_codes.CREDIT_LINE.BNPL_DOWN'));
    }
}
