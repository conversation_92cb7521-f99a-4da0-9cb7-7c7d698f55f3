<?php

namespace App\Console\Commands;

use App\Models\LoanSubType;
use App\Models\LoanType;
use Illuminate\Console\Command;

class ManageLoanTypeStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'manage:loan-type-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Via this command we are able to enable and disable loans by type';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $setting_service = resolve('App\Interfaces\ISettingsService');
            $loan_types = $setting_service->getLoanTypes();

            $this->info('Loan types!');
            $this->table(['Name', 'Disabled'], $loan_types);
            $key = $this->ask('Enter the loan type (Name) that you want to interact with.');

            if (!in_array($key, collect($loan_types)->pluck('name')->toArray())) {
                throw new \Exception('Wrong loan ID input');
            }

            $loan_type = LoanType::whereName($key)->first() ?? LoanSubtype::whereName($key)->first();

            $action = $this->ask(str_replace(
                [':DISABLE', ':ENABLE'],
                [constants('LOAN_TYPE_STATUS.DISABLE'), constants('LOAN_TYPE_STATUS.ENABLE')],
                "Enter action. Disable -> ':DISABLE' or Enable -> ':ENABLE'."
            ));

            if (!in_array($action, constants('LOAN_TYPE_STATUS'))) {
                throw new \Exception('Wrong action');
            }

            $action_title = (int) $action === 1 ? 'disable' : 'enable';
            $confirm_message = str_replace(
                [':action_title', ':key'],
                [$action_title, $key],
                'Are you sure you want to :action_title :key.'
            );

            if ($this->confirm($confirm_message)) {
                $setting_service->updateStatus(['name' => $key, 'disabled' => $action], $loan_type);
            }

            $this->info("Success!. You $action_title the $key");
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }
}
