<?php

namespace App\Http;

use App\Http\Middleware\AppDown;
use App\Http\Middleware\BNPLDown;
use App\Http\Middleware\LoanDown;
use App\Http\Middleware\OBLDown;
use App\Http\Middleware\OEPLDown;
use App\Http\Middleware\OFSLDown;
use App\Http\Middleware\OIDLDown;
use App\Http\Middleware\OldMobileVersion;
use App\Http\Middleware\OTCLDown;
use App\Http\Middleware\OUPLDown;
use App\Http\Middleware\VLXDown;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Tymon\JWTAuth\Http\Middleware\RefreshToken;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \App\Http\Middleware\LogRouteMiddleware::class,
        // \App\Http\Middleware\IPAddresses::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            'throttle:60,1',
            'bindings',
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \Illuminate\Auth\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'jwt.check' => \Tymon\JWTAuth\Http\Middleware\Check::class,
        'jwt.auth' => \Tymon\JWTAuth\Http\Middleware\Authenticate::class,
        'jwt.refresh' => RefreshToken::class,
        'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
        'permission' => \Spatie\Permission\Middlewares\PermissionMiddleware::class,

        'response_formatter' => \App\Http\Middleware\ResponseFormatter::class,
        'security_entry' => \App\Http\Middleware\SecurityEntry::class,
        'profile_update' => \App\Http\Middleware\ProfileUpdate::class,
        'security_meta' => \App\Http\Middleware\MetaCollector::class,
        'store_type' => \App\Http\Middleware\StoreLoanType::class,
        'conditional.jwt.auth' => \App\Http\Middleware\ConditionalJwtAuth::class,
        'pl_failure_callback' => \App\Http\Middleware\PlFailureCallback::class,
        'check_availability_internal_bnpl' => \App\Http\Middleware\CheckAvailabilityInternalBNPL::class,
        'check_availability_bnpl' => \App\Http\Middleware\CheckAvailabilityBNPL::class,
        'check_bnpl_status' => \App\Http\Middleware\CheckBNPLStatus::class,
        'check_availability_pl' => \App\Http\Middleware\CheckAvailabilityPL::class,
        'add_suuid_header_pl' => \App\Http\Middleware\AddSuuidHeaderPl::class,
        'check_wallet_response' => \App\Http\Middleware\CheckWalletResponse::class,
        'can_access_route' => \App\Http\Middleware\CanAccessRoute::class,
        'oidl_down' => OIDLDown::class,
        'otcl_down' => OTCLDown::class,
        'oepl_down' => OEPLDown::class,
        'oupl_down' => OUPLDown::class,
        'vlx_down' => VLXDown::class,
        'bnpl_down' => BNPLDown::class,
        'obl_down' => OBLDown::class,
        'app_down' => AppDown::class,
        'ofsl_down' => OFSLDown::class,
        'old_mobile_version' => OldMobileVersion::class,
        'mobile_loan_down' => LoanDown::class,

        'authorize_vendor' => \App\Http\Middleware\AuthorizeVendor::class,
        'activity_log' => \App\Http\Middleware\ActivityLog::class,
        'internal_auth' => \App\Http\Middleware\InternalAuth::class,
        'authorize_bnpl_merchant_agent' => \App\Http\Middleware\AuthorizeBNPLMerchantAgent::class,
        'authorize_armed_vendor' => \App\Http\Middleware\AuthorizeArmedVendor::class,
        'authorize_internal_bnpl_vendor' => \App\Http\Middleware\AuthorizeInternalBNPLVendor::class,
        'authorize_ovil_merchant_agent' => \App\Http\Middleware\AuthorizeOVILMerchantAgent::class,
    ];
}
