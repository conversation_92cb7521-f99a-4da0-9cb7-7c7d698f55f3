<?php

namespace App\Http\Middleware;

use App\Exceptions\UnauthorizedException;
use Closure;
use Illuminate\Support\Facades\Log;

class InternalAuth
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $internal_auth_keys = env('INTERNAL_AUTH_KEYS', '');

        if (!empty($internal_auth_keys)) {
            $internal_auth_keys = explode(',', $internal_auth_keys);

            foreach ($internal_auth_keys as $key_value) {
                list($key, $value) = explode(':', $key_value);

                if ($request->hasHeader($key)) {
                    if ($request->header($key) === $value) {
                        return $next($request);
                    }

                    Log::warning('Internal Auth Middleware, Invalid Internal Auth Key', ['key' => $key]);

                    throw new UnauthorizedException('Invalid Internal Auth Key');
                }
            }
        }

        $payload_hash = $request->input('hash');
        $encoded_data = json_encode($request->except('hash'));

        $generated_hash = hash_hmac('md5', $encoded_data, env('INTERNAL_AUTH_SECRET'));

        if ($payload_hash !== $generated_hash) {
            Log::warning('Internal Auth Middleware, Invalid Internal Auth Hash');

            throw new UnauthorizedException('Invalid Internal Auth Hash');
        }

        return $next($request);
    }
}
