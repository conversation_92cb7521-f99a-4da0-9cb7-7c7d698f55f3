<?php

namespace App\Http\Middleware;

use App\Helpers\Security;
use App\Interfaces\ISecurityService;
use Browser;
use Closure;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class MetaCollector
{
    public function __construct(ISecurityService $security_service)
    {
        $this->security_service = $security_service;
    }

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next, $step, ...$required_steps)
    {
        // Make sure suuid is provided
        if (is_null(Security::getSuuid())) {
            throw new AccessDeniedHttpException('Access denied', null, config('error_codes.INVALID_SUUID'));
        }

        // Make sure suuid didn't expire, citizen wasn't blocked
        $loan_security = $this->security_service->resolveLoanSecurity();
        if (is_null($loan_security)) {
            throw new AccessDeniedHttpException('Access denied', null, config('error_codes.INVALID_SUUID'));
        }

        $has_optional_steps = $this->hasOptionalSteps($required_steps);
        $steps = $has_optional_steps ? $this->extractOptionalSteps($required_steps) : $required_steps;

        // Make sure all required steps where done
        $found_steps = $loan_security
        ->security_metas()
        ->distinct()
        ->whereIn('step', $steps)
        ->where('success', true)
        ->pluck('step');

        $required_steps_count = $has_optional_steps
            ? $found_steps->intersect($steps)->count()
            : count($required_steps);

        // Deny access if step validation fails
        if ($required_steps_count != $found_steps->count()) {
            $this->denyAccess();
        }

        $response = $next($request);

        $status = $response->status();
        $isSuccess = in_array($status, range(200, 399));

        // We should remove 'citizen_image' key from the request(if exists that key) for DB optimization
        $request_data = array_except($request->all(), ['citizen_image']);

        $loan_security->storeMeta([
            'client' => Browser::detect(),
            'request' => [
                'method' => $request->method(),
                'headers' => $request->header(),
                'payload' => $request_data,
                'cookies' => $request->cookie(),
                'ajax' => $request->ajax(),
                'secure' => $request->secure(),
                'url' => $request->fullUrl(),
                'root' => $request->root(),
                'query' => $request->query(),
                'files' => $request->allFiles(),
                'ip' => $request->ip(),
                'server' => $request->server(),
            ],
            'response' => [
                'status' => $status,
                'body' => $response->content(),
            ],
        ], $step, $isSuccess);

        return $response;
    }

    private function extractOptionalSteps($steps)
    {
        $result = [];
        foreach ($steps as $step) {
            // Check if the step is a string starting with '[' and ending with ']'
            if (is_string($step) && preg_match('/^\[.*\]$/', $step)) {
                // Remove the brackets and split by ','
                $step = trim($step, '[]');
                $step_array = array_map('trim', explode(';', $step));
                $result = array_merge($result, $step_array);
            }
        }

        return $result;
    }

    private function hasOptionalSteps($required_steps)
    {
        return collect($required_steps)->contains(function ($step) {
            return is_string($step) && preg_match('/^\[.*\]$/', $step);
        });
    }

    private function denyAccess()
    {
        Log::warning('Meta collector, access denied');
        throw new AccessDeniedHttpException('Access denied', null, config('error_codes.INVALID_SUUID'));
    }
}
