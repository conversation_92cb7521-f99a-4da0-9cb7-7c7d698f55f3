<?php

namespace App\Http\Middleware;

use App\Exceptions\CreditLine\InvalidVendorException;
use App\Models\CreditLine\Vendor;
use Closure;
use Illuminate\Support\Facades\Log;

class AuthorizeArmedVendor
{
    // Allowed Armed request IP
    protected $ips = [
        '***********',
    ];

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!is_dev() && !$this->isValidIp($request->ip())) {
            Log::warning('Not allowed armed IP', ['request_ip' => $request->ip()]);

            throw new InvalidVendorException();
        }

        $this->setHeaders($request);

        return $next($request);
    }

    protected function isValidIp($ip)
    {
        return in_array($ip, $this->ips);
    }

    protected function setHeaders($request)
    {
        $armed_vendor = Vendor::getVendorByType(constants('VENDOR_TYPES.ARMED'));

        $request->headers->set('Client-Identifier', $armed_vendor->client_identifier);
    }
}
