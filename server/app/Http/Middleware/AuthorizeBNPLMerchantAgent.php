<?php

namespace App\Http\Middleware;

use App\Exceptions\InvalidTokenException;
use App\Models\CreditLine\Vendor;
use Closure;
use Illuminate\Support\Facades\Auth;

class AuthorizeBNPLMerchantAgent
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = auth()->user();

        $merchant = $user->merchants()->first();
        // Handling cases when a user does not associated with any merchant,
        //but has a valid user (with a merchant-agent role)
        if (is_null($merchant) || ($merchant->type != constants('MERCHANT_TYPES.HOSPITAL'))) {
            Auth::guard('api')->logout();

            throw new InvalidTokenException();
        }

        $this->setHeaders($request);

        return $next($request);
    }

    protected function setHeaders($request)
    {
        $internal_bnpl_vendor = Vendor::getVendorByType(constants('VENDOR_TYPES.INTERNAL_BNPL'));

        $request->headers->set('Client-Identifier', $internal_bnpl_vendor->client_identifier);
    }
}
