<?php

namespace App\Http\Middleware;

use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidDocumentException;
use App\Helpers\PassportHelper;
use Closure;
use function Functional\first;
use Log;
use Throwable;

class ProfileUpdate
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // This middleware stops the flow in the Dev env, so we need to skip this
        if (is_dev()) {
            return $next($request);
        }

        try {
            Log::debug('Start to check pallaton user documents');

            $ekeng_service = resolve('App\Interfaces\IEkengService');

            $user = auth()->user();
            $current_document_number = $user->profile->document_number;
            $citizen = $ekeng_service->getCitizen(constants('SOC_CARD'), $user->profile->ssn);

            $user_current_document = PassportHelper::getPassportFromList(
                $citizen['passport_data']->AVVDocuments->AVVDocument ?? [],
                $current_document_number
            );

            if (empty($user_current_document)) {
                Log::info('Start to update pallaton user documents', ['current_document_number' => $current_document_number]);

                $valid_documents = PassportHelper::getPassportNumbers(
                    $citizen['passport_data']->AVVDocuments->AVVDocument ?? []
                );

                if ($valid_documents->isEmpty()) {
                    throw new InvalidDocumentException();
                }

                $new_document_number = first($valid_documents)->DocumentIdentifier->DocumentNumber;
                $user->profile()->update([
                    'document_number' => $new_document_number,
                ]);
                $user->load(['profile']);

                Log::info('End to update pallaton user documents', ['new_document_number' => $new_document_number]);
            }

            Log::debug('End to check pallaton user documents');
        } catch (InvalidDocumentException $e) {
            Log::info("Citizen's passport is expired", ['message' => $e->getMessage()]);

            throw $e;
        } catch (Throwable $e) {
            Log::error('Update citizen data, Exception', ['message' => $e->getMessage(), 'error' => $e->getTraceAsString()]);
            throw new InternalErrorException();
        }

        return $next($request);
    }
}
