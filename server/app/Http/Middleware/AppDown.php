<?php

namespace App\Http\Middleware;

use App\Exceptions\AppDownException;
use App\Models\SettingsStatus;
use Closure;

class AppDown
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $settings_service = resolve('App\Interfaces\ISettingsService');
        $app_status = $settings_service->isGeneralSettingTypeDisabled(SettingsStatus::APP);

        if ($app_status) {
            throw new AppDownException();
        }

        return $next($request);
    }
}
