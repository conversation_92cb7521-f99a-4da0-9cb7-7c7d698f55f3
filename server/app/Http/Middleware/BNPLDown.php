<?php

namespace App\Http\Middleware;

use App\Exceptions\BNPLDownException;
use Closure;

class BNPLDown
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $settings_service = resolve('App\Interfaces\ISettingsService');

        if ($settings_service->isLoanTypeDisabled((constants('LOAN_TYPES.BNPL')))) {
            throw new BNPLDownException();
        }

        return $next($request);
    }
}
