<?php

namespace App\Http\Middleware;

use App\Exceptions\OBLDownException;
use Closure;

class OBLDown
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $settings_service = resolve('App\Interfaces\ISettingsService');

        if ($settings_service->isLoanTypeDisabled(constants('LOAN_TYPES.OBL'))) {
            throw new OBLDownException();
        }

        return $next($request);
    }
}
