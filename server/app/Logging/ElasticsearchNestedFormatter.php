<?php

/*
 * This file is part of the Monolog package.
 *
 * (c) <PERSON><PERSON> <j.bog<PERSON><PERSON>@seld.be>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Logging;

use Monolog\Formatter\NormalizerFormatter;

/**
 * Format a log message into an Elasticsearch record.
 *
 * <AUTHOR> <<EMAIL>>
 */
class ElasticsearchNestedFormatter extends NormalizerFormatter
{
    // Elasticsearch requires an ISO 8601 format date with optional millisecond precision.
    const DATE_FORMAT = 'Y-m-d\TH:i:s.uP';

    /**
     * @var string Elasticsearch index name
     */
    protected $index;

    /**
     * @var string Elasticsearch record type
     */
    protected $type;

    /**
     * @param string $index Elasticsearch index name
     * @param string $type  Elasticsearch record type
     */
    public function __construct(string $index, string $type)
    {
        parent::__construct(self::DATE_FORMAT);

        $this->index = $index;
        $this->type = $type;
    }

    /**
     * {@inheritDoc}
     */
    public function format(array $record)
    {
        $record = parent::format($record);

        // stringify each context key individually to avoid mapper_parsing issues
        if (!empty($record['context'])) {
            $content = json_encode($record['context']);

            $record['context'] = [];

            $record['context']['content'] = $content;
        }

        return $this->getDocument($record);
    }

    /**
     * Getter index.
     */
    public function getIndex(): string
    {
        return $this->index;
    }

    /**
     * Getter type.
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Convert a log message into an Elasticsearch record.
     *
     * @param array $record Log message
     */
    protected function getDocument(array $record): array
    {
        $record['_index'] = $this->index;
        $record['_type'] = $this->type;

        return $record;
    }
}
