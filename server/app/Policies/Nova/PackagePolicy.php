<?php

namespace App\Policies\Nova;

use App\Models\Loan;
use App\Models\Package;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PackagePolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function attachLoan(User $user, Package $package, Loan $loan)
    {
        $package_loan_ids = $package->arpi_solar_loans()->pluck('loan_id');

        // Disallow attaching the same loan twice
        if ($package_loan_ids->contains($loan->id)) {
            return false;
        }

        return true;
    }

    public function attachAnyLoan(User $user, Package $package)
    {
        // This method must be disabled for NMC agents
        return !$user->isOASLNMCAgent();
    }

    public function detachLoan(User $user, Package $package)
    {
        // This method must be disabled for NMC agents
        return !$user->isOASLNMCAgent();
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return mixed
     */
    public function view(User $user, Package $package)
    {
        return true;
    }

    /**
     * Determine whether the user can create model.
     *
     * @return mixed
     */
    public function create(User $user)
    {
        // This method must be disabled for NMC agents
        return !$user->isOASLNMCAgent();
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return mixed
     */
    public function update(User $user, Package $package)
    {
        // This method must be disabled for NMC agents
        return !$user->isOASLNMCAgent();
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return mixed
     */
    public function delete(User $user, Package $package)
    {
        // This method must be disabled for NMC agents
        return !$user->isOASLNMCAgent();
    }
}
