<?php

namespace App\Policies\Nova;

use App\Models\LoanDocument;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LoanDocumentPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('view-pdfs', config('nova.guard'));
    }

    /**
     * Determine whether the user can view the loan.
     *
     * @param \App\Loan $loan
     *
     * @return mixed
     */
    public function view(User $user, LoanDocument $loan)
    {
        return false;
    }

    /**
     * Determine whether the user can create loans.
     *
     * @return mixed
     */
    public function create(User $user)
    {
        return false;
    }

    /**
     * Determine whether the user can update the loan.
     *
     * @param \App\Loan $loan
     *
     * @return mixed
     */
    public function update(User $user, LoanDocument $loan)
    {
        return false;
    }

    /**
     * Determine whether the user can delete the loan.
     *
     * @param \App\Loan $loan
     *
     * @return mixed
     */
    public function delete(User $user, LoanDocument $loan)
    {
        return false;
    }
}
