<?php

namespace App\Policies\Nova;

use App\Models\Blacklist;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class BlacklistPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Determine whether the user can view the vehicle model list.
     *
     * @return mixed
     */
    public function view(User $user, Blacklist $blacklist)
    {
        return true;
    }

    /**
     * Determine whether the user can create vehicle model list.
     *
     * @param \App\Models\Blacklist $blacklist
     *
     * @return mixed
     */
    public function create(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can update the vehicle model list.
     *
     * @return mixed
     */
    public function update(User $user, Blacklist $blacklist)
    {
        return true;
    }

    /**
     * Determine whether the user can delete the vehicle model list.
     *
     * @return mixed
     */
    public function delete(User $user, Blacklist $blacklist)
    {
        return false;
    }
}
