<?php

namespace App\Strategies\OvlFlow;

use App\Factory\DocumentServiceFactory;
use App\Factory\HcServiceFactory;
use App\Jobs\GeneratePoliceApplicationDocument;
use App\Jobs\GeneratePowerOfAttorneyDocument;
use App\Jobs\SendAgentAdminNotifySMS;
use App\Jobs\SendAgentNotifySMS;
use App\Jobs\SendConfirmationNotification;
use App\Jobs\SendConfirmationSMS;
use App\Jobs\SendLoanDocuments;
use App\Jobs\SendLoanPartialProcessedMerchantEmail;
use App\Mail\LoanDocumentsMail;
use App\Models\Loan;
use App\Models\Pallaton\UserDevice;
use App\Models\User;
use App\Services\LoanServiceOVL;
use Illuminate\Support\Facades\Log;

abstract class OvlFlowStrategy implements IOvlFlowStrategy
{
    protected $loan = null;

    public function __construct($loan)
    {
        $this->loan = $loan;
    }

    public function notifyTarget()
    {
        // Notify the agent
        SendAgentNotifySMS::dispatch($this->loan);
    }

    public function assignAgent()
    {
        $agent = User::getGCAgent($this->loan);

        if ($agent) {
            $this->loan->agents()->attach($agent);

            GeneratePoliceApplicationDocument::dispatch($this->loan);
            GeneratePowerOfAttorneyDocument::dispatch($this->loan);

            return;
        }

        // Notify Agent Admin to assign agent manually
        SendAgentAdminNotifySMS::dispatch($this->loan, constants('NO_FREE_AGENTS'));
    }

    public function review()
    {
    }

    public function process()
    {
        if ($this->loan->status == Loan::PROCESSED) {
            return;
        }

        $this->loan->update([
            'status' => Loan::PROCESSED,
        ]);
        // In case of OVIL we already sent the docs and Loan stored in HC during the loan flow
        if ($this->loan->isOVIL()) {
            return;
        }

        $loanService = resolve('\App\Services\LoanServiceOVL');
        $loan_document_service = DocumentServiceFactory::build($this->loan->loan_type_id);

        $loan_document_service->exposePrivateDocuments($this->loan);
        SendLoanDocuments::dispatch($this->loan, LoanDocumentsMail::class);

        $loanService->saveLoanToHC($this->loan);
    }

    public function pledge()
    {
        $this->loan->update([
            'status' => Loan::PLEDGED,
        ]);
    }

    public function confirm()
    {
        /** @var LoanServiceOVL $loanService */
        $loanService = resolve('\App\Services\LoanServiceOVL');

        $loanService->processLoanPayment($this->loan);

        SendConfirmationSMS::dispatch($this->loan);
    }

    public function confirmOVIL()
    {
        $hc_service = HcServiceFactory::build($this->loan->loan_type_id);

        $this->loan->update([
            'status' => Loan::CONFIRMED,
        ]);

        $outer_id = $hc_service->getOrCreateClientId($this->loan->loan_security->ssn, $this->loan->loan_type_id);
        $hc_service->storeMortgageDetails($this->loan, $this->loan->contract_number, $outer_id);
    }

    public function processOVIL()
    {
        $loan_service = resolve('\App\Services\LoanServiceOVL');
        $loan_application_order_service = resolve('App\Interfaces\ILoanApplicationOrderService');
        $loan_document_service = DocumentServiceFactory::build($this->loan->loan_type_id);

        $loan_service->processLoanPayment($this->loan, Loan::PARTIAL_PROCESSED);

        $loan_document_service->exposePrivateDocuments($this->loan);

        SendLoanDocuments::dispatch($this->loan, LoanDocumentsMail::class);
        // We Need to notify ovil merchant about the new loan and provide some necessary documents
        SendLoanPartialProcessedMerchantEmail::dispatch($this->loan);

        $loan_application_order_service->approveOrder();
        $loan_service->saveLoanToHC($this->loan);
        $this->sendConfirmationMessage();
    }

    private function sendConfirmationMessage()
    {
        $ssn = $this->loan->loan_security->ssn;
        $user_device = UserDevice::where('user_uuid', $ssn)->first();

        if ($user_device) {
            Log::info('Send confirmation push notification');
            SendConfirmationNotification::dispatch($ssn, $this->loan);
        } else {
            Log::info('Send confirmation SMS');
            SendConfirmationSMS::dispatch($this->loan);
        }
    }
}
