<?php

namespace App\Strategies\Transfer;

use Carbon\Carbon;

class EasypayWalletTransferStrategy extends WalletTransferStrategy
{
    public function __construct($loan, $wallet_id)
    {
        parent::__construct($loan, $wallet_id);
    }

    public function storeTransfer()
    {
        $payment_service = resolve('App\Interfaces\IPaymentService');

        return $payment_service->createEasypayWalletPayment($this->loan, $this->wallet_id);
    }

    public function makeTransfer()
    {
        $easypayService = resolve('App\Interfaces\IEasypayService');

        $ssn = $this->loan->citizen->getSocCard();
        $easypayService->makeTransfer($ssn, $this->loan->amount);

        parent::withdrawTransfer();

        $now = Carbon::now();

        return $this->loan->payment->where('id', $this->loan->payment->id)->update([
            'paid' => $now,
            'withdrawn' => $now,
        ]);
    }
}
