<?php

namespace App\Validators;

use Carbon\Carbon;
use Illuminate\Support\MessageBag;
use Illuminate\Validation\Validator;

class RestValidator extends Validator
{
    /**
     * Add an error message to the validator's collection of messages.
     *
     * @param string $attribute
     * @param string $rule
     * @param array  $parameters
     *
     * @return void
     */
    public function addFailure($attribute, $rule, $parameters = [])
    {
        $message = $this->getMessage($attribute, $rule);
        $message = $this->makeReplacements($message, $attribute, $rule, $parameters);
        $customMessage = new MessageBag();

        $customMessage->merge(['code' => $message]);

        // If validation rule has parameters, include them in response
        // Example: The length of the size rule
        if (!empty($parameters)) {
            $customMessage->merge(['parameters' => $parameters]);
        }
        $this->messages->add($attribute, $customMessage);
    }

    /**
     * Custom validators.
     */
    public function validatePassport($attribute, $value, $parameters)
    {
        return preg_match(constants('REGEXES.PASSPORT'), $value);
    }

    public function validateSocCard($attribute, $value, $parameters)
    {
        return preg_match(constants('REGEXES.SOC_CARD'), $value);
    }

    public function validateDocumentNumber($attribute, $value, $parameters)
    {
        return preg_match(constants('REGEXES.PASSPORT'), $value) || preg_match(constants('REGEXES.ID_CARD'), $value);
    }

    public function validateBankAccount($attribute, $value, $parameters)
    {
        if (
            strlen($value) < constants('BANK_ACOUNT_NUM_MIN_LEN') ||
            strlen($value) > constants('BANK_ACOUNT_NUM_MAX_LEN')
        ) {
            return false;
        }

        $code = substr($value, 0, constants('BANK_CODE_LENGTH'));

        $isValidBankCode = substr($value, 0, 1) == 0 || array_key_exists($code, constants('BANK_CODES'));

        if (!$isValidBankCode) {
            return false;
        }

        $digits = str_split(substr($value, 0, constants('BANK_ACOUNT_NUM_MIN_LEN')));

        $index = 0;

        $calculatedCode = array_reduce($digits, function ($accumulator, $currentValue) use ($digits, &$index) {
            $weight = $index % 2 == 0 ? 2 : 1;

            $digitWithWeight = $currentValue * $weight;

            $digits = str_split($digitWithWeight);

            $index = $index + 1;

            return
                $accumulator +
                array_reduce($digits, function ($sum, $x) {
                    return $sum + (int) ($x);
                }, 0)
            ;
        }, 0);

        return $calculatedCode % 10 == 0;
    }

    public function validateArmPhone($attribute, $value, $parameters)
    {
        $regex = sprintf(
            '/^(([0]{2}|\\+)?374|[0]{1})?(%s)[0-9]{6}$/',
            implode('|', constants('ARM_MOBILE_PHONE_CODES'))
        );

        return preg_match($regex, $value);
    }

    public function validateNotificationMethod($attribute, $value, $parameters)
    {
        return in_array($value, array_keys(constants('NOTIFICATION_METHODS')));
    }

    public function validateYear($attribute, $value, $parameters)
    {
        $now = Carbon::now();
        $year = $now->year;

        return $value >= $year;
    }

    public function validateMonth($attribute, $value, $parameters)
    {
        return in_array($value, constants('NUMERIC_MONTHS'));
    }

    public function validatePassword($attribute, $value, $parameters)
    {
        return preg_match(constants('REGEXES.PASSWORD'), $value);
    }

    public function validatePasswordMobile($attribute, $value, $parameters)
    {
        return preg_match(constants('REGEXES.PASSWORD_MOBILE'), $value);
    }

    public function validateEmailOrArmPhone($attribute, $value, $parameters)
    {
        return $this->validateEmail($attribute, $value, $parameters) || $this->validateArmPhone($attribute, $value, $parameters);
    }

    public function validateVehicleCheckUpDate($attribute, $value, $parameters)
    {
        $now = Carbon::now()->addHour();
        $date = Carbon::parse($value);

        return !$date->isBefore($now);
    }

    public function validateVehicleVin($attribute, $value, $parameters)
    {
        return preg_match(constants('REGEXES.VIN'), $value);
    }
}
