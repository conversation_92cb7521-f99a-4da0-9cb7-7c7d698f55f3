<?php

namespace App\Listeners;

use Log;

class JobFailedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @param object $event
     *
     * @return void
     */
    public function handle($event)
    {
        $alert_service = resolve('App\Services\AlertService');

        Log::info('Job Failed', ['Alert Action' => constants('ALERT.ACTIONS.CASHME_JOB_FAILED'), 'META' => ['job_name' => $event->job->resolveName()]]);

        $alert_service->processAlerting(
            constants('ALERT.ACTIONS.CASHME_JOB_FAILED'),
            null,
            ['job_name' => $event->job->resolveName()]
        );
    }
}
