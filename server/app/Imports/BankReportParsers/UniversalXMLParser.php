<?php

namespace App\Imports\BankReportParsers;

use App\Abstracts\AbstractBankReportParser;
use App\Exceptions\XMLParserException;
use App\Interfaces\IBankReportParserService;
use Log;

class UniversalXMLParser extends AbstractBankReportParser implements IBankReportParserService
{
    private const XML_BANKS_PARSERS = [
        AEBParser::class,
        ArdshinParser::class
    ];

    private function detectParser($rows)
    {
        foreach (self::XML_BANKS_PARSERS as $parser) {
            if (array_key_exists($parser::UNIQUE_KEYWORD, $rows)) {
                return new $parser();
            }
        }
        throw new XMLParserException();
    }

    public function parse($rows)
    {
        $parser = $this->detectParser($rows);
        return $parser->parse($rows);
    }

}
