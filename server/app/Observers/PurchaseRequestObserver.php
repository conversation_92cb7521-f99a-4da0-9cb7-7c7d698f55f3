<?php

namespace App\Observers;

use App\Models\CreditLine\PurchaseRequest;

class PurchaseRequestObserver
{
    /**
     * Handle the credit line purchase request "created" event.
     *
     * @param  \App\Models\CreditLine\PurchaseRequest  $purchase_request
     * @return void
     */
    public function created(PurchaseRequest $purchase_request)
    {
        $purchase_service = resolve('App\Interfaces\CreditLine\IPayLaterService');

        $purchase_service->createPurchaseRequest($purchase_request);
    }

    /**
     * Handle the credit line purchase request "updated" event.
     *
     * @param  \App\Models\CreditLine\PurchaseRequest  $purchase_request
     * @return void
     */
    public function updated(PurchaseRequest $purchase_request)
    {
        //
    }

    /**
     * Handle the credit line purchase request "deleted" event.
     *
     * @param  \App\Models\CreditLine\PurchaseRequest  $purchase_request
     * @return void
     */
    public function deleted(PurchaseRequest $purchase_request)
    {
        //
    }

    /**
     * Handle the credit line purchase request "restored" event.
     *
     * @param  \App\Models\CreditLine\PurchaseRequest  $purchase_request
     * @return void
     */
    public function restored(PurchaseRequest $purchase_request)
    {
        //
    }

    /**
     * Handle the credit line purchase request "force deleted" event.
     *
     * @param  \App\Models\CreditLine\PurchaseRequest  $purchase_request
     * @return void
     */
    public function forceDeleted(PurchaseRequest $purchase_request)
    {
        //
    }
}
