<?php

namespace App\Observers;

use App\Exceptions\HCBlacklistedException;
use App\Factory\HcServiceFactory;
use App\Helpers\PassportHelper;
use App\Models\EkengRequest;
use App\Traits\CheckIsHcBlacklisted;
use Illuminate\Support\Facades\Log;
use Throwable;

class EkengRequestObserver
{
    use CheckIsHcBlacklisted;

    /**
     * Handle the ekeng request "created" event.
     *
     * @return void
     */
    public function created(EkengRequest $ekeng_request)
    {
        try {
            if ($ekeng_request->identifier_type != constants('SOC_CARD')) {
                return;
            }

            $ssn = $ekeng_request->identifier;

            $security_service = resolve('App\Interfaces\ISecurityService');
            $loan_security = $security_service->tryResolveLoanSecurity();
            $hc_service = HcServiceFactory::build($loan_security->loan_type_id ?? null);
            $hc_gc_client = $hc_service->getHcGCClientBySocCard($ssn);

            // Interact with the citizen if already GC client
            if (!$hc_gc_client) {
                return;
            }

            Log::info('Ekeng Request observer, starting HC client upsert', ['ssn' => $ssn]);

            ['citizen' => $citizen, 'ekeng_data' => $ekeng_data] =
                EkengRequest::extractCitizenFromEkengData($ekeng_request);

            $internal_client_service = resolve('App\Interfaces\IInternalClientService');
            if (!$internal_client_service->validateHashableData($citizen, $ekeng_data)) {
                Log::warning('Ekeng Request observer, Hashable data validation not passed', [
                    'ssn' => $ssn,
                    'ekeng_request_id' => $ekeng_request->id ?? null,
                ]);

                return;
            }

            if ($this->isBlacklisted($ssn, $ekeng_data)) {
                throw new HCBlacklistedException();
            }

            return $hc_service->upsertClient($ssn, null, $citizen);
        } catch (Throwable $e) {
            Log::warning('Ekeng Request observer, error upsert HC client',
                [
                    'ssn' => $ssn ?? null,
                    'ekeng_request_id' => $ekeng_request->id ?? null,
                    'message' => $e->getMessage(),
                    'error' => $e->getTraceAsString(),
                ]
            );
        }
    }

    private function isBlacklisted($ssn, $ekeng_data): bool
    {
        $passport_numbers_as_array = PassportHelper::getEkengPassportNumbersAsArray($ekeng_data['passport_data'] ?? []);

        return $this->getCachedHcBlacklistedOrFetch($ssn, $passport_numbers_as_array);
    }

    /**
     * Handle the ekeng request "updated" event.
     *
     * @return void
     */
    public function updated(EkengRequest $ekeng_request)
    {
    }

    public function saved(EkengRequest $ekeng_request)
    {
    }

    /**
     * Handle the ekeng request "deleted" event.
     *
     * @return void
     */
    public function deleted(EkengRequest $ekeng_request)
    {
    }

    /**
     * Handle the ekeng request "restored" event.
     *
     * @return void
     */
    public function restored(EkengRequest $ekeng_request)
    {
    }

    /**
     * Handle the ekeng request "force deleted" event.
     *
     * @return void
     */
    public function forceDeleted(EkengRequest $ekeng_request)
    {
    }
}
